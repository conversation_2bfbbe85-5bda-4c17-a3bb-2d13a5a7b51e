package com.rutong.platform.client.service;

import com.rutong.platform.client.entity.EmployeeProductConfigurationHistory;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysUser;
import org.springframework.stereotype.Service;

@Service
public class EmployeeProductConfigurationHistoryService extends BaseService<EmployeeProductConfigurationHistory> {


    public EmployeeProductConfigurationHistory selectUserByPhone(String phoneNumber) {
        return dao.findByPropertyFirst(EmployeeProductConfigurationHistory.class, EmployeeProductConfigurationHistory.EMPLOYEE_PHONE, phoneNumber);
    }


}
