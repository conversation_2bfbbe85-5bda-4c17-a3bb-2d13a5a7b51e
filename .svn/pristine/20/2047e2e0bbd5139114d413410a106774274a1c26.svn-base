package com.rutong.platform.wechat.entity;

import com.rutong.platform.common.entity.BaseEntity;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 提交的聊天信息实体类
 */
@Entity
@Table(name = "wechat_submitchatinfo")
public class SubmitChatInfo extends BaseEntity {
    
    @Column(name = "owner_dept_id")
    private String ownerDeptId = "0";  // 设置默认值为"0"
    
    /**
     * 配置品类
     */
    @Column(name = "configuration_category", length = 100, nullable = false)
    private String configurationCategory;
    
    /**
     * 产品款号
     */
    @Column(name = "product_model_number", length = 100, nullable = false)
    private String productModelNumber;
    
    /**
     * 状态列表，多个状态用逗号分隔，允许为空
     */
    @Column(name = "status", columnDefinition = "text")
    private String status;
    
    /**
     * 用户姓名
     */
    @Column(name = "user_name", length = 100)
    private String userName;
    
    /**
     * 员工工号
     */
    @Column(name = "employee_id", length = 100)
    private String employeeId;
    
    /**
     * 提交时间
     */
    @Column(name = "submit_time", nullable = false)
    private Date submitTime;
    
    /**
     * 用户IP
     */
    @Column(name = "user_ip", length = 50)
    private String userIp;
    
    /**
     * 处理状态：0-未处理，1-已处理，2-已解决
     */
    @Column(name = "process_status", nullable = false)
    private Integer processStatus;
    
    /**
     * 处理说明
     */
    @Column(name = "process_remark", length = 500)
    private String processRemark;

    // 无参构造函数
    public SubmitChatInfo() {
    }

    // 全参构造函数
    public SubmitChatInfo(String ownerDeptId, String configurationCategory, String productModelNumber,
                         String status, String userName, String employeeId, Date submitTime,
                         String userIp, Integer processStatus, String processRemark) {
        this.ownerDeptId = ownerDeptId;
        this.configurationCategory = configurationCategory;
        this.productModelNumber = productModelNumber;
        this.status = status;
        this.userName = userName;
        this.employeeId = employeeId;
        this.submitTime = submitTime;
        this.userIp = userIp;
        this.processStatus = processStatus;
        this.processRemark = processRemark;
    }

    // Getter 和 Setter 方法
    public String getOwnerDeptId() {
        return ownerDeptId;
    }

    public void setOwnerDeptId(String ownerDeptId) {
        this.ownerDeptId = ownerDeptId;
    }

    public String getConfigurationCategory() {
        return configurationCategory;
    }

    public void setConfigurationCategory(String configurationCategory) {
        this.configurationCategory = configurationCategory;
    }

    public String getProductModelNumber() {
        return productModelNumber;
    }

    public void setProductModelNumber(String productModelNumber) {
        this.productModelNumber = productModelNumber;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public String getUserIp() {
        return userIp;
    }

    public void setUserIp(String userIp) {
        this.userIp = userIp;
    }

    public Integer getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(Integer processStatus) {
        this.processStatus = processStatus;
    }

    public String getProcessRemark() {
        return processRemark;
    }

    public void setProcessRemark(String processRemark) {
        this.processRemark = processRemark;
    }

    /**
     * 设置状态列表
     * @param statusList 状态列表
     */
    public void setStatusList(List<String> statusList) {
        if (statusList == null || statusList.isEmpty()) {
            this.status = null;
            return;
        }
        this.status = String.join(",", statusList);
    }

    /**
     * 获取状态列表
     * @return 状态列表
     */
    public List<String> getStatusList() {
        if (StringUtils.isBlank(this.status)) {
            return new ArrayList<>();
        }
        return Arrays.stream(this.status.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }
} 