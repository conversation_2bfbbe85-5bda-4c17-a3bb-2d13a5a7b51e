package com.rutong.platform.wms.service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.rutong.framework.utils.StringUtils;
import com.rutong.platform.client.service.ClientInfoService;
import com.rutong.platform.washing.entity.WashingTask;
import com.rutong.platform.washing.service.WashingRecordService;
import com.rutong.platform.washing.service.WashingTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.rutong.framework.constant.GlobalContsant;
import com.rutong.framework.utils.BeanUtils;
import com.rutong.framework.utils.ImageUtil;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.ClientInfo;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsDelivery;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import org.springframework.transaction.annotation.Transactional;

@Service
public class WmsDeliveryService extends BaseService<WmsDelivery> {

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    @Autowired
    private WmsDeliveryDetailService deliveryDetailService;
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;
    @Autowired
    private WmsDeliveryService wmsDeliveryService;

    @Autowired
    private WashingTaskService washingTaskService;
    @Autowired
    private ClientInfoService clientInfoService;
    @Autowired
    private WashingRecordService washingRecordService;

    @Transactional
    /**
     * 批量生成洗涤记录
     * @param proConfigurations 配置
     * @param signImg 签名
     * @param type    类型
     * @param orderId 任务单id
     * @param source 源
     */
    public void generateBatch(List<String> proConfigurations, String signImg, String type, String source, String orderId) {

        //通过washingTaskService 根据 orderId 查询对应的 WashingTask 任务信息
        WashingTask washingTask = washingTaskService.findById(orderId);

        // 查询客户信息，以第一个衣服收取计算
        ClientInfo clientInfo = clientInfoService.findById(washingTask.getClientId());

        // 将base64保存为图片并返回地址
        String httpPath = "";
        if (!StringUtils.isEmpty(signImg)) {httpPath = ImageUtil.base64Save(signImg);}

        /*LoginUser loginUser = SecurityUtils.getLoginUser();
        WmsDelivery wmsDelivery = new WmsDelivery();
        wmsDelivery.setTotalNum(proConfigurations.size());//总件数
        wmsDelivery.setClientInfo(clientInfo);//客户信息
        wmsDelivery.setBatchNo(sdf.format(new Date()));//批次号
        wmsDelivery.setRemark(type);//类型，例如脏衣清点机001、客户端（收取）
        wmsDelivery.setAttachment(httpPath);// 附件
        wmsDelivery.setCreateBy(loginUser.getUsername());//创建人
        wmsDelivery.setCreateTime(new Date()); //创建一个表示当前系统时间的 Date 对象
        wmsDelivery.setStatus("已完成");
        wmsDelivery.setSource(source);//源--pda or pc
        wmsDelivery.setContractNumber(clientInfo.getContractNumber());//合同编号
        wmsDelivery.setDeliver(loginUser.getUser().getNickname());//配送人
        wmsDelivery.setOrderId(washingTask.getId());//任务单id
        wmsDelivery.setOrderNumber(washingTask.getTaskOrderNumber());//任务单编号
        wmsDelivery.setHasAbnormality(0L);//是否有异常*/

        //查询是否有相同任务单号和类型的交接记录
        List<WmsDelivery> existingDeliveries = wmsDeliveryService.findByDeliveryIdAndRemark(washingTask.getId(), type);
        WmsDelivery wmsDelivery = new WmsDelivery();
        if (!existingDeliveries.isEmpty()){
            //如果存在相同的任务单号和类型的交接记录
            wmsDelivery = existingDeliveries.get(0);
            //设置总数
            wmsDelivery.setTotalNum(wmsDelivery.getTotalNum() + proConfigurations.size());
            //更新提交的签名图片（如果有）
            if (!StringUtils.isEmpty(httpPath)) {
                wmsDelivery.setAttachment(httpPath);
            }
            //更新修改时间和修改人
            wmsDelivery.setUpdateTime(new Date());
            wmsDelivery.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        }else{
            //如果之前不存在相同的任务单号和类型的交接记录
            wmsDelivery.setTotalNum(proConfigurations.size());//总件数
            wmsDelivery.setClientInfo(clientInfo);//客户信息
            wmsDelivery.setBatchNo(sdf.format(new Date()));//批次号
            wmsDelivery.setRemark(type);//类型，例如脏衣清点机001、客户端（收取）
            wmsDelivery.setAttachment(httpPath);// 附件
            wmsDelivery.setCreateBy(SecurityUtils.getLoginUser().getUsername()); //创建人
            wmsDelivery.setCreateTime(new Date()); //创建一个表示当前系统时间的 Date 对象
            wmsDelivery.setStatus("已完成");
            wmsDelivery.setSource(source);//源--pda or pc
            wmsDelivery.setContractNumber(clientInfo.getContractNumber());//合同编号
            wmsDelivery.setDeliver(SecurityUtils.getLoginUser().getUsername());//配送人
            wmsDelivery.setOrderId(washingTask.getId());//任务单id
            wmsDelivery.setOrderNumber(washingTask.getTaskOrderNumber());//任务单编号
            wmsDelivery.setHasAbnormality(0L);//是否有异常*/
        }


        // 通过washingTaskService 根据 orderId 获取对应的 WmsDelivery
        List<WmsDelivery> byDeliveryIdAndRemark = wmsDeliveryService.findByDeliveryIdAndRemark(washingTask.getId(), GlobalContsant.CLOTHING_WASH_TYPE_7);
        // 若为脏衣清点机首次提交，则需要在统计 sum 时把本次数量也计入
        boolean isFirstType7Submission = type.equals(GlobalContsant.CLOTHING_WASH_TYPE_7) && existingDeliveries.isEmpty();

        // 判断是否进行客户端收取操作
        /*if (byDeliveryIdAndRemark.size() == 0 && !type.equals(GlobalContsant.CLOTHING_WASH_TYPE_2)) {
            throw new ExcelAnalysisException("未进行客户端收取操作！");
        }*/
        //初始化总数为0，用于记录总共的件数
        long sum = 0;
        //遍历byDeliveryIdAndRemark获取到每个WmsDelivery对象，对象名为wmsDeliveryDetail
        for (WmsDelivery wmsDeliveryDetail : byDeliveryIdAndRemark) {
            sum += wmsDeliveryDetail.getTotalNum();
        }
        if (isFirstType7Submission == true) {
            sum += proConfigurations.size();
        }

        // 设置上次收取数量
        if (type.equals(GlobalContsant.CLOTHING_WASH_TYPE_2)) {
            wmsDelivery.setLastNum(0);
            washingTask.setStatus(1L);//状态设置为1
        } else if (type.equals(GlobalContsant.CLOTHING_WASH_TYPE_7)) {
            wmsDelivery.setLastNum(0);
            washingTask.setReceivedQty(sum);
            washingTask.setStatus(1L);
        } else if (type.equals(GlobalContsant.CLOTHING_WASH_TYPE_1)) {
            wmsDelivery.setLastNum((int) sum);
            //washingTask.setStatus(3L);
        } else if (type.equals(GlobalContsant.CLOTHING_WASH_TYPE_3) || type.equals(GlobalContsant.CLOTHING_WASH_TYPE_4)) {
            wmsDelivery.setLastNum((int) sum);
            washingTask.setStatus(2L);
        } else {
            wmsDelivery.setLastNum((int) sum);
            washingTask.setStatus(1L);
        }
        //更新交接记录
        if (existingDeliveries.isEmpty()){
            this.save(wmsDelivery);
        }else {
            this.update(wmsDelivery);
        }

        // 针对“先查后存”问题：
//        if (type.equals(GlobalContsant.CLOTHING_WASH_TYPE_7)) {
//            List<WmsDelivery> afterSaveType7List = wmsDeliveryService.findByDeliveryIdAndRemark(washingTask.getId(), GlobalContsant.CLOTHING_WASH_TYPE_7);
//            long totalType7Count = 0;
//            for (WmsDelivery d : afterSaveType7List) {
//                totalType7Count += d.getTotalNum();
//            }
//            washingTask.setReceivedQty(totalType7Count);
//        }

        //批量生成洗涤明细表
        List<WmsDeliveryDetail> byOrderIdAndRemark = deliveryDetailService.findByOrderIdAndRemark(wmsDelivery.getOrderId(), type);
        List<String> stringList = new ArrayList<>();
        for (WmsDeliveryDetail wmsDeliveryDetail : byOrderIdAndRemark) {
            stringList.add(wmsDeliveryDetail.getRfidTagNumber());
        }
        for (String data : proConfigurations) {
            EmployeeProductConfiguration byRfidAndStatus = employeeProductConfigurationService.byRfid(data);
            if (byRfidAndStatus == null) {
                continue;
            }
            if (stringList.contains(data)) {
                throw new ExcelAnalysisException(byRfidAndStatus.getProductSerialNumber() + "重复提交");
            }
            WmsDeliveryDetail wmsDeliveryDetail = new WmsDeliveryDetail();
            BeanUtils.copyProperties(wmsDeliveryDetail, byRfidAndStatus);
            wmsDeliveryDetail.setBatchNo(wmsDelivery.getBatchNo());
            wmsDeliveryDetail.setType(type);
            wmsDeliveryDetail.setCreateTime(new Date());
            wmsDeliveryDetail.setDelivery(wmsDelivery);
            wmsDeliveryDetail.setCreateBy(SecurityUtils.getLoginUser().getUsername());
            wmsDeliveryDetail.setId(null);
            wmsDeliveryDetail.setOrderId(wmsDelivery.getOrderId());
            wmsDeliveryDetail.setOrderNumber(wmsDelivery.getOrderNumber());
            wmsDeliveryDetail.setWorkWearConfigurationCategory(byRfidAndStatus.getWorkWearConfigurationCategory());
            deliveryDetailService.save(wmsDeliveryDetail);
        }

        //以脏衣清点机数量作为洗涤数量
        if (type.equals(GlobalContsant.CLOTHING_WASH_TYPE_7)) {
            washingRecordService.generateWashingRecordsFromDelivery(wmsDelivery);
        }

        //当存在历史脏衣清点机收取记录时，才进行异常检测
        if (sum != 0) {
            List<WmsDeliveryDetail> wmsDeliveryDetails = washingTaskService.calculationAnomalies(wmsDelivery);
            if (wmsDeliveryDetails.size() > 0) {
                List<WmsDelivery> byDeliveryIdAndRemarkOther = wmsDeliveryService.findByDeliveryIdAndRemark(washingTask.getId(), type);
                for (WmsDelivery delivery : byDeliveryIdAndRemarkOther) {
                    delivery.setHasAbnormality(1L);
                    wmsDeliveryService.update(delivery);
                }
                washingTask.setHasAbnormality(1L);
            } else {
                List<WmsDelivery> byDeliveryIdAndRemarkOther = wmsDeliveryService.findByDeliveryIdAndRemark(washingTask.getId(), type);
                for (WmsDelivery delivery : byDeliveryIdAndRemarkOther) {
                    delivery.setHasAbnormality(0L);
                    wmsDeliveryService.update(delivery);
                }
                List<WmsDelivery> byDeliveryId = wmsDeliveryService.findByDeliveryId(washingTask.getId());
                boolean status = false;
                for (WmsDelivery delivery : byDeliveryId) {
                    if (delivery.getHasAbnormality() == 1L) {
                        status = true;
                    }
                }
                if (!status) {
                    washingTask.setHasAbnormality(0L);
                }
            }
        }

        washingTaskService.update(washingTask);
        //更新交接记录,如果不存在则更新
        if (existingDeliveries.isEmpty()){
            this.update(wmsDelivery);
        }
    }

    public List<WmsDelivery> findByDeliveryId(String delivery_id) {
        String sql = "FROM WmsDelivery WHERE orderId = ?0 ";
        return dao.executeQuery(sql, WmsDelivery.class, delivery_id);
    }

    // 根据订单id和类型查询
    public List<WmsDelivery> findByDeliveryIdAndRemark(String delivery_id, String type) {
        String sql = "FROM WmsDelivery WHERE orderId = ?0 and remark = ?1 ";
        return dao.executeQuery(sql, WmsDelivery.class, delivery_id, type);
    }

    public WmsDelivery findLast(String contractNumber, String type) {
        String sql = "FROM WmsDelivery WHERE contractNumber = ?0 AND remark = ?1 ORDER BY createTime DESC";
        WmsDelivery wmsDelivery = dao.executeQueryFirst(sql, WmsDelivery.class, contractNumber, type);
        return wmsDelivery;
    }

    public List<WmsDelivery> findAllByOwnerUserIdAndSource(String userId, String source) {
        return dao.findByProperty(WmsDelivery.class, "ownerUserId", userId, "source", source);
    }

}
