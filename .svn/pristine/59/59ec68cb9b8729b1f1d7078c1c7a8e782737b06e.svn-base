package com.rutong.platform.sys.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.rutong.platform.common.entity.BaseEntity;

@Entity
@Table(name = "sys_role")
public class SysRole extends BaseEntity {
	/** 角色名称 */
	@Column(nullable = false,unique = true)
	private String rolename;

	/** 数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限） */
	@Column(nullable = false)
	private String datascope;
	
	/**
	 * 拥有数据权限的部门id
	 */
	@Column(length = 1024)
	private String deptids;
	
	/**
	 * 接收菜单数组
	 */
	@Transient
	private String[] menuIds;
	
	public String getRolename() {
		return rolename;
	}

	public void setRolename(String rolename) {
		this.rolename = rolename;
	}

	public String getDatascope() {
		return datascope;
	}

	public void setDatascope(String datascope) {
		this.datascope = datascope;
	}

	public String getDeptids() {
		return deptids;
	}

	public void setDeptids(String deptids) {
		this.deptids = deptids;
	}

	public String[] getMenuIds() {
		return menuIds;
	}

	public void setMenuIds(String[] menuIds) {
		this.menuIds = menuIds;
	}

}
