package com.rutong.platform.wechat.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * 意见反馈数据传输对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FeedbackDTO implements Serializable {

    
    private static final long serialVersionUID = 1L;
    
    /**
     * 反馈内容
     */
    private String content;
    
    /**
     * 反馈类型：常见问题、意见反馈等
     * 暂时不需要
     */
    // private String feedbackType;
    
    /**
     * 联系方式
     */
    private String contactInfo;
    
    /**
     * 提交人Id
     */
    private String openId;
    
    /**
     * 提交人用户信息
     * 暂时不需要
     */
    // private String userName;
} 