package com.rutong.platform.sys.service;

import java.util.List;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.rutong.framework.constant.CacheConstants;
import com.rutong.framework.core.redis.RedisCache;
import com.rutong.framework.utils.StringUtils;
import com.rutong.framework.utils.text.Convert;
import com.rutong.platform.sys.entity.SysConfig;

@Service
public class SysConfigService extends BaseService<SysConfig> {
	@Autowired
	private RedisCache redisCache;

	/**
	 * 项目启动时，初始化参数到缓存
	 */
	@PostConstruct
	public void init() {
		loadingConfigCache();
	}

	/**
	 * 加载参数缓存数据
	 */
	public void loadingConfigCache() {
		List<SysConfig> configsList = dao.findAll(SysConfig.class);
		for (SysConfig config : configsList) {
			redisCache.setCacheObject(getCacheKey(config.getConfigkey()), config.getConfigvalue());
		}
	}

	/**
	 * 获取验证码开关
	 * 
	 * @return true开启，false关闭
	 */
	public boolean selectCaptchaEnabled() {
		String captchaEnabled = selectConfigByKey("sys.account.captchaEnabled");
		if (StringUtils.isEmpty(captchaEnabled)) {
			return true;
		}
		return Convert.toBool(captchaEnabled);
	}

	/**
	 * 根据键名查询参数配置信息
	 * 
	 * @param configKey 参数key
	 * @return 参数键值
	 */
	public String selectConfigByKey(String configKey) {
		String configValue = Convert.toStr(redisCache.getCacheObject(getCacheKey(configKey)));
		if (StringUtils.isNotEmpty(configValue)) {
			return configValue;
		}
		SysConfig retConfig = dao.findByPropertyFirst(SysConfig.class, "configkey", configKey);
		if (StringUtils.isNotNull(retConfig)) {
			redisCache.setCacheObject(getCacheKey(configKey), retConfig.getConfigvalue());
			return retConfig.getConfigvalue();
		}
		return StringUtils.EMPTY;
	}

	/**
	 * 设置cache key
	 * 
	 * @param configKey 参数键
	 * @return 缓存键key
	 */
	private String getCacheKey(String configKey) {
		return CacheConstants.SYS_CONFIG_KEY + configKey;
	}

}
