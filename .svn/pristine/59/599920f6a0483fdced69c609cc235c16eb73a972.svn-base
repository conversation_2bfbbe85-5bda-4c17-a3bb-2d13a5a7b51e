package com.rutong.platform.sys.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.utils.TreeBuilder;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysDept;
import com.rutong.platform.sys.pojo.DeptTree;
import com.rutong.platform.sys.service.SysDeptService;

@RestController
@RequestMapping("/system/dept")
public class SysDeptController extends CrudController<SysDept> {

	@Autowired
	private SysDeptService deptService;

	@Override
	public BaseService<SysDept> getService() {
		return deptService;
	}

	@GetMapping(value = "/getDeptTree.do")
	public ResultBean getDeptTree() {
		List<DeptTree> depts = deptService.getDeptTree();
		return ResultBean.success(TreeBuilder.buildListToTree(depts));
	}

	@Override
	public ResultBean delete(@RequestBody SysDept entity) {
		// 删除前校验
		if (deptService.checkChildren(entity)) {
			return ResultBean.error("请先删除下级部门", 500);
		}
		if (deptService.checkUser(entity)) {
			return ResultBean.error("请先取消用户关联", 500);
		}
		return super.delete(entity);
	}
}
