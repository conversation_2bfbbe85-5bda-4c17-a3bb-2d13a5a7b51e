package com.rutong.platform.washing.controller;

import com.alibaba.excel.EasyExcel;
import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.utils.DataAuthUtil;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.washing.entity.WashingRecord;
import com.rutong.platform.washing.entity.WashingTask;
import com.rutong.platform.washing.excel.WashingRecordExcel;
import com.rutong.platform.washing.service.WashingRecordService;
import com.rutong.platform.washing.service.WashingTaskService;
import com.rutong.platform.wms.entity.WmsDelivery;
import com.rutong.platform.wms.service.WmsDeliveryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 洗涤记录控制器
 */
@RestController
@RequestMapping("/washing/record")
@LogDesc(title = "洗涤记录")
public class WashingRecordController extends CrudController<WashingRecord> {

    private static final Logger log = LoggerFactory.getLogger(WashingRecordController.class);

    @Autowired
    private WashingRecordService washingRecordService;
    
    @Autowired
    private WashingTaskService washingTaskService;
    
    @Autowired
    private WmsDeliveryService wmsDeliveryService;

    @Override
    public BaseService<WashingRecord> getService() {
        return washingRecordService;
    }
    
    /**
     * 自定义查询接口，支持按时间范围查询并按照洗涤时间倒序排序
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 查询结果
     */
    @PostMapping("/findByWashingTime.do")
    public ResultBean findByWashingTime(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        
        List<WashingRecord> result;
        
        if (startTime != null && endTime != null) {
            // 按时间范围查询
            result = washingRecordService.findByWashingTimeBetween(startTime, endTime);
        } else {
            // 查询所有并按时间倒序排序
            result = washingRecordService.findAllOrderByWashingTimeDesc();
        }
        
        return ResultBean.success(result);
    }
    
    /**
     * 高级条件查询接口
     * 根据多个条件查询洗涤记录，支持模糊查询和时间范围
     * 
     * @param clientName 客户名称
     * @param employeeName 员工姓名
     * @param employeePhone 员工电话
     * @param department 部门
     * @param employeeId 员工工号
     * @param rfidTagNumber RFID标签号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param ids 指定ID列表（优先级最高）
     * @return 查询结果
     */
    @PostMapping("/fetchCustomData.do")
    public ResultBean fetchCustomData(
            @RequestParam(required = false) String clientName,
            @RequestParam(required = false) String employeeName,
            @RequestParam(required = false) String employeePhone,
            @RequestParam(required = false) String department,
            @RequestParam(required = false) String employeeId,
            @RequestParam(required = false) String rfidTagNumber,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam(required = false) String ids) {
        
        List<WashingRecord> result = new ArrayList<>();
        
        // 优先根据ID列表查询
        if (StringUtils.isNotBlank(ids)) {
            String[] idArray = ids.split(",");
            for (String id : idArray) {
                WashingRecord record = washingRecordService.findById(id);
                if (record != null) {
                    result.add(record);
                }
            }
        } 
        // 条件查询
        else if (StringUtils.isNotBlank(clientName) || 
                 StringUtils.isNotBlank(employeeName) || 
                 StringUtils.isNotBlank(employeePhone) || 
                 StringUtils.isNotBlank(department) || 
                 StringUtils.isNotBlank(employeeId) || 
                 StringUtils.isNotBlank(rfidTagNumber) || 
                 startTime != null || 
                 endTime != null) {
            
            result = washingRecordService.findByMultipleConditions(
                clientName,
                employeeName,
                employeePhone,
                department,
                employeeId,
                rfidTagNumber,
                startTime,
                endTime
            );
        } 
        // 无条件，返回所有记录（按洗涤时间倒序）
        else {
            result = washingRecordService.findAllOrderByWashingTimeDesc();
        }
        
        return ResultBean.success(result);
    }
    
    /**
     * 重写基础查询方法，确保默认按照洗涤时间倒序排序，并处理查询条件
     */
    @Override
    public PageInfo<WashingRecord> findAllByPage(GridParams gridParams,
                                              @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
                                              @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        if (sorts.isEmpty()) {
            SortCondition sortCondition = new SortCondition();
            sortCondition.setDirection(SortCondition.SORT_DESC);
            sortCondition.setProperty("washingTime");
            sorts.add(sortCondition);
        }

        // 日志输出查询条件，便于调试
        log.info("查询条件数量: {}", filters.size());
        for (FilterCondition filter : filters) {
            log.info("查询条件: property={}, operator={}, value={}",
                    filter.getProperty(), filter.getOperator(), filter.getValue());
        }
        
        // 处理各种查询条件
        for (int i = 0; i < filters.size(); i++) {
            FilterCondition filter = filters.get(i);
            
            // 处理日期类型的查询条件
            if ((filter.getProperty().equals("washingTime") || filter.getProperty().equals("createTime")) 
                    && filter.getValue() instanceof String) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String dateStr = (String) filter.getValue();
                    Date date = sdf.parse(dateStr);
                    filter.setValue(date);
                } catch (ParseException e) {
                    log.error("日期解析错误", e);
                }
            }

            // 客户名称使用精确匹配
            if (filter.getProperty().equals("clientName")) {
                // 如果是模糊查询操作符，改为精确匹配
                if (filter.getOperator().equals(FilterCondition.LIKE)) {
                    filter.setOperator(FilterCondition.EQ);
                    // 如果值包含了%字符(前端使用了模糊匹配格式)，去掉%
                    if (filter.getValue() instanceof String) {
                        String value = (String) filter.getValue();
                        if (value.startsWith("%") && value.endsWith("%")) {
                            value = value.substring(1, value.length() - 1);
                            filter.setValue(value);
                        }
                    }
                }
            }
        }
        
        // 增加数据权限
        DataAuthUtil.setDataAuth(filters);
        
        // 调用服务层查询
        PageInfo<WashingRecord> result = getService().findAllByPage(gridParams, filters, sorts);
        
        // 记录查询结果
        log.info("查询结果: 总记录数={}", result.getTotal());
        
        return result;
    }
    
    /**
     * 从历史客户端收取记录生成洗涤记录
     * 此方法用于补充已有的数据，生成洗涤记录
     * @return 生成结果
     */
    @PostMapping("/generateFromHistory.do")
    public ResultBean generateFromHistory() {
        // 查找所有洗涤任务
        List<WashingTask> allTasks = washingTaskService.findAll();
        int count = 0;
        
        for (WashingTask task : allTasks) {
            // 查找该任务的所有交接记录
            WmsDelivery query = new WmsDelivery();
            query.setOrderId(task.getId());
            List<WmsDelivery> deliveries = wmsDeliveryService.findAllByObject(query, null);
            
            // 对每个交接记录生成洗涤记录
            for (WmsDelivery delivery : deliveries) {
                washingRecordService.generateWashingRecordsFromDelivery(delivery);
                count++;
            }
        }
        
        return ResultBean.success("成功从历史记录生成了" + count + "条洗涤记录");
    }

    /**
     * 导出洗涤记录为Excel
     * 
     * @param clientName 客户名称
     * @param employeeName 员工姓名
     * @param employeePhone 员工电话
     * @param department 部门
     * @param employeeId 员工工号
     * @param rfidTagNumber RFID标签号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param ids 指定ID列表，多个ID用逗号分隔
     */
    @GetMapping("/export.do")
    public void exportWashingRecords(
            HttpServletResponse response,
            @RequestParam(required = false) String clientName,
            @RequestParam(required = false) String employeeName,
            @RequestParam(required = false) String employeePhone,
            @RequestParam(required = false) String department,
            @RequestParam(required = false) String employeeId,
            @RequestParam(required = false) String rfidTagNumber,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam(required = false) String ids) throws Exception {
        
        List<WashingRecord> list = new ArrayList<>();
        
        // 优先根据ID列表查询
        if (StringUtils.isNotBlank(ids)) {
            String[] idArray = ids.split(",");
            for (String id : idArray) {
                WashingRecord record = washingRecordService.findById(id);
                if (record != null) {
                    list.add(record);
                }
            }
        }
        // 条件查询
        else if (StringUtils.isNotBlank(clientName) || 
                StringUtils.isNotBlank(employeeName) || 
                StringUtils.isNotBlank(employeePhone) || 
                StringUtils.isNotBlank(department) || 
                StringUtils.isNotBlank(employeeId) || 
                StringUtils.isNotBlank(rfidTagNumber) || 
                startTime != null || 
                endTime != null) {
            
            list = washingRecordService.findByMultipleConditions(
                clientName,
                employeeName,
                employeePhone,
                department,
                employeeId,
                rfidTagNumber,
                startTime,
                endTime
            );
        } 
        // 无条件，返回所有记录
        else {
            list = washingRecordService.findAllOrderByWashingTimeDesc();
        }
        
        // 转换为Excel导出结构
        List<WashingRecordExcel> excelList = new ArrayList<>();
        int i = 1;
        for (WashingRecord record : list) {
            WashingRecordExcel excel = new WashingRecordExcel();
            excel.setIndex(i++);
            excel.setClientName(record.getClientName());
            excel.setEmployeeName(record.getEmployeeName());
            excel.setWashingTime(record.getWashingTime());
            excel.setEmployeePhone(record.getEmployeePhone());
            excel.setDepartment(record.getDepartment());
            excel.setSubDepartment(record.getSubDepartment());
            excel.setConfigType(record.getConfigType());
            excel.setProductModel(record.getProductModel());
            excel.setProductColor(record.getProductColor());
            excel.setLogoPosition(record.getLogoPosition());
            excel.setProductSpecification(record.getProductSpecification());
            excel.setEmployeeId(record.getEmployeeId());
            excel.setRfidCode(record.getRfidCode());
            excel.setRfidTagNumber(record.getRfidTagNumber());
            excelList.add(excel);
        }
        
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("洗涤记录导出", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), WashingRecordExcel.class)
                .sheet("洗涤记录")
                .doWrite(excelList);
    }
} 