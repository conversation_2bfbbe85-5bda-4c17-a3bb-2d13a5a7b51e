package com.rutong.platform.sys.controller;

import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.Log;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.security.service.TokenService;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.framework.utils.ServerUtil;
import com.rutong.framework.utils.StringUtils;
import com.rutong.framework.utils.file.FileTypeUtils;
import com.rutong.framework.utils.file.MimeTypeUtils;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysFile;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.sys.service.SysFileService;
import com.rutong.platform.sys.service.SysUserService;

@RestController
@RequestMapping("/system/user")
@LogDesc(title = "用户管理")
public class SysUserController extends CrudController<SysUser> {

	@Autowired
	private SysUserService userService;
	@Autowired
	private TokenService tokenService;
	@Autowired
	private SysFileService sysFileService;

	@Override
	public BaseService<SysUser> getService() {
		return userService;
	}

	@Override
	public ResultBean save(SysUser entity) {
		// 生成加密密码
		String encryptPassword = SecurityUtils.encryptPassword(entity.getPassword());
		entity.setPassword(encryptPassword);
		return super.save(entity);
	}

	@GetMapping(value = "/getRoleIdsByUser.do")
	public ResultBean getRoleIdsByUser(String userid) {
		List<String> roleids = userService.getRoleIdsByUser(userid);
		return ResultBean.success(roleids);
	}

	@Log(desc = "重置密码")
	@PostMapping(value = "/resetPwd.do")
	public ResultBean resetPwd(String userid, String password) {
		userService.resetPwd(userid, password);
		return ResultBean.success();
	}

	@Log(desc = "个人中心修改昵称")
	@PostMapping(value = "/profile.do")
	public ResultBean updateProfile(@RequestBody SysUser user) {
		LoginUser loginUser = SecurityUtils.getLoginUser();
		// 只能更新用户昵称
		if (userService.updateUserProfile(loginUser.getUserId(), user.getNickname()) != null) {
			// 更新缓存用户信息
			loginUser.getUser().setNickname(user.getNickname());
			tokenService.setLoginUser(loginUser);
			return ResultBean.success();
		}
		return ResultBean.error("修改个人信息异常，请联系管理员", 500);
	}

	/**
	 * 重置密码
	 */
	@Log(desc = "个人中心重置密码")
	@PostMapping("/profile/updatePwd.do")
	public ResultBean updatePwd(String oldPassword, String newPassword) {
		String username = SecurityUtils.getUsername();
		SysUser user = userService.selectUserByUserName(username);
		String password = user.getPassword();
		if (!SecurityUtils.matchesPassword(oldPassword, password)) {
			return ResultBean.error("修改密码失败，旧密码错误", 500);
		}
		if (SecurityUtils.matchesPassword(newPassword, password)) {
			return ResultBean.error("新密码不能与旧密码相同", 500);
		}
		if (userService.resetUserPwd(user, SecurityUtils.encryptPassword(newPassword)) != null) {
			// 更新缓存用户密码
			LoginUser loginUser = SecurityUtils.getLoginUser();
			loginUser.getUser().setPassword(SecurityUtils.encryptPassword(newPassword));
			tokenService.setLoginUser(loginUser);
			return ResultBean.success();
		}
		return ResultBean.error("修改密码异常，请联系管理员", 500);
	}

	/**
	 * 头像上传
	 */
	@Log(desc = "个人中心上传头像")
	@PostMapping("/profile/avatar.do")
	public ResultBean avatar(@RequestParam("avatarfile") MultipartFile file) {
		if (!file.isEmpty()) {
			LoginUser loginUser = SecurityUtils.getLoginUser();
			String extension = FileTypeUtils.getExtension(file);
			if (!StringUtils.equalsAnyIgnoreCase(extension, MimeTypeUtils.IMAGE_EXTENSION)) {
				return ResultBean.error("文件格式不正确，请上传" + Arrays.toString(MimeTypeUtils.IMAGE_EXTENSION) + "格式",500);
			}
			SysFile fileResult = sysFileService.uploadFile(file);
			if (StringUtils.isNull(fileResult)) {
				return ResultBean.error("文件服务异常，请联系管理员",500);
			}
			String url = ServerUtil.getDomain() + fileResult.getUrl();
			if (userService.updateUserAvatar(loginUser.getUserId(), url) != null) {
				// 更新缓存用户头像
				loginUser.getUser().setAvatar(url);
				tokenService.setLoginUser(loginUser);
				return ResultBean.success(url);
			}
		}
		return ResultBean.error("上传图片异常，请联系管理员",500);
	}
}
