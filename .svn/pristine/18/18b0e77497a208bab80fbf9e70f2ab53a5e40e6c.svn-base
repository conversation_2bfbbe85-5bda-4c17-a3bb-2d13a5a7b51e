package com.rutong.platform.client.service;

import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.platform.client.entity.EmployeeProductConfigurationDetail;
import com.rutong.platform.common.service.BaseService;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 冬装夏装分别配置服务类
 * <AUTHOR>
 */
@Service
public class EmployeeProductConfigurationDetailService extends BaseService<EmployeeProductConfigurationDetail> {

    /**
     * 根据ID删除某条冬夏装详情
     * @param employeeProductConfigurationId 配置子表ID
     */
    public void deleteByEmployeeProductConfigurationId(String employeeProductConfigurationId) {
        List<FilterCondition> filters = new ArrayList<>();
        FilterCondition filter = new FilterCondition();
        filter.setProperty("employeeProductConfigurationId");
        filter.setOperator(FilterCondition.EQ);
        filter.setValue(employeeProductConfigurationId);
        filters.add(filter);
        
        List<EmployeeProductConfigurationDetail> details = findAll(filters, null);
        for (EmployeeProductConfigurationDetail detail : details) {
            delete(detail);
        }
    }

    /**
     * 安全保存详情记录，避免detached entity问题
     * @param detail 详情记录
     */
    public void safeSave(EmployeeProductConfigurationDetail detail) {
        // 设置创建信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        detail.setCreateBy(loginUser.getUsername());
        detail.setCreateTime(new Date());
        detail.setOwnerDeptId(loginUser.getDeptId());
        detail.setOwnerUserId(loginUser.getUserId());
        
        // 直接使用merge方法，这样可以处理新实体和detached实体
        dao.merge(detail);
    }

    /**
     * 根据主表id查询子表
     * @param employeeProductConfigurationId 主表id
     * @return
     */
    public List<EmployeeProductConfigurationDetail> findByEmployeeProductConfigurationId(String employeeProductConfigurationId) {

        List<FilterCondition> filters = new ArrayList<>();
        FilterCondition filter = new FilterCondition();
        filter.setProperty("employeeProductConfigurationId");
        filter.setOperator(FilterCondition.EQ);
        filter.setValue(employeeProductConfigurationId);
        filters.add(filter);

        //按照时间排序
        List<SortCondition> sorts = new ArrayList<>();

        SortCondition sortCondition = new SortCondition();
        sortCondition.setProperty("createTime");
        sortCondition.setDirection(SortCondition.SORT_DESC);//降序
        sorts.add(sortCondition);
        return findAll(filters,sorts);
    }

}