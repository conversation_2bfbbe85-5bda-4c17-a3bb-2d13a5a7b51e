package com.rutong.platform.app.controller;

import java.beans.PropertyEditorSupport;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomCollectionEditor;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSON;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.platform.app.utils.AppConstan;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.device.entity.DevInCabinet;
import com.rutong.platform.device.service.InCabinetService;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.sys.service.SysUserService;
import com.rutong.platform.wms.service.WmsDeliveryService;

/**
 * 客户端接口
 */
@RestController
@RequestMapping(value = "/client")
public class ClientController {

	@Autowired
	private EmployeeProductConfigurationService employeeProductConfigurationService;
	@Autowired
	private SysUserService sysUserService;
	@Autowired
	private WmsDeliveryService wmsDeliveryService;
	@Autowired
	private InCabinetService inCabinetService;
	
	@PostMapping(value = "/loginByPhone.do")
	public ResultBean loginByName(String phone) {
		SysUser user = sysUserService.selectUserByPhone(phone);
		if(user == null) {
			return ResultBean.error("用户不存在", 500);
		}
		return ResultBean.success(user);
	}
	
	
	/**
	 * 根据rifd查询服装
	 * @param rfid
	 * @return
	 */
	@GetMapping("/getClothingByRfid.do")
	public ResultBean getClothingByRfid(String rfid) {
		EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfid(rfid);
		if(employeeProductConfiguration == null) {
			return ResultBean.error("无效标签", 500);
		}
		return ResultBean.success(employeeProductConfiguration);
	}
	
	/**
	 * 根据rifd查询服装及存衣柜信息
	 * @param rfid
	 * @return
	 */
	@GetMapping("/getClothingAndShelfByRfid.do")
	public ResultBean getClothingAndShelfByRfid(String rfid) {
		EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfid(rfid);
		if(employeeProductConfiguration == null) {
			return ResultBean.error("无效标签", 500);
		}
		DevInCabinet cabinet = inCabinetService.findByCabNo(employeeProductConfiguration.getLockerSerialNumber());
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("employeeProduct", employeeProductConfiguration);
		map.put("cabinet", cabinet);
		return ResultBean.success(map);
	}

	@GetMapping("/getShelfByHostSid.do")
	public ResultBean getShelfByHostSid(String host,Integer sid) {
		DevInCabinet cabinet = inCabinetService.findByHostAndSid(host,sid);
		if(cabinet == null) {
			return ResultBean.error();
		}
		if(cabinet.getType().equals(AppConstan.DEVICE_TYPE_NO)) {
			//按列号查询
			List<DevInCabinet> caList = inCabinetService.findByCol(cabinet.getCol());
			return ResultBean.success(caList);
		}
		return ResultBean.success();
	}
	
	/**
	 * 提交盘点到的服装
	 */
	@PostMapping("/postClothingList.do")
	public ResultBean postClothingList(@RequestList(clazz = EmployeeProductConfiguration.class) List<EmployeeProductConfiguration> proConfigurations,String username) {
		//生成一次交接记录
		wmsDeliveryService.generateBatch(proConfigurations,username);
		return ResultBean.success();
		
	}
	
}
