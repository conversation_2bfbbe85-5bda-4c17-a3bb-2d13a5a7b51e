package com.rutong.platform.sys.pojo;

import com.rutong.framework.bean.TreeNode;

/**
 * antd pro的菜单格式
 */
public class MenuDataItem extends TreeNode{

	private String authority;
	private boolean hideChildrenInMenu = false;
	private boolean hideInMenu = false;
	private String icon;
	private String locale;
	private String name;
	private String path;
	
	public String getAuthority() {
		return authority;
	}
	public void setAuthority(String authority) {
		this.authority = authority;
	}
	public boolean isHideChildrenInMenu() {
		return hideChildrenInMenu;
	}
	public void setHideChildrenInMenu(boolean hideChildrenInMenu) {
		this.hideChildrenInMenu = hideChildrenInMenu;
	}
	public boolean isHideInMenu() {
		return hideInMenu;
	}
	public void setHideInMenu(boolean hideInMenu) {
		this.hideInMenu = hideInMenu;
	}
	public String getIcon() {
		return icon;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}
	public String getLocale() {
		return locale;
	}
	public void setLocale(String locale) {
		this.locale = locale;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getPath() {
		return path;
	}
	public void setPath(String path) {
		this.path = path;
	}

}
