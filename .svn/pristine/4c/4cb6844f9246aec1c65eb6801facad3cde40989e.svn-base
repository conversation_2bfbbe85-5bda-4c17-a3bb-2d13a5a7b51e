package com.rutong.platform.sys.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysLogininfor;
import com.rutong.platform.sys.service.SysLogininforService;

@RestController
@RequestMapping(value = "/system/logininfo")
@LogDesc(title = "登录日志")
public class SysLogininfoController extends CrudController<SysLogininfor>{
	
	@Autowired
	private SysLogininforService logininforService;

	@Override
	public BaseService<SysLogininfor> getService() {
		return logininforService;
	}

}
