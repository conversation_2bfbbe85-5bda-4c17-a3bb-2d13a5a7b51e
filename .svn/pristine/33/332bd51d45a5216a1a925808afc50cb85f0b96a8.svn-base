package com.rutong.platform.client.controller;

import com.alibaba.excel.EasyExcel;
import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.excel.LeaseClothingExcel;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.client.util.ExportUtil;
import com.rutong.platform.device.entity.DevInCabinet;
import com.rutong.platform.device.service.InCabinetService;
import com.rutong.platform.wms.entity.WmsClothing;
import com.rutong.platform.wms.service.WmsClothingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 租赁服装控制器
 */
@RestController
@RequestMapping("/client/leaseClothing")
@LogDesc(title = "租赁服装")
public class LeaseClothingController {

    private static final Logger logger = LoggerFactory.getLogger(LeaseClothingController.class);

    @Autowired
    private WmsClothingService wmsClothingService;

    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;
    
    @Autowired
    private InCabinetService inCabinetService;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 导出租赁服装信息
     * @param response HTTP响应对象
     * @param filters 筛选条件列表
     * @param sorts 排序条件列表
     * @throws IOException IO异常
     */
    @PostMapping("/export.do")
    public void exportLeaseClothing(
            HttpServletResponse response,
            @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
            @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) throws IOException {
        
        logger.info("开始导出租赁服装信息");
        
        try {
            // 打印筛选条件
            if (filters != null) {
                for (FilterCondition filter : filters) {
                    logger.info("筛选条件: {} {} {}", filter.getProperty(), filter.getOperator(), filter.getValue());
                }
            }
            
            List<LeaseClothingExcel> excelList = new ArrayList<>();
            
            // 构建动态SQL查询条件
            StringBuilder whereClauses = new StringBuilder(" WHERE 1=1 ");
            List<Object> parameters = new ArrayList<>();
            
            // 处理筛选条件
            if (filters != null && !filters.isEmpty()) {
                for (FilterCondition filter : filters) {
                    String property = filter.getProperty();
                    String operator = filter.getOperator();
                    Object valueObj = filter.getValue();
                    String value = valueObj != null ? valueObj.toString() : null;
                    
                    // 字段映射，前端字段名可能与数据库字段名不一致
                    String dbField = mapPropertyToDbField(property);
                    
                    if (dbField != null && value != null && !value.isEmpty()) {
                        switch (operator) {
                            case FilterCondition.EQ:
                                whereClauses.append(" AND ").append(dbField).append(" = ? ");
                                parameters.add(value);
                                break;
                            case FilterCondition.LIKE:
                                whereClauses.append(" AND ").append(dbField).append(" LIKE ? ");
                                parameters.add("%" + value + "%");
                                break;
                            case FilterCondition.GT:
                                whereClauses.append(" AND ").append(dbField).append(" > ? ");
                                parameters.add(value);
                                break;
                            case FilterCondition.LT:
                                whereClauses.append(" AND ").append(dbField).append(" < ? ");
                                parameters.add(value);
                                break;
                            case FilterCondition.GE:
                                whereClauses.append(" AND ").append(dbField).append(" >= ? ");
                                parameters.add(value);
                                break;
                            case FilterCondition.LE:
                                whereClauses.append(" AND ").append(dbField).append(" <= ? ");
                                parameters.add(value);
                                break;
                            case FilterCondition.BETWEEN:
                                // 处理BETWEEN条件，假设value是JSON数组格式的字符串 ["2025-06-01", "2025-06-30"]
                                if (value.startsWith("[") && value.endsWith("]")) {
                                    String[] values = value.substring(1, value.length() - 1).split(",");
                                    if (values.length == 2) {
                                        String start = values[0].trim().replace("\"", "");
                                        String end = values[1].trim().replace("\"", "");
                                        whereClauses.append(" AND ").append(dbField).append(" BETWEEN ? AND ? ");
                                        parameters.add(start);
                                        parameters.add(end);
                                    }
                                }
                                break;
                            default:
                                // 其他操作符...
                                break;
                        }
                    }
                }
            }
            
            // 使用联表查询一次性获取所有需要的数据
            String sql = "SELECT e.id, e.employee_name, e.employee_phone, e.enterprise_name, " +
                   "e.department, e.sub_department, e.configuration_category, " +
                   "e.product_model_number, e.product_color, e.logo_position, e.product_specification, " +
                   "e.employee_id, e.rfid_tag_number, e.product_serial_number, " +
                   "e.locker_model, e.locker_serial_number, e.sorting_box_number, " +
                   "e.dirty_locker_model, e.dirty_locker_serial_number, " +
                   "e.protocol_usage_months, e.weekly_schedule, e.weekly_wash_date, " +
                   "e.weekly_wash_frequency, e.product_wash_info, e.single_wash_rental_fee, " +
                   "e.weekly_wash_rental_fee, e.clothing_settlement_price, e.clothing_residual_value, " +
                   "e.protocol_start_date, e.protocol_end_date, e.resignation_date, " +
                   "e.attributes, e.status " +
                   "FROM employee_product_configuration e " +
                   whereClauses;
                   
            // 添加排序条件
            if (sorts != null && !sorts.isEmpty()) {
                sql += " ORDER BY ";
                for (int i = 0; i < sorts.size(); i++) {
                    SortCondition sort = sorts.get(i);
                    String dbField = mapPropertyToDbField(sort.getProperty());
                    if (dbField != null) {
                        sql += dbField + " " + (SortCondition.SORT_ASC.equals(sort.getDirection()) ? "ASC" : "DESC");
                        if (i < sorts.size() - 1) {
                            sql += ", ";
                        }
                    }
                }
            } else {
                sql += " ORDER BY e.id";
            }
                   
            logger.info("执行联表查询: {}", sql);
            logger.info("查询参数: {}", parameters);
            
            // 执行查询
            List<Map<String, Object>> resultList;
            if (parameters.isEmpty()) {
                resultList = jdbcTemplate.queryForList(sql);
            } else {
                resultList = jdbcTemplate.queryForList(sql, parameters.toArray());
            }
            logger.info("查询到 {} 条符合条件的员工产品配置记录", resultList.size());
            
            int index = 1;
            for (Map<String, Object> row : resultList) {
                LeaseClothingExcel excel = new LeaseClothingExcel();
                excel.setIndex(index++);
                
                // 填充Excel数据
                excel.setEmployeeName(getStringValue(row, "employee_name"));
                excel.setEmployeePhone(getStringValue(row, "employee_phone"));
                excel.setEnterpriseName(getStringValue(row, "enterprise_name"));
                excel.setDepartment(getStringValue(row, "department"));
                excel.setSubDepartment(getStringValue(row, "sub_department"));
                excel.setConfigType(getStringValue(row, "configuration_category"));
                excel.setProductModel(getStringValue(row, "product_model_number"));
                excel.setProductColor(getStringValue(row, "product_color"));
                excel.setLogoPosition(getStringValue(row, "logo_position"));
                excel.setProductSpecification(getStringValue(row, "product_specification"));
                excel.setEmployeeId(getStringValue(row, "employee_id"));
                excel.setRfidTagNumber(getStringValue(row, "rfid_tag_number"));
                excel.setProductSerialNumber(getStringValue(row, "product_serial_number"));
                excel.setCabinetConfigModel(getStringValue(row, "locker_model"));
                excel.setLockerSerialNumber(getStringValue(row, "locker_serial_number"));
                excel.setSortingBoxNumber(getStringValue(row, "sorting_box_number"));
                excel.setDirtyClothingCabinetModel(getStringValue(row, "dirty_locker_model"));
                excel.setDirtyClothingCabinetSerialNumber(getStringValue(row, "dirty_locker_serial_number"));
                excel.setConfigurationUseMonths(getStringValue(row, "protocol_usage_months"));
                excel.setWeeklyWorkSchedule(getStringValue(row, "weekly_schedule"));
                excel.setWeeklyWashDate(getStringValue(row, "weekly_wash_date"));
                excel.setWeeklyWashCount(getIntegerValue(row, "weekly_wash_frequency"));
                excel.setWashingInformation(getStringValue(row, "product_wash_info"));
                excel.setSingleWashRentalFee(getBigDecimalValue(row, "single_wash_rental_fee"));
                excel.setWeeklyWashRentalFee(getBigDecimalValue(row, "weekly_wash_rental_fee"));
                excel.setClothingSettlementPrice(getBigDecimalValue(row, "clothing_settlement_price"));
                excel.setCurrentMonthClothingValue(getBigDecimalValue(row, "clothing_residual_value"));
                excel.setProtocolStartDate((Date) row.get("protocol_start_date"));
                excel.setProtocolEndDate((Date) row.get("protocol_end_date"));
                excel.setResignationDate((Date) row.get("resignation_date"));
                excel.setAttribute(getStringValue(row, "attributes"));
                excel.setStatus(getStringValue(row, "status"));
                
                excelList.add(excel);
            }
            
            // 如果从员工产品配置表中获取不到数据，则尝试从服装表中获取
            if (excelList.isEmpty()) {
                logger.info("员工产品配置表无数据，尝试从服装表获取数据");
                
                // 重置查询条件
                whereClauses = new StringBuilder(" WHERE 1=1 ");
                parameters = new ArrayList<>();
                
                // 处理筛选条件
                if (filters != null && !filters.isEmpty()) {
                    for (FilterCondition filter : filters) {
                        // 将员工产品配置表的筛选条件转换为服装表的条件
                        String clothingField = mapToClothingField(filter.getProperty());
                        if (clothingField != null) {
                                                    String operator = filter.getOperator();
                        Object valueObj = filter.getValue();
                        String value = valueObj != null ? valueObj.toString() : null;
                            
                            if (value != null && !value.isEmpty()) {
                                switch (operator) {
                                    case FilterCondition.EQ:
                                        whereClauses.append(" AND ").append(clothingField).append(" = ? ");
                                        parameters.add(value);
                                        break;
                                    case FilterCondition.LIKE:
                                        whereClauses.append(" AND ").append(clothingField).append(" LIKE ? ");
                                        parameters.add("%" + value + "%");
                                        break;
                                    // 其他操作符...处理方式类似
                                }
                            }
                        }
                    }
                }
                
                String clothingSql = "SELECT c.id, c.clo_type, c.clo_model, c.clo_color, c.clo_logo, " +
                        "c.clo_spec, c.clo_rfid, c.clo_number, c.status, c.lease_time " +
                        "FROM wms_clothing c " + whereClauses;
                
                // 添加排序条件
                if (sorts != null && !sorts.isEmpty()) {
                    clothingSql += " ORDER BY ";
                    for (int i = 0; i < sorts.size(); i++) {
                        SortCondition sort = sorts.get(i);
                        String dbField = mapToClothingField(sort.getProperty());
                        if (dbField != null) {
                            clothingSql += dbField + " " + (SortCondition.SORT_ASC.equals(sort.getDirection()) ? "ASC" : "DESC");
                            if (i < sorts.size() - 1) {
                                clothingSql += ", ";
                            }
                        }
                    }
                } else {
                    clothingSql += " ORDER BY c.id";
                }
                
                logger.info("执行服装表查询: {}", clothingSql);
                logger.info("查询参数: {}", parameters);
                
                List<Map<String, Object>> clothingList;
                if (parameters.isEmpty()) {
                    clothingList = jdbcTemplate.queryForList(clothingSql);
                } else {
                    clothingList = jdbcTemplate.queryForList(clothingSql, parameters.toArray());
                }
                logger.info("查询到 {} 条租赁状态的服装记录", clothingList.size());
                
                // 批量获取RFID对应的员工产品配置信息
                List<String> rfids = clothingList.stream()
                        .map(row -> getStringValue(row, "clo_rfid"))
                        .filter(rfid -> rfid != null && !rfid.isEmpty())
                        .collect(Collectors.toList());
                
                Map<String, Map<String, Object>> rfidConfigMap = new HashMap<>();
                
                if (!rfids.isEmpty()) {
                    // 构建IN查询，分批处理以避免IN条件太长
                    int batchSize = 500;  // 每批查询的RFID数量
                    for (int i = 0; i < rfids.size(); i += batchSize) {
                        int endIndex = Math.min(i + batchSize, rfids.size());
                        List<String> batchRfids = rfids.subList(i, endIndex);
                        
                        StringBuilder inClause = new StringBuilder();
                        for (int j = 0; j < batchRfids.size(); j++) {
                            inClause.append("'").append(batchRfids.get(j)).append("'");
                            if (j < batchRfids.size() - 1) {
                                inClause.append(",");
                            }
                        }
                        
                        String employeeConfigSql = "SELECT e.* FROM employee_product_configuration e " +
                                "WHERE e.rfid_tag_number IN (" + inClause + ")";
                        
                        List<Map<String, Object>> configList = jdbcTemplate.queryForList(employeeConfigSql);
                        logger.info("查询到 {} 条关联的员工产品配置记录", configList.size());
                        
                        // 保存RFID到配置的映射
                        for (Map<String, Object> config : configList) {
                            String rfid = getStringValue(config, "rfid_tag_number");
                            if (rfid != null && !rfid.isEmpty()) {
                                rfidConfigMap.put(rfid, config);
                            }
                        }
                    }
                }
                
                // 遍历服装记录，生成Excel数据
                index = 1;
                for (Map<String, Object> clothing : clothingList) {
                    LeaseClothingExcel excel = new LeaseClothingExcel();
                    excel.setIndex(index++);
                    
                    String rfid = getStringValue(clothing, "clo_rfid");
                    Map<String, Object> employeeConfig = rfid != null ? rfidConfigMap.get(rfid) : null;
                    
                    if (employeeConfig != null) {
                        // 员工和企业信息
                        excel.setEmployeeName(getStringValue(employeeConfig, "employee_name"));
                        excel.setEmployeePhone(getStringValue(employeeConfig, "employee_phone"));
                        excel.setEnterpriseName(getStringValue(employeeConfig, "enterprise_name"));
                        excel.setDepartment(getStringValue(employeeConfig, "department"));
                        excel.setSubDepartment(getStringValue(employeeConfig, "sub_department"));
                        
                        // 产品信息
                        excel.setConfigType(getStringValue(employeeConfig, "configuration_category"));
                        excel.setProductModel(getStringValue(employeeConfig, "product_model_number"));
                        excel.setProductColor(getStringValue(employeeConfig, "product_color"));
                        excel.setLogoPosition(getStringValue(employeeConfig, "logo_position"));
                        excel.setProductSpecification(getStringValue(employeeConfig, "product_specification"));
                        excel.setEmployeeId(getStringValue(employeeConfig, "employee_id"));
                        excel.setRfidTagNumber(getStringValue(employeeConfig, "rfid_tag_number"));
                        excel.setProductSerialNumber(getStringValue(employeeConfig, "product_serial_number"));
                        
                        // 柜信息
                        excel.setCabinetConfigModel(getStringValue(employeeConfig, "locker_model"));
                        excel.setLockerSerialNumber(getStringValue(employeeConfig, "locker_serial_number"));
                        excel.setSortingBoxNumber(getStringValue(employeeConfig, "sorting_box_number"));
                        excel.setDirtyClothingCabinetModel(getStringValue(employeeConfig, "dirty_locker_model"));
                        excel.setDirtyClothingCabinetSerialNumber(getStringValue(employeeConfig, "dirty_locker_serial_number"));
                        
                        // 协议信息
                        excel.setConfigurationUseMonths(getStringValue(employeeConfig, "protocol_usage_months"));
                        excel.setWeeklyWorkSchedule(getStringValue(employeeConfig, "weekly_schedule"));
                        excel.setWeeklyWashDate(getStringValue(employeeConfig, "weekly_wash_date"));
                        excel.setWeeklyWashCount(getIntegerValue(employeeConfig, "weekly_wash_frequency"));
                        excel.setWashingInformation(getStringValue(employeeConfig, "product_wash_info"));
                        excel.setSingleWashRentalFee(getBigDecimalValue(employeeConfig, "single_wash_rental_fee"));
                        excel.setWeeklyWashRentalFee(getBigDecimalValue(employeeConfig, "weekly_wash_rental_fee"));
                        excel.setClothingSettlementPrice(getBigDecimalValue(employeeConfig, "clothing_settlement_price"));
                        excel.setCurrentMonthClothingValue(getBigDecimalValue(employeeConfig, "clothing_residual_value"));
                        excel.setProtocolStartDate((Date) employeeConfig.get("protocol_start_date"));
                        excel.setProtocolEndDate((Date) employeeConfig.get("protocol_end_date"));
                        excel.setResignationDate((Date) employeeConfig.get("resignation_date"));
                        excel.setAttribute(getStringValue(employeeConfig, "attributes"));
                        excel.setStatus(getStringValue(employeeConfig, "status"));
                    } else {
                        // 如果找不到关联的员工产品配置，则填充可以从服装表获取的基本信息
                        excel.setConfigType(getStringValue(clothing, "clo_type"));
                        excel.setProductModel(getStringValue(clothing, "clo_model"));
                        excel.setProductColor(getStringValue(clothing, "clo_color"));
                        excel.setLogoPosition(getStringValue(clothing, "clo_logo"));
                        excel.setProductSpecification(getStringValue(clothing, "clo_spec"));
                        excel.setRfidTagNumber(rfid);
                        excel.setProductSerialNumber(getStringValue(clothing, "clo_number"));
                        excel.setStatus("租赁中");
                    }
                    
                    excelList.add(excel);
                }
            }

            if (excelList.isEmpty()) {
                logger.warn("未找到符合条件的租赁服装记录！");
                // 创建一个空的Excel表格
                List<LeaseClothingExcel> emptyList = new ArrayList<>();
                EasyExcel.write(response.getOutputStream(), LeaseClothingExcel.class)
                        .sheet("租赁服装信息(无数据)")
                        .doWrite(emptyList);
                return;
            }

            logger.info("准备写入Excel文件，数据条数: {}", excelList.size());
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("租赁服装信息", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            
            // 写入Excel文件
            EasyExcel.write(response.getOutputStream(), LeaseClothingExcel.class)
                    .sheet("租赁服装信息")
                    .doWrite(excelList);
                    
            logger.info("导出租赁服装信息Excel完成，共 {} 条记录", excelList.size());
        } catch (Exception e) {
            logger.error("导出租赁服装信息失败", e);
            throw new IOException("导出租赁服装信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 将前端属性名映射到数据库字段名
     */
    private String mapPropertyToDbField(String property) {
        // 根据前端传递的字段名映射到数据库字段名
        switch (property) {
            case "employeeName":
                return "e.employee_name";
            case "employeePhone":
                return "e.employee_phone";
            case "enterpriseName":
                return "e.enterprise_name";
            case "department":
                return "e.department";
            case "subDepartment":
                return "e.sub_department";
            case "configType":
            case "configurationType":
                return "e.configuration_category";
            case "productModel":
            case "productModelNumber":
                return "e.product_model_number";
            case "productColor":
                return "e.product_color";
            case "logoPosition":
                return "e.logo_position";
            case "productSpecification":
                return "e.product_specification";
            case "employeeId":
                return "e.employee_id";
            case "rfidTagNumber":
                return "e.rfid_tag_number";
            case "productSerialNumber":
                return "e.product_serial_number";
            case "lockerSerialNumber":
                return "e.locker_serial_number";
            case "status":
                return "e.status";
            case "protocolStartDate":
                return "e.protocol_start_date";
            case "protocolEndDate":
                return "e.protocol_end_date";
            default:
                return null;
        }
    }
    
    /**
     * 将员工产品配置表字段映射到服装表字段
     */
    private String mapToClothingField(String property) {
        // 根据前端传递的字段名映射到服装表字段名
        switch (property) {
            case "configType":
            case "configurationType":
                return "c.clo_type";
            case "productModel":
            case "productModelNumber":
                return "c.clo_model";
            case "productColor":
                return "c.clo_color";
            case "logoPosition":
                return "c.clo_logo";
            case "productSpecification":
                return "c.clo_spec";
            case "rfidTagNumber":
                return "c.clo_rfid";
            case "productSerialNumber":
                return "c.clo_number";
            case "status":
                return "c.status";
            default:
                return null;
        }
    }
    
    /**
     * 从Map中获取String类型的值
     */
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }
    
    /**
     * 从Map中获取Integer类型的值
     */
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            logger.warn("无法将值[{}]转换为Integer", value);
            return null;
        }
    }
    
    /**
     * 从Map中获取BigDecimal类型的值
     */
    private BigDecimal getBigDecimalValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            logger.warn("无法将值[{}]转换为BigDecimal", value);
            return null;
        }
    }
} 