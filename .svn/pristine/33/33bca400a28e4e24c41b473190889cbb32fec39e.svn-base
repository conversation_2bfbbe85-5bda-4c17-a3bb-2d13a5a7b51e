package com.rutong.platform.device.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.device.entity.DevInCabinet;

@Service
public class InCabinetService extends BaseService<DevInCabinet> {

    public void updateInfo(DevInCabinet devInCabinet) {
        dao.persist(devInCabinet);
    }


    public DevInCabinet findByCabNo(String lockerSerialNumber) {
        return dao.findByPropertyFirst(DevInCabinet.class, DevInCabinet.FIELD_CABNO, lockerSerialNumber);
    }

    public DevInCabinet findByHostAndSid(String host, Integer sid) {
        return dao.findByPropertyFirst(DevInCabinet.class, DevInCabinet.FIELD_HOST, host, DevInCabinet.FIELD_SID, sid);
    }

    public List<DevInCabinet> findByCol(int col) {
        return dao.findByProperty(DevInCabinet.class, DevInCabinet.FIELD_COL, col);
    }

    public List<DevInCabinet> findBySortingnoAndCol(String sortingno, int col) {
        return dao.findByProperty(DevInCabinet.class, DevInCabinet.FIELD_SORTINGNO, sortingno, DevInCabinet.FIELD_COL, col);
    }

    public List<DevInCabinet> findByHostAndColAndType(String host, String col, String type) {
        return dao.findByProperty(DevInCabinet.class, "host", host, DevInCabinet.FIELD_COL, col, "type", type);

    }
}
