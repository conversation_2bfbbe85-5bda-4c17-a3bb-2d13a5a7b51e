package com.rutong.platform.sys.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.ResultBean;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysRole;
import com.rutong.platform.sys.service.SysRoleService;

@RestController
@RequestMapping("/system/role")
public class SysRoleController extends CrudController<SysRole>{
	
	@Autowired
	private SysRoleService roleService;

	@Override
	public BaseService<SysRole> getService() {
		return roleService;
	}

	@PostMapping("/authrole.do")
	public ResultBean authrole(SysRole role) {
		roleService.authrole(role.getId(),role.getMenuIds());
		return ResultBean.success();
	}
	
	@GetMapping("/getRoleMenuIds.do")
	public ResultBean getRoleMenuIds(String roleid) {
		List<String> menuIds = roleService.getRoleMenuIds(roleid);
		return ResultBean.success(menuIds);
	}
	
	@GetMapping("/getRoles.do")
	public ResultBean getRoles() {
		List<SysRole> roles = roleService.findAll();
		return ResultBean.success(roles);
	}
	
	@Override
	public ResultBean delete(@RequestBody SysRole entity) {
		//删除之前校验是否存在关联用户情况
		if(roleService.checkUser(entity)) {
			return ResultBean.error("请先取消用户关联信息！", 500);
		}
		//删除关联菜单
		roleService.deleteRoleMenu(entity);
		return super.delete(entity);
	}
}
