package com.rutong.platform.fms.controller;

import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.fms.entity.ReceiptPayable;
import com.rutong.platform.fms.entity.ReceiptPayableItem;
import com.rutong.platform.fms.service.ReceiptPayableItemService;
import com.rutong.platform.fms.service.ReceiptPayableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/fms/receivableCollectionItem")
public class ReceiptPayableItemController extends CrudController<ReceiptPayableItem> {

    @Autowired
    private ReceiptPayableItemService receiptPayableItemService;


    @Override
    public BaseService<ReceiptPayableItem> getService() {
        return receiptPayableItemService;
    }


}
