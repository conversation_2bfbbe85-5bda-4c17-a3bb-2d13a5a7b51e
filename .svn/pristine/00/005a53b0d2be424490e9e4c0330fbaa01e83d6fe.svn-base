package com.rutong.platform.app.controller;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.app.model.Achievement;
import com.rutong.platform.app.service.AppService;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.entity.PositionGuide;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.device.entity.DevInCabinet;
import com.rutong.platform.device.entity.DevSorting;
import com.rutong.platform.device.service.InCabinetService;
import com.rutong.platform.device.service.SortingService;
import com.rutong.platform.washing.entity.WashingTask;
import com.rutong.platform.washing.service.WashingTaskService;
import com.rutong.platform.wms.entity.WmsDelivery;
import com.rutong.platform.wms.entity.WmsFeedback;
import com.rutong.platform.wms.service.WmsDeliveryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/app")
public class PdaController {

    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;

    @Autowired
    private WmsDeliveryService wmsDeliveryService;

    @Autowired
    private WashingTaskService washingTaskService;



    @Autowired
    private AppService appService;


    /**     =============库存接口=============      **/
    /**
     * 获取出入库单据
     * @param type 出库or入库
     * @return
     */
    @PostMapping("/getOrder.do")
    public ResultBean getOrder(String type) {
        // 调用appService的getOrder方法，传入type参数，返回结果
        return appService.getOrder(type);
    }

    /**
     * 上传出入库数据
     * @param id 单据id
     * @param dataJson 数据data
     * @param type 出库or入库
     * @return
     */
    @PostMapping("/uploadOrder.do")
    public ResultBean uploadOrder(String id, String dataJson, String type) {
        return appService.uploadOrder(id,dataJson,type);
    }

    /**
     * 获取洗涤任务信息
     * @param status 状态  3L 查询所有  其他根据状态来
     * @return
     */
    @PostMapping("/getWashingTask.do")
    public ResultBean getWashingTask(Long status) {
        return appService.getWashingTask(status);
    }
  /**
     * 获取洗涤任务信息
     * @return
     */
    @PostMapping("/getWashingTaskPc.do")
    public ResultBean getWashingTaskPc() {
        return appService.getWashingTaskPc();
    }

    /**
     * 获取洗涤任务交接明细
     * @param id
     * @return
     */
    @PostMapping("/getWashingTaskItem.do")
    public ResultBean getWashingTaskItem(String id) {
        return appService.getWashingTaskItem(id);
    }


    /**
     * 获取洗涤任务交接明细下的服装内容
     * @param id
     * @return
     */
    @PostMapping("/getWashingTaskItemInfo.do")
    public ResultBean getWashingTaskItemInfo(String id) {
        return appService.getWashingTaskItemInfo(id);
    }


    /**
     * 结束洗涤任务
     * @param id
     * @return
     */
    @PostMapping("/overWashingTask.do")
    public ResultBean overWashingTask(String id) {
        return appService.overWashingTask(id);
    }



    /**     =============库存接口=============      **/


    /**
     * 登录信息
     */
    @PostMapping("/login.do")
    public ResultBean login(String username, String password) {
        String token = appService.login(username, password);
        return ResultBean.success(token);
    }
    @GetMapping("/getUserinfo.do")
    public ResultBean getUserinfo() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return ResultBean.success(loginUser.getUser());
    }

    /**
     * 根据rifd查询服装
     *
     * @param rfid
     * @return
     */
    @GetMapping("/getClothingByRfid.do")
    public ResultBean getClothingByRfid(String rfid) {
        System.out.println(rfid);
        EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfid(rfid);
        if (employeeProductConfiguration == null) {
            return ResultBean.error("没有查到有效数据", 500);
        }
        return ResultBean.success(employeeProductConfiguration);
    }
    /**
     * 根据rifd和任务单号查询服装
     *
     * @param rfid rifd
     * @param taskId 任务id
     * @return
     */
    @GetMapping("/getClothingByRfidAndTaskId.do")
    public ResultBean getClothingByRfid(String rfid,String taskId) {
        WashingTask byId = washingTaskService.findById(taskId);
        EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfidAndClientId(rfid,byId.getClientId());
        if (employeeProductConfiguration == null) {
            return ResultBean.error("没有查到有效数据", 500);
        }
        return ResultBean.success(employeeProductConfiguration);
    }
    /**
     * 提交盘点到的服装 洗涤任务
     * @param proConfigurations 读取到的信息
     * @param signImg 图片
     * @param type 类型
     * @param orderId  订单id
     * @return
     */
    @PostMapping("/postClothingList.do")
    public ResultBean postClothingList(
            @RequestList(clazz = String.class) List<String> proConfigurations,
            String signImg, String type, String orderId) {
        // 生成一次交接记录
        if (proConfigurations.size() == 0) {
            return ResultBean.error("服装信息无效", 500);
        }
        wmsDeliveryService.generateBatch(proConfigurations, signImg, type, "pda", orderId);
        return ResultBean.success();
    }

    /**
     * 车辆列表
     *
     * @return
     */
    @GetMapping("/getCarList.do")
    public ResultBean getCarList() {
        List<String> list = appService.getCarList();
        return ResultBean.success(list);
    }

    /**
     * 工作记录
     *
     * @return
     */
    @GetMapping("/getWorkList.do")
    public ResultBean getWorkList() {
        List<WmsDelivery> list = appService.getWorkList();
        return ResultBean.success(list);
    }

    /**
     * 个人业绩
     *
     * @return
     */
    @GetMapping("/getAchievement.do")
    public ResultBean getAchievement() {
        Achievement data = appService.getAchievement();
        return ResultBean.success(data);
    }

    /**
     * 操作指南
     *
     * @return
     */
    @GetMapping("/getPositionGuide.do")
    public ResultBean getPositionGuide() {
        PositionGuide data = appService.getPositionGuide();
        return ResultBean.success(data);
    }

    /**
     * 车况报备
     */
    @PostMapping("/postCarinfo.do")
    public ResultBean postCarinfo(String carno, String result) {
        appService.saveCarinfo(carno, result);
        return ResultBean.success();
    }

    /**
     * 意见反馈
     */
    @PostMapping("/postFeedback.do")
    public ResultBean postFeedback(WmsFeedback feedback) {
        appService.saveFeedback(feedback);
        return ResultBean.success();
    }
}
