package com.rutong.framework.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Base64;
import java.util.Date;

import org.apache.commons.lang3.time.DateFormatUtils;

import com.rutong.framework.constant.GlobalContsant;
import com.rutong.framework.utils.uuid.UUID;

public class ImageUtil {

	public static String base64Save(String base64String) {
		byte[] imageBytes = Base64.getDecoder().decode(base64String);
		String fileName = UUID.fastUUID().toString() + ".png";
		// 文件相对路径
		String folderPath = File.separator + "file" + File.separator + DateFormatUtils.format(new Date(), "yyyy/MM/dd");
		String attachPath = GlobalContsant.ATTACH_PATH;
		File folder = new File(attachPath + folderPath);
		if (!folder.exists()) {
			folder.mkdirs();
		}
		File file = new File(attachPath + folderPath + File.separator + fileName);
		if(!file.exists()) {
			try {
				file.createNewFile();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		try (OutputStream outputStream = new FileOutputStream(file)) {
			outputStream.write(imageBytes);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return GlobalContsant.PROFILE_PATH + folderPath + File.separator + fileName;
	}

}
