package com.rutong.platform.fms.entity;

/**
 * @ClassName ExpenseSettlement * @Description TODO
 * <AUTHOR>
 * @Date 15:36 2024/11/7
 * @Version 1.0
 **/

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 已收款单
 */
@Data
@Entity
@Table(name = "fms_receipt_payable_item")
public class ReceiptPayableItem extends BaseEntity {
    public static final String FIELD_COL_CONTRACT_NUMBER = "contractNumber";

    /**
     * 结算编号
     */
    private String settlementNumber;
    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 客户名称
     */
    private String enterpriseName;

    /** 收款金额 */
    @JsonSerialize(using= ToStringSerializer.class)
    private BigDecimal receivableAmount;

    private String collectionAccount; // 收款账户

    private String relatedRemarks; // 相关备注
    private String filler; // 填写人
}
