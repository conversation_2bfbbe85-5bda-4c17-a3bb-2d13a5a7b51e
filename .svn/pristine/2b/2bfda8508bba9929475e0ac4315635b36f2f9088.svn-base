package com.rutong.platform.device.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.device.entity.DevSorting;
import com.rutong.platform.device.service.SortingService;

@RestController
@RequestMapping("/device/sorting")
@LogDesc(title = "分拣台")
public class SortingController extends CrudController<DevSorting>{

	@Autowired
	private SortingService sortingService;
	
	@Override
	public BaseService<DevSorting> getService() {
		return sortingService;
	}

}
