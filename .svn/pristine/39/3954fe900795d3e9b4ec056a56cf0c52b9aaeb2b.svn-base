package com.rutong.platform.sys.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.utils.TreeBuilder;
import com.rutong.platform.sys.entity.SysDept;
import com.rutong.platform.sys.pojo.DeptTree;
import com.rutong.platform.sys.service.BaseService;
import com.rutong.platform.sys.service.SysDeptService;

@RestController
@RequestMapping("/system/dept")
public class SysDeptController extends CrudController<SysDept> {

	@Autowired
	private SysDeptService deptService;

	@Override
	public BaseService<SysDept> getService() {
		return deptService;
	}

	@Override
	public PageInfo<SysDept> findAllByPage(GridParams gridParams,
			@RequestList(clazz = FilterCondition.class) List<FilterCondition> filters) {
		if(filters.size() == 0) {
			FilterCondition filterCondition = new FilterCondition();
			filterCondition.setProperty("dept");
			filterCondition.setOperator(FilterCondition.ISNULL);
			filters.add(filterCondition);
		}
		return super.findAllByPage(gridParams, filters);
	}
	
	@GetMapping(value = "/getDeptTree.do")
	public ResultBean getDeptTree() {
		List<DeptTree> depts= deptService.getDeptTree();
		return ResultBean.success(TreeBuilder.buildListToTree(depts));
	}
}
