package com.rutong.framework.core.interceptor;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Map;

import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import com.alibaba.fastjson2.JSON;
import com.rutong.framework.utils.StringUtils;

public class ListArgumentResolver implements HandlerMethodArgumentResolver {

	@Override
	public boolean supportsParameter(MethodParameter parameter) {
		RequestList requestList = parameter.getParameterAnnotation(RequestList.class);
		return requestList != null;
	}

	@Override
	public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
			NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
		RequestList requestList = parameter.getParameterAnnotation(RequestList.class);
		try {
			if (requestList != null) {
				String paramstr = parameter.getParameterName();
				String text = webRequest.getParameter(paramstr);
				Type type = parameter.getGenericParameterType();
				Class<?> clazz = Map.class;
				if (type instanceof ParameterizedType) {
					Type[] types = ((ParameterizedType) type).getActualTypeArguments();
					if (types[0] instanceof ParameterizedType) {
						ParameterizedType parameterizedType = (ParameterizedType) types[0];
						clazz = (Class<?>) parameterizedType.getRawType();
					} else if (types[0] instanceof Class) {
						clazz = (Class<?>) types[0];
					}
				}
				if(StringUtils.isNull(text)) {
					return JSON.parseArray("[]", clazz);
				}
				return JSON.parseArray(text, clazz);
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
		return null;
	}
}