package com.rutong.platform.common.service;

import java.util.ArrayList;
import java.util.List;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.utils.StringUtils;
import com.rutong.platform.common.entity.TreeEntity;

public class TreeService<T extends TreeEntity> extends BaseService<T> {

	@Override
	public void save(T t) {
		if (StringUtils.isEmpty(t.getParentid())) {
			t.setParentid("00");
		}
		super.save(t);
	}

	@Override
	public T update(T t) {
		if (StringUtils.isEmpty(t.getParentid())) {
			t.setParentid("00");
		}
		return super.update(t);
	}

	// 将查到的数据再递归查询子数据
	public PageInfo<T> findAllByTree(GridParams gridParams, List<FilterCondition> filters, List<SortCondition> sorts) {
		Long count = dao.count(getPersistentClass(), filters);
		List<T> dataList = new ArrayList<T>();
		if (count > 0L) {
			dataList = dao.findAll(getPersistentClass(), gridParams, filters, sorts);
			// 递归查询子数据
			recursionChildren(dataList);
		}
		return new PageInfo<T>(count, dataList);
	}

	private void recursionChildren(List<T> dataList) {
		for (T t : dataList) {
			if (t.getHasChildren() > 0) {
				List<T> children = dao.findByProperty(getPersistentClass(), TreeEntity.PARENTID, t.getId());
				if (StringUtils.isNotEmpty(children)) {
					t.setChildren(children);
					recursionChildren(children);
				}
			}
		}
	}
}
