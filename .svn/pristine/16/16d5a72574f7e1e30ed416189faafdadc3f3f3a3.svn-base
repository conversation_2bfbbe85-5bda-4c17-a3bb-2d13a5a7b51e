package com.rutong.platform.wechat.controller;

import com.rutong.framework.bean.ResultBean;
import com.rutong.platform.wechat.entity.SysFaq;
import com.rutong.platform.wechat.service.SysFaqService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: mkwang
 * @date: 2025年4月29日08:49:21
 * @description:常见问题查询接口
 */
@RestController // 标记该类为RESTful控制器，返回的数据直接作为HTTP响应体
@RequestMapping("/wechat/faq") // 设置该控制器的基本URL路径为/wechat/faq
public class SysFaqController {

    @Autowired // 自动注入SysFaqService实例
    private SysFaqService sysFaqService;

    @GetMapping("/list") // 处理GET请求，URL为/wechat/faq/list
    public List<SysFaq> list() {
        return sysFaqService.list(); // 调用sysFaqService的list方法，返回常见问题列表
    }

    @PostMapping("/add.do") // 处理POST请求，URL为/wechat/faq/add
    public ResultBean add(@RequestBody SysFaq sysFaq) {
        sysFaqService.add(sysFaq);
        return ResultBean.success("添加成功")  ; // 从请求体中获取SysFaq对象，并调用sysFaqService的add方法添加常见问题
    }

    @PutMapping("/update") // 处理PUT请求，URL为/wechat/faq/update
    public void update(@RequestBody SysFaq sysFaq) {
        sysFaqService.update(sysFaq); // 从请求体中获取SysFaq对象，并调用sysFaqService的update方法更新常见问题
    }

    @DeleteMapping("/delete/{id}") // 处理DELETE请求，URL为/wechat/faq/delete/{id}
    public void delete(@PathVariable Long id) {
        sysFaqService.delete(id); // 从URL路径中获取id，并调用sysFaqService的delete方法删除指定id的常见问题
    }
} 