package com.rutong.platform.washing.entity;

import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName 洗涤任务单
 * <AUTHOR>
 * @Date 15:14 2025/4/2
 * @Version 1.0
 **/
@Data
@Entity
@Table(name = "washing_task")
public class WashingTask extends BaseEntity {
    private String taskOrderNumber; // 任务单号

    // 司机信息
    private String driverName;      // 司机姓名
    private String driverPhone;     // 司机联系方式
    private String driverEmployeeId;// 司机工号

    // 车辆信息
    private String vehicleLicense;  // 车牌号

    // 派发人信息
    private String dispatcherName;  // 派发人姓名
    private String dispatcherEmpId; // 派发人工号

    // 任务信息
    private Date departureTime; // 出发时间
    private Long status;      // 当前状态
    private Long hasAbnormality;// 有无异常
    private Long receivedQty;    // 收取数量（允许null）
    private BigDecimal distance; //距离（KM）
    private String clientId; //客户ID
    private String clientName; //客户名称


}
