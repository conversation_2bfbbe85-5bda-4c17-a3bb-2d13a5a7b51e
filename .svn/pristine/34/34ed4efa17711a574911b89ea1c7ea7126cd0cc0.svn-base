package com.rutong.platform.wms.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.rutong.platform.client.util.ToolsUtil;
import com.rutong.platform.device.entity.DevRecordSorting;
import com.rutong.platform.device.excel.DevRecordSortingExcel;
import com.rutong.platform.washing.excel.WashingRecordExcelByDate;
import org.hibernate.query.Query;
import org.springframework.stereotype.Service;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;


import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class WmsDeliveryDetailService extends BaseService<WmsDeliveryDetail> {

    public List<WmsDeliveryDetail> findByContractNumber(String contractNumber, String type, String date) {
        return dao.findByProperty(WmsDeliveryDetail.class, "contractNumber", contractNumber, "type", type, "batchNo", date, "status", 0);
    }

    public List<WmsDeliveryDetail> findByContractNumberAndProductSerialNumber(String contractNumber, String rfidTagNumberWork, String type) {
        return dao.findByProperty(WmsDeliveryDetail.class, "contractNumber", contractNumber, "rfidTagNumberWork", rfidTagNumberWork, "type", type);
    }

    // 根据订单ID和备注查询
    public List<WmsDeliveryDetail> findByOrderIdAndRemark(String orderId, String type) {
        String sql = "FROM WmsDeliveryDetail WHERE orderId = ?0  and type = ?1";
        return dao.executeQuery(sql, WmsDeliveryDetail.class, orderId, type);
    }


    public List<WmsDeliveryDetail> findByDeliveryId(String delivery_id) {
        String sql = "FROM WmsDeliveryDetail WHERE delivery_id = ?0 ";
        return dao.executeQuery(sql, WmsDeliveryDetail.class, delivery_id);
    }

    public List<WmsDeliveryDetail> findByDeliveryIdAndRemark(String delivery_id, String type) {
        String sql = "FROM WmsDeliveryDetail WHERE delivery_id = ?0 and type = ?1 ";
        return dao.executeQuery(sql, WmsDeliveryDetail.class, delivery_id, type);
    }

    public List<WmsDeliveryDetail> findByRfid(String rfid, String date) {
        return dao.findByProperty(WmsDeliveryDetail.class, "rfidTagNumber", rfid, "batchNo", date);
    }

    public void updateInfo(WmsDeliveryDetail wmsDeliveryDetail) {
        //调用了dao.persist(wmsDeliveryDetail)，
        // 其功能是将传入的WmsDeliveryDetail实体对象持久化到数据库中。
        // persist方法会根据实体的状态决定是插入新记录还是更新已有记录。
        dao.persist(wmsDeliveryDetail);
    }

    public long countByUserAndDate(LoginUser loginUser, Date date) {
        List<FilterCondition> filterConditions = new ArrayList<FilterCondition>();
        FilterCondition filterCondition = new FilterCondition();
        filterCondition.setOperator(FilterCondition.EQ);
        filterCondition.setProperty(BaseEntity.FIELD_OWNER_USER_ID);
        filterCondition.setValue(loginUser.getUserId());
        filterConditions.add(filterCondition);
        FilterCondition filterCondition2 = new FilterCondition();
        filterCondition2.setOperator(FilterCondition.GT);
        filterCondition2.setProperty(BaseEntity.FIELD_CREATE_TIME);
        filterCondition2.setValue(date);
        filterConditions.add(filterCondition2);
        return dao.count(WmsDeliveryDetail.class, filterConditions);
    }

    /**
     * 按照 rfidTagNumberWork 分组查询，并返回每组的计数
     *
     * @param gridParams 分页参数
     * @param filters    过滤条件
     * @param sorts      排序条件
     * @return 分组查询结果，包含计数信息
     */
    public PageInfo<WmsDeliveryDetail> findAllByPageGroupWithCount(GridParams gridParams,
                                                                   List<FilterCondition> filters,
                                                                   List<SortCondition> sorts) {
        StringBuilder sqlBuilder = new StringBuilder();
        StringBuilder countSqlBuilder = new StringBuilder();
        List<Object> parameters = new ArrayList<>();
        List<Object> countParameters = new ArrayList<>();

        sqlBuilder.append("SELECT d, COUNT(d.rfidTagNumberWork)  as rfidTagNumberWorkCount FROM WmsDeliveryDetail d ");
        countSqlBuilder.append("SELECT COUNT(DISTINCT d.rfidTagNumberWork) FROM WmsDeliveryDetail d ");

        if (filters != null && !filters.isEmpty()) {
            sqlBuilder.append("WHERE ");
            countSqlBuilder.append("WHERE ");

            for (int i = 0; i < filters.size(); i++) {
                FilterCondition filter = filters.get(i);
                if (i > 0) {
                    sqlBuilder.append(" AND ");
                    countSqlBuilder.append(" AND ");
                }

                String property = filter.getProperty();
                String operator = filter.getOperator();
                Object value = filter.getValue();
                // 日期类型转换
                if (ToolsUtil.isDateField(property)) {
                    value = ToolsUtil.convertToDate(value);
                }
                String columnName = "d." + property;

                appendCondition(sqlBuilder, columnName, operator, value, parameters, i + 1);

                appendCondition(countSqlBuilder, columnName, operator, value, countParameters, i + 1);
            }
        }

        // 添加分组
        sqlBuilder.append(" GROUP BY d.rfidTagNumberWork ");

        // 构建ORDER BY条件
        if (sorts != null && !sorts.isEmpty()) {
            sqlBuilder.append("ORDER BY ");
            for (int i = 0; i < sorts.size(); i++) {
                SortCondition sort = sorts.get(i);
                if (i > 0) {
                    sqlBuilder.append(", ");
                }
                String columnName = sort.getProperty();
                sqlBuilder.append(columnName).append(" ").append(sort.getDirection());
            }
        } else {
            // 默认按创建时间倒序排序
            sqlBuilder.append("ORDER BY d.createTime DESC ");
        }

        // 执行查询获取总数
        Long count = 0L;
        if (!countParameters.isEmpty()) {
            Query countQuery = dao.getCurrentSession().createQuery(countSqlBuilder.toString());
            for (int i = 0; i < countParameters.size(); i++) {
                countQuery.setParameter(i + 1, countParameters.get(i));
            }
            count = ((Number) countQuery.getSingleResult()).longValue();
        } else {
            Query countQuery = dao.getCurrentSession().createQuery(countSqlBuilder.toString());
            count = ((Number) countQuery.getSingleResult()).longValue();
        }

        // 执行查询获取数据
        List<WmsDeliveryDetail> dataList = new ArrayList<>();
        if (count > 0L) {
            Query query = dao.getCurrentSession().createQuery(sqlBuilder.toString());

            for (int i = 0; i < parameters.size(); i++) {
                query.setParameter(i + 1, parameters.get(i));
            }

            if (gridParams != null) {
                query.setFirstResult(gridParams.getStart());
                query.setMaxResults(gridParams.getPageSize());
            }
            List<Object[]> results = query.getResultList();
            for (Object[] result : results) {
                WmsDeliveryDetail detail = (WmsDeliveryDetail) result[0];
                detail.setRfidTagNumberWorkCount(((Number)result[1]).longValue());
                dataList.add(detail);
            }
        }
        return new PageInfo<>(count, dataList);
    }

    // 修改后的辅助方法：使用带序号的参数占位符
    private void appendCondition(StringBuilder builder, String columnName,
                                 String operator, Object value,
                                 List<Object> parameters, int paramIndex) {
        if (FilterCondition.EQ.equals(operator)) {
            builder.append(columnName).append(" = ?").append(paramIndex);
            parameters.add(value);
        } else if (FilterCondition.LIKE.equals(operator)) {
            builder.append(columnName).append(" LIKE ?").append(paramIndex);
            parameters.add("%" + value + "%");
        } else if (FilterCondition.GT.equals(operator)) {
            builder.append(columnName).append(" > ?").append(paramIndex);
            parameters.add(value);
        } else if (FilterCondition.LT.equals(operator)) {
            builder.append(columnName).append(" < ?").append(paramIndex);
            parameters.add(value);
        } else if (FilterCondition.GE.equals(operator)) {
            builder.append(columnName).append(" >= ?").append(paramIndex);
            parameters.add(value);
        } else if (FilterCondition.LE.equals(operator)) {
            builder.append(columnName).append(" <= ?").append(paramIndex);
            parameters.add(value);
        }
    }


    /**
     * 导出合并后的洗涤报表和洗涤数据
     *
     * @param dataList
     * @param response
     * @throws IOException
     */
    public void exportMergedWorkWearData(List<WmsDeliveryDetail> dataList,
                                         HttpServletResponse response) throws IOException {

        //从 dataList 中提取所有记录的 workWearConfigurationCategory（工作服品类）
        Set<String> categories = dataList.stream()
                .map(deliveryRecord ->
                        deliveryRecord.getWorkWearConfigurationCategory() != null ?
                                deliveryRecord.getWorkWearConfigurationCategory() :
                                ""
                )
                .collect(Collectors.toSet());
        List<String> categoryList = new ArrayList<>(categories);
        Collections.sort(categoryList);

        // 动态构建表头
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("洗涤日期"));
        head.add(Collections.singletonList("客户名称"));
        head.add(Collections.singletonList("任务单号"));

        // 添加动态工作服品类列
        for (String category : categoryList) {
            head.add(Collections.singletonList(category));
        }
        head.add(Collections.singletonList("共计"));
        //head.add(Collections.singletonList("备注"));
        //head.add(Collections.singletonList("修补件数"));

        // 合并数据
        List<List<Object>> data = mergeData(dataList, categoryList);

        // 写入Excel
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("合并工作服销售报表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();

        // 第二个sheet，洗涤明细报表
        // 按工作服rfid标签编码进行分组，同时收集有洗涤记录的日期
        Map<String, List<WmsDeliveryDetail>> groupsRecords = new HashMap<>();
        Set<String> allDates = new HashSet<>();

        //创建日期格式化对象,日期列显示格式为"MM-dd"
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd");

        //遍历所有洗涤记录list，进行分组并收集有洗涤记录的日期
        for (WmsDeliveryDetail record : dataList) {
            //获取工作服rfid标签编码
            String rfidCode = record.getRfidTagNumberWork();
            if (rfidCode != null && !rfidCode.trim().isEmpty()) {
                groupsRecords.computeIfAbsent(rfidCode, k -> new ArrayList<>()).add(record);

                // 只收集有洗涤记录的日期
                if (record.getCreateTime() != null) {
                    String dateStr = dateFormat.format(record.getCreateTime());
                    allDates.add(dateStr);
                }
            }
        }

        //日期集合按照日期排序生成一个新的列表，按自然顺序排序的日期字符串
        List<String> sorteDates = allDates.stream().sorted().collect(Collectors.toList());

        //创建名为excellist列表，用于保存WashingRecordExcelByDate类型的对象
        List<WashingRecordExcelByDate> excellist = new ArrayList<>();

        //初始化了一个名为 index 的整数变量，并将其值设置为 1
        int index = 1;

        //遍历循环groupsRecords中所有的键值对，其中 entry.getKey() 是分组的键，entry.getValue() 是分组的值
        for (Map.Entry<String, List<WmsDeliveryDetail>> entry : groupsRecords.entrySet()) {
            //获取键值对中的值
            List<WmsDeliveryDetail> records = entry.getValue();
            if (!records.isEmpty()) {
                //使用第一条记录作为基础信息
                WmsDeliveryDetail baseRecord = records.get(0);
                //创建一个新的excel对象
                WashingRecordExcelByDate excel = convertToWashingRecordExcel(baseRecord, index++);

                //创建洗涤时间Map
                Map<String, String> dateWashingMap = new HashMap<>();
                //根据工作服RFID标签编码收集洗涤日期和洗涤次数
                Map<String, Integer> washingDateCount = new HashMap<>();
                //遍历洗涤记录
                for (WmsDeliveryDetail record : records) {
                    if (record.getCreateTime() != null) {
                        String dateStr = dateFormat.format(record.getCreateTime());
                        //统计洗涤次数
                        washingDateCount.put(dateStr, washingDateCount.getOrDefault(dateStr, 0) + 1);
                    }
                }
                excel.setWashCount(records.size());
                //为每个日期设置洗涤次数
                for (String dateStr : sorteDates) {
                    Integer count = washingDateCount.get(dateStr);
                    dateWashingMap.put(dateStr, count == null ? "" : count.toString());
                }
                excel.setDateWashingMap(dateWashingMap);//设置所有日期的洗涤次数
                //将所有的数据添加到excel中
                excellist.add(excel);
            }
        }
        // 第一个Sheet：洗涤汇总报表
        WriteSheet mergedSheet = EasyExcel.writerSheet(0, "洗涤汇总报表")
                .head(head)
                .build();
        excelWriter.write(data, mergedSheet);

        // 第二个Sheet：洗涤明细数据
        WriteSheet detailSheet = EasyExcel.writerSheet(1, "洗涤明细数据")
                .head(createDynamicHead(sorteDates))
                .build();
        excelWriter.write(createDynamicData(excellist, sorteDates), detailSheet);

        // 完成写入，关闭资源
        excelWriter.finish();
    }

    /**
     * 将WmsDeliveryDetail转换为WashingRecordExcelByDate
     *
     * @param detail WmsDeliveryDetail对象
     * @param index  序号
     * @return WashingRecordExcelByDate对象
     */
    private WashingRecordExcelByDate convertToWashingRecordExcel(WmsDeliveryDetail detail, int index) {
        WashingRecordExcelByDate excel = new WashingRecordExcelByDate();
        excel.setIndex(index);
        excel.setClientName(detail.getEnterpriseName());//客户名称
        excel.setEmployeeName(detail.getEmployeeName());//员工姓名
        excel.setEmployeePhone(detail.getEmployeePhone());//员工电话
        excel.setDepartment(detail.getDepartment());//部门名称
        excel.setSubDepartment(detail.getSubDepartment());//子部门名称
        excel.setWorkWearConfigurationCategory(detail.getWorkWearConfigurationCategory());//工作服品类
        excel.setConfigType(detail.getConfigurationCategory());//配置品类
        excel.setProductModel(detail.getProductModelNumber());//产品款号
        excel.setProductColor(detail.getProductColor());//产品颜色
        excel.setLogoPosition(detail.getLogoPosition());//LOGO位置
        excel.setProductSpecification(detail.getProductSpecification());//产品规格
        excel.setEmployeeId(detail.getEmployeeId());//员工ID
        excel.setRfidCode(detail.getRfidTagNumberWork());//工作服RFID标签编码
        excel.setRfidTagNumber(detail.getRfidTagNumber());//RFID标签号
////        excel.setWashCount(washingRecordService.countByRfidCode(detail.getRfidTagNumberWork()));//洗涤次数
        return excel;
    }



    private List<List<Object>> mergeData(List<WmsDeliveryDetail> dataList, List<String> categories) {
        Map<String, List<Object>> mergedDataMap = new LinkedHashMap<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        for (WmsDeliveryDetail item : dataList) {
            String mergeKey = simpleDateFormat.format(item.getCreateTime()) + "|" + item.getEnterpriseName() + "|" + item.getContractNumber();

            if (!mergedDataMap.containsKey(mergeKey)) {
                List<Object> row = new ArrayList<>();
                row.add(simpleDateFormat.format(item.getCreateTime())); // 洗涤日期
                row.add(item.getEnterpriseName()); // 客户名称
                row.add(item.getContractNumber()); // 任务单号

                // 初始化所有品类数量为0（从第4列开始）
                for (int i = 0; i < categories.size(); i++) {
                    row.add(0);
                }

                // 添加共计列初始值0（位于：3 + categories.size()）
                row.add(0);

                mergedDataMap.put(mergeKey, row);
            }
            List<Object> existingRowUpdated = mergedDataMap.get(mergeKey);
            String workCategoryUpdated = item.getWorkWearConfigurationCategory() != null ? item.getWorkWearConfigurationCategory() : "";
            int categoryIndexUpdated = categories.indexOf(workCategoryUpdated);

            if (categoryIndexUpdated >= 0) {
                // 累加品类数量（每次加1），品类从第4列开始
                Integer currentValueUpdated = (Integer) existingRowUpdated.get(3 + categoryIndexUpdated);
                existingRowUpdated.set(3 + categoryIndexUpdated, currentValueUpdated + 1);

                // 更新总数（共计列）
                Integer currentTotalUpdated = (Integer) existingRowUpdated.get(3 + categories.size());
                existingRowUpdated.set(3 + categories.size(), currentTotalUpdated + 1);
            }
        }
        return new ArrayList<>(mergedDataMap.values());
    }

    /**
     * 动态表头
     *
     * @param dates 日期列表
     * @return 表头列表
     */
    public List<List<String>> createDynamicHead(List<String> dates) {
        List<List<String>> head = new ArrayList<>();
        //固定列
        head.add(Collections.singletonList("序号"));
        head.add(Collections.singletonList("客户名称"));
        head.add(Collections.singletonList("员工姓名"));
        head.add(Collections.singletonList("员工电话"));
        head.add(Collections.singletonList("部门"));
        head.add(Collections.singletonList("分部门"));
        head.add(Collections.singletonList("工作服品类"));
        head.add(Collections.singletonList("配置品类"));
        head.add(Collections.singletonList("产品款号"));
        head.add(Collections.singletonList("产品颜色"));
        head.add(Collections.singletonList("LOGO位置"));
        head.add(Collections.singletonList("产品规格"));
        head.add(Collections.singletonList("员工工号"));
        head.add(Collections.singletonList("工作服RFID标签编码"));
        head.add(Collections.singletonList("RFID标签号"));
        head.add(Collections.singletonList("已洗涤次数"));
        //遍历所有日期，将日期集合dates中的每一个date日期添加到head中
        for (String date : dates) {
            head.add(createHead(date));
        }
        return head;
    }

    /**
     * 创建表头单元格
     *
     * @param title
     * @return
     */
    private List<String> createHead(String title) {
        List<String> head = new ArrayList<>();
        head.add(title);
        return head;
    }

    /**
     * 创建动态日期
     *
     * @param excelList
     * @param dates
     * @return
     */
    private List<List<Object>> createDynamicData(List<WashingRecordExcelByDate> excelList, List<String> dates) {
        List<List<Object>> data = new ArrayList<>();
        for (WashingRecordExcelByDate excel : excelList) {
            List<Object> row = new ArrayList<>();
            //固定列数据
            row.add(excel.getIndex());
            row.add(excel.getClientName());
            row.add(excel.getEmployeeName());
            row.add(excel.getEmployeePhone());
            row.add(excel.getDepartment());
            row.add(excel.getSubDepartment());
            row.add(excel.getWorkWearConfigurationCategory());
            row.add(excel.getConfigType());
            row.add(excel.getProductModel());
            row.add(excel.getProductColor());
            row.add(excel.getLogoPosition());
            row.add(excel.getProductSpecification());
            row.add(excel.getEmployeeId());
            row.add(excel.getRfidCode());
            row.add(excel.getRfidTagNumber());
            row.add(excel.getWashCount());
            //动态日期列数据
            for (String date : dates) {
                row.add(excel.getDateWashing(date));
            }
            data.add(row);
        }
        return data;
    }

}
