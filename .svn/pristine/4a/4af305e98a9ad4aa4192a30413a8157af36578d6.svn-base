package com.rutong.platform.wms.controller;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.utils.BeanUtils;
import com.rutong.framework.utils.Global;
import com.rutong.platform.client.util.ExportUtil;
import com.rutong.platform.wms.entity.WmsClothing;
import com.rutong.platform.wms.entity.WmsClothingExcel;
import com.rutong.platform.wms.entity.WmsClothingOutboundExcel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsClothingOutbound;
import com.rutong.platform.wms.service.WmsClothingOutboundService;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/wms/clothingoutbound")
@LogDesc(title = "出库服装")
public class WmsClothingOutboundController extends CrudController<WmsClothingOutbound>{
	
	@Autowired
	private WmsClothingOutboundService clothingOutboundService;

	@Override
	public BaseService<WmsClothingOutbound> getService() {
		return clothingOutboundService;
	}

	/**
	 * 导出出库服装
	 * @param filters
	 * @return
	 * @throws IOException
	 */
	@PostMapping(value = "/export.do")
	public ResultBean exportClothing(@RequestList(clazz = FilterCondition.class) List<FilterCondition> filters) throws IOException{
		String fileName = "出库服装";
		fileName = ExportUtil.encodingExcelFilename(fileName);
		String folder = Global.getTempPath() + File.separator + "pio" + File.separator;
		FileUtil.touch(folder + fileName);
		List<WmsClothingOutboundExcel> list = new ArrayList<>();
		// 导出的Excel按照创建时间进行降序排序
		List<SortCondition> sorts = new ArrayList<>();
		if (sorts.isEmpty()) {
			SortCondition sortCondition = new SortCondition();
			sortCondition.setProperty("createTime");
			sortCondition.setDirection(SortCondition.SORT_DESC);
			sorts.add(sortCondition);
		}
		List<WmsClothingOutbound> allByObject = clothingOutboundService.findAll(filters,sorts);
		for (WmsClothingOutbound wmsClothingOutbound : allByObject) {
			WmsClothingOutboundExcel wmsClothingOutboundExcel = new WmsClothingOutboundExcel();
			BeanUtils.copyProperties(wmsClothingOutboundExcel,wmsClothingOutbound);
			list.add(wmsClothingOutboundExcel);
		}
		EasyExcel.write(folder + fileName, WmsClothingOutboundExcel.class)
				.sheet("出库服装")
				.doWrite(list);
		return ResultBean.success(fileName);
	}

}
