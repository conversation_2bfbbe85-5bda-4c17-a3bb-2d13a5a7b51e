package com.rutong.platform.client.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.entity.EmployeeProductConfigurationHistory;
import com.rutong.platform.client.service.ClientInfoService;
import com.rutong.platform.client.service.EmployeeProductConfigurationHistoryService;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.common.service.BaseService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/client/employeeProductConfiguration")
public class EmployeeProductConfigurationController extends CrudController<EmployeeProductConfiguration> {

    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;


    @Autowired
    private EmployeeProductConfigurationHistoryService employeeProductConfigurationHistoryService;


    @Autowired
    private ClientInfoService clientInfoService;


    @Override
    public BaseService<EmployeeProductConfiguration> getService() {
        return employeeProductConfigurationService;
    }

    @Override
    public ResultBean save(EmployeeProductConfiguration entity) {
        EmployeeProductConfiguration employeeProductConfiguration1 = new EmployeeProductConfiguration();
        employeeProductConfiguration1.setRfidTagNumber(entity.getRfidTagNumber());
        List<EmployeeProductConfiguration> allByObject = employeeProductConfigurationService.findAllByObject(employeeProductConfiguration1, null);
        if (allByObject.size() > 0) {
            throw new ExcelAnalysisException("RFID标签信息：" + entity.getRfidTagNumber() + "，已存在无法重复添加");
        } else {
//            employeeProductConfiguration.setContractNumber(contractNumber);
            this.save(entity);
        }
        return ResultBean.success(entity);
    }

    @PostMapping(value = "/fetchdataGroup.do")
    public PageInfo<EmployeeProductConfiguration> fetchdataGroup(GridParams gridParams,
                                                                 @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
                                                                 @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        if (sorts.size() == 0) {
            SortCondition sortCondition = new SortCondition();
            sortCondition.setDirection(SortCondition.SORT_DESC);
            sortCondition.setProperty(BaseEntity.FIELD_CREATE_TIME);
            sorts.add(sortCondition);
        }
        return employeeProductConfigurationService.fetchdataGroup(gridParams, filters, sorts);
    }

    @Override
    public ResultBean update(EmployeeProductConfiguration entity) {
        EmployeeProductConfiguration byId = employeeProductConfigurationService.findById(entity.getId());
        EmployeeProductConfigurationHistory employeeProductConfigurationHistory = new EmployeeProductConfigurationHistory();
        BeanUtils.copyProperties(byId, employeeProductConfigurationHistory);
        employeeProductConfigurationHistory.setParentId(byId.getId());
        employeeProductConfigurationHistory.setId(null);
        employeeProductConfigurationHistoryService.save(employeeProductConfigurationHistory);
        employeeProductConfigurationService.update(entity);
        return ResultBean.success(entity);
    }

    /**
     * 条款服装导入
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importExcel.do")
    public ResultBean importExcel(@RequestParam("file") MultipartFile file, String contractNumber) {
        try {
            List<EmployeeProductConfiguration> dataList = EasyExcel.read(file.getInputStream(), EmployeeProductConfiguration.class, null)
                    .doReadAllSync();
            employeeProductConfigurationService.importData(dataList, contractNumber);
//
            return ResultBean.success(dataList);
//            clauseProductConfigService.saveAll(wmsClothings);
        } catch (IOException e) {
            e.printStackTrace();
            return ResultBean.error(e.getMessage(), 500);
        }
    }

}
