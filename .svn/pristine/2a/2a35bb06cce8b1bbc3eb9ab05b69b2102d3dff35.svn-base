package com.rutong.platform.wms.entity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.rutong.platform.common.entity.BaseEntity;

/**
 * 配送明细
 */
@Entity
@Table(name = "wms_delivery_detail")
public class WmsDeliveryDetail extends BaseEntity{

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "delivery_id")
	private WmsDelivery delivery;
	
	//合同人员及服装
	
	
}
