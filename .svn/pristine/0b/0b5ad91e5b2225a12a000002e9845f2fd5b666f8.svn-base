package com.rutong.platform.wechat.controller;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.Log;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.wechat.dto.WashingInfoDTO;
import com.rutong.platform.wechat.dto.WashingItemDTO;
import com.rutong.platform.wechat.service.WashingInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 洗涤信息控制器
 */
@RestController
@RequestMapping(value = "/wechat/washinginfo")
@LogDesc(title = "洗涤信息")
public class WashingInfoController {
    
    private static final Logger log = LoggerFactory.getLogger(WashingInfoController.class);
    
    @Autowired
    private WashingInfoService washingInfoService;
    
    /**
     * 获取当前登录用户的洗涤信息
     * 
     * @return 洗涤信息
     */
    @Log(title = "查询洗涤信息", desc = "查询当前登录用户的洗涤信息")
    @GetMapping(value = "/getCurrentUserWashingInfo.do")
    public ResultBean getCurrentUserWashingInfo() {
        try {
            // 检查用户是否登录
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null || loginUser.getUser() == null) {
                log.warn("用户未登录，无法查询洗涤信息");
                return ResultBean.error("用户未登录", 401);
            }
            
            // 查询洗涤信息
            WashingInfoDTO washingInfo = washingInfoService.getWashingInfoByLoginUser();
            
            // 记录返回的数据，用于调试
            if (washingInfo != null && washingInfo.getWashingItems() != null) {
                for (WashingItemDTO item : washingInfo.getWashingItems()) {
                    log.info("返回洗涤项目: 品类={}, 产品款号={}, 每周洗涤日期={}, 每周洗涤次数={}, 总洗涤次数={}, 已洗次数={}",
                            item.getConfigurationCategory(),
                            item.getProductModelNumber(),
                            item.getWeeklyWashDate(),
                            item.getWeeklyWashFrequency(),
                            item.getTotalWashCount(),
                            item.getWashedCount());
                }
            }
            
            // 返回结果
            return ResultBean.success(washingInfo);
        } catch (Exception e) {
            log.error("查询洗涤信息失败", e);
            return ResultBean.error("查询洗涤信息失败，请稍后重试", 500);
        }
    }
} 