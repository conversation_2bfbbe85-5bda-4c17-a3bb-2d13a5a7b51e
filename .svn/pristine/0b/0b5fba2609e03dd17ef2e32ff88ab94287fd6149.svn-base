package com.rutong.framework.core.jpa;

import javax.persistence.criteria.*;

import com.rutong.framework.utils.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

public class QueryHelp {

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <R, Q> Predicate getPredicate(Root<R> root, List<FilterCondition> filters, CriteriaBuilder cb) {
		List<Predicate> list = new ArrayList<>();
		if (filters == null) {
			return cb.and(list.toArray(new Predicate[0]));
		}
		for (FilterCondition filterCondition : filters) {
			String attributeName = filterCondition.getProperty();
			String joinName = filterCondition.getJoinName();
			Object value = filterCondition.getValue();

			Join join = null;
			if (StringUtils.isNotEmpty(joinName)) {
				String[] joinNames = joinName.split(">");
				for (String name : joinNames) {
					switch (filterCondition.getJoin()) {
					case FilterCondition.JOIN_LEFT:
						if (StringUtils.isNotNull(join) && StringUtils.isNotNull(value)) {
							join = join.join(name, JoinType.LEFT);
						} else {
							join = root.join(name, JoinType.LEFT);
						}
						break;
					case FilterCondition.JOIN_RIGHT:
						if (StringUtils.isNotNull(join) && StringUtils.isNotNull(value)) {
							join = join.join(name, JoinType.RIGHT);
						} else {
							join = root.join(name, JoinType.RIGHT);
						}
						break;
					case FilterCondition.JOIN_INNER:
						if (StringUtils.isNotNull(join) && StringUtils.isNotNull(value)) {
							join = join.join(name, JoinType.INNER);
						} else {
							join = root.join(name, JoinType.INNER);
						}
						break;
					default:
						break;
					}
				}
			}
            if(filterCondition.getOperator()!=null){
                switch (filterCondition.getOperator()) {
                    case FilterCondition.BLURRY:
                        String[] blurrys = attributeName.split(",");
                        List<Predicate> orPredicate = new ArrayList<>();
                        for (String s : blurrys) {
                            orPredicate.add(cb.like(root.get(s).as(String.class), "%" + value + "%"));
                        }
                        Predicate[] p = new Predicate[orPredicate.size()];
                        list.add(cb.or(orPredicate.toArray(p)));
                        break;
                    case FilterCondition.EQ:
                        list.add(cb.equal(getExpression(attributeName, join, root), value));
                        break;
                    case FilterCondition.GE:
                        list.add(cb.greaterThanOrEqualTo(getExpression(attributeName, join, root), (Comparable) value));
                        break;
                    case FilterCondition.LE:
                        list.add(cb.lessThanOrEqualTo(getExpression(attributeName, join, root), (Comparable) value));
                        break;
                    case FilterCondition.LT:
                        list.add(cb.lessThan(getExpression(attributeName, join, root), (Comparable) value));
                        break;
                    case FilterCondition.GT:
                        list.add(cb.greaterThan(getExpression(attributeName, join, root), (Comparable) value));
                        break;
                    case FilterCondition.LEFTLIKE:
                        list.add(cb.like(getExpression(attributeName, join, root).as(String.class), "%" + value));
                        break;
                    case FilterCondition.RIGHTLIKE:
                        list.add(cb.like(getExpression(attributeName, join, root).as(String.class), value + "%"));
                        break;
                    case FilterCondition.NOTLIKE:
                        list.add(cb.notLike(getExpression(attributeName, join, root).as(String.class),"%" + value + "%"));
                        break;
                    case FilterCondition.LIKE:
                        list.add(cb.like(getExpression(attributeName, join, root).as(String.class), "%" + value + "%"));
                        break;
                    case FilterCondition.IN:
                    	if(value instanceof Collection) {
                    		list.add(getExpression(attributeName, join, root).in(value));
                    	}
                    	if(value instanceof String) {
                    		String newVal = (String)value;
                    		list.add(getExpression(attributeName, join, root).in(Arrays.asList(newVal.split(","))));
                    	}
                        break;
                    case FilterCondition.NE:
                        list.add(cb.notEqual(getExpression(attributeName, join, root), value));
                        break;
                    case FilterCondition.ISNOTNULL:
                        list.add(cb.isNotNull(getExpression(attributeName, join, root)));
                        break;
                    case FilterCondition.ISNULL:
                        list.add(cb.isNull(getExpression(attributeName, join, root)));
                        break;
                    case FilterCondition.BETWEEN:
                        List<String> between = new ArrayList<>();
                        list.add(cb.between(
                                getExpression(attributeName, join, root)
                                        .as((Class<? extends Comparable>) between.get(0).getClass()),
                                (Comparable) between.get(0), (Comparable) between.get(1)));
                        break;
                    default:
                        break;
                }
            }


		}
		int size = list.size();
		return cb.and(list.toArray(new Predicate[size]));
	}

	@SuppressWarnings("unchecked")
	private static <T, R> Expression<T> getExpression(String attributeName, Join join, Root<R> root) {
		if (StringUtils.isNotNull(join)) {
			return join.get(attributeName);
		} else {
			return root.get(attributeName);
		}
	}

}
