package com.rutong.platform.client.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * @ClassName DemandResponse * @Description 需求回复
 * <AUTHOR>
 * @Date 09:34 2024/8/13
 * @Version 1.0
 **/
@Entity
@Table(name = "cli_demand_response")
@Data
public class DemandResponse extends BaseEntity {







    private String contractNumber;//合同编号
    /**
     * 客户名称
     */
    private String enterpriseName;
    private String deliveryBatchNumber; // 送达批次单号(日期)
    private String employeeName; // 员工姓名
    private String department; // 部门
    private String subDepartment; // 分部门
    private String configurationCategory; // 配置品类
    private int protocolConfigurationQuantity; // 配置数量
    private String productModelNumber; // 产品款号
    private String productColor; // 产品颜色
    private String logoPosition; // LOGO位置
    private String productSpecification; // 产品规格
    private String employeeID; // 员工企业工号
    private String rfidTagNumber; // RFID标签号
    private String productSerialNumber; // 产品序列号
    private String lockerSerialNumber; // 更衣柜序列号
    private int weeklyWashFrequency; // 周洗涤次数
    private String status; // 状态
    private String conditionInfo; // 状况
    private String remarks; // 相关备注
    private String filledBy; // 填写人
    private String processingRemarks; // 处理备注
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processingTime; // 处理时间

    private String processingUser; // 处理人

    @Transient
    private String itemJson;


}
