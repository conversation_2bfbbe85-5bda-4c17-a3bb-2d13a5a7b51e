package com.rutong.platform.wechat.repository;

import com.rutong.platform.wechat.entity.RichTextAgreement;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

// 使用@Repository注解标记该接口为Spring Data的仓库接口，用于持久化操作
@Repository
// 定义一个接口RichTextAgreementRepository，继承自JpaRepository，泛型参数为实体类RichTextAgreement和主键类型Long
public interface RichTextAgreementRepository extends JpaRepository<RichTextAgreement, Long> {
    // 定义一个方法findByType，根据类型(type)查询RichTextAgreement实体
    // 参数为String类型的type，返回值为RichTextAgreement对象
    RichTextAgreement findByType(String type);
} 