package com.rutong.platform.wms.service;

import com.rutong.platform.device.entity.DevInCabinet;
import com.rutong.platform.device.entity.DevSorting;
import org.springframework.stereotype.Service;

import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;

import java.util.Date;
import java.util.List;

@Service
public class WmsDeliveryDetailService extends BaseService<WmsDeliveryDetail> {

    public List<WmsDeliveryDetail> findByContractNumber(String contractNumber, String type, String date) {
        return dao.findByProperty(WmsDeliveryDetail.class, "contractNumber", contractNumber, "type", type, "batchNo", date, "status", 0);
    }

    public List<WmsDeliveryDetail> findByContractNumberAndProductSerialNumber(String contractNumber, String productSerialNumber,String type) {
        return dao.findByProperty(WmsDeliveryDetail.class, "contractNumber", contractNumber, "productSerialNumber", productSerialNumber,"type",type);
    }

    public List<WmsDeliveryDetail> findByRfid(String rfid, String date) {
        return dao.findByProperty(WmsDeliveryDetail.class, "rfidTagNumber", rfid, "batchNo", date);
    }
    public void updateInfo(WmsDeliveryDetail wmsDeliveryDetail) {
        dao.persist(wmsDeliveryDetail);
    }


}
