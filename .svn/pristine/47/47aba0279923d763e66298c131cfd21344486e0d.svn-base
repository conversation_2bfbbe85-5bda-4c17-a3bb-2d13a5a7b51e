package com.rutong.platform.wechat.entity;

import lombok.Data;
import javax.persistence.*;
import java.util.Date;

@Data // 使用Lombok注解，自动生成getter、setter、toString、equals和hashCode方法
@Entity // 标识这是一个JPA实体类
@Table(name = "rich_text_agreement") // 指定实体类对应的数据库表名为rich_text_agreement
public class RichTextAgreement {
    @Id // 标识该字段为主键
    @GeneratedValue(strategy = GenerationType.IDENTITY) // 主键生成策略为自增
    private Long id; // 唯一标识符

    @Column(nullable = false, unique = true, length = 32) // 指定该字段在数据库中不能为空、唯一且最大长度为32
    private String type; // type可选值：user, privacy, member

    // 使用@Column注解来配置title字段的数据库列属性
    @Column(nullable = false, length = 100)
    // 定义一个私有的String类型的成员变量title，用于存储标题信息
    private String title;

    // 使用 @Lob 注解标记该字段为大型对象，通常用于存储大文本或二进制数据
    @Lob
    // 使用 @Column 注解指定该字段在数据库中的映射，设置 nullable = false 表示该字段不能为空
    @Column(nullable = false)
    // 定义一个私有的 String 类型的字段 content，用于存储内容信息
    private String content;

    // 使用 @Column 注解指定该字段在数据库中的映射，设置 name = "create_time" 表示该字段在数据库中的列名为 create_time
    @Column(name = "create_time")
    // 定义一个私有的 Date 类型的字段 createTime，用于存储创建时间
    private Date createTime;

    // 使用 @Column 注解指定该字段在数据库中的映射，设置 name = "update_time" 表示该字段在数据库中的列名为 update_time
    @Column(name = "update_time")
    // 定义一个私有的 Date 类型的字段 updateTime，用于存储更新时间
    private Date updateTime;
} 