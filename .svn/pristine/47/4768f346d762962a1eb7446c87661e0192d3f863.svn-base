package com.rutong.framework.core.dao;

import java.io.Serializable;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

import org.hibernate.Session;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.core.jpa.FilterCondition;

public interface Dao {
	/**
	 * 当前线程的session对象，在事务结束后会自动关闭。
	 * 
	 * @return
	 */
	public Session getCurrentSession();

	/**
	 * 获取当前数据库连接的Connection对象
	 * 
	 * @return
	 */
	public Connection getConnection();

	/**
	 * 加载实体对象<如数据库不存在,返回null。 >
	 * 
	 * @param c  实体对象
	 * @param id 实体对象主键
	 * @return
	 */
	public <T, ID extends Serializable> T findById(Class<T> c, ID id);

	/**
	 * 插入或更新
	 * 
	 * @param t             实体对象
	 * @param ignoredNull   是否过滤空(缺省为true=过滤)
	 * @param includeFields 更新情况下,必须更新的字段,NULL也更新
	 * @return 返回当前操作的实体对象,可以使用当前对象对数据库进行操作
	 */
	public <T> T saveOrUpdate(T t);

	/**
	 * 根据查询对象查询总页数
	 * 
	 * @param <T>
	 * @param t
	 * @param query
	 * @return
	 */
	public <T> Long count(Class<T> t, List<FilterCondition> filters);

	/**
	 * 分页查询对象
	 * 
	 * @param <T>
	 * @param t
	 * @param gridParams
	 * @param query
	 * @return
	 */
	public <T> List<T> findAll(Class<T> t, GridParams gridParams, List<FilterCondition> filters);

	/**
	 * 根据bean类取得所有记录
	 * 
	 * @param className
	 * @param t
	 * @return 所有记录列表
	 */
	public <T> List<T> findAll(Class<T> t);

	/**
	 * 基础查询，通过实体对象类查询对象列表信息
	 * 
	 * @param t
	 * @param obj 参数 格式，usercode,admin,password,admin
	 * @return
	 */
	public <T> List<T> findByProperty(Class<T> t, Object... obj);

	/**
	 * 基础查询，通过实体对象类查询对象信息
	 * 
	 * @param t
	 * @param obj 参数 格式，usercode,admin,password,admin
	 * @return
	 */
	public <T> T findByPropertyFirst(Class<T> t, Object... obj);

	/**
	 * 基础查询
	 * 
	 * @param sql
	 * @param params 数组参数 例如： user.name=?
	 * @return List<Map>: 每一元素Map中应一行, Map中的键对应数据库中的字段名
	 */
	public List<Map<String, Object>> executeSQLQuery(String sql, Object... params);

	/**
	 * 基础查询
	 * 
	 * @param sql
	 * @param c   对象Class
	 * @DynamicUpdate</b>
	 * @param params 数组参数 例如： user.name=?
	 * @return List<T>: 返回实体对象集合
	 */
	public <T> List<T> executeSQLQuery(String sql, Class<T> c, Object... params);

}
