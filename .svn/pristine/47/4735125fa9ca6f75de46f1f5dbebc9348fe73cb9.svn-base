package com.rutong.platform.wms.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsDelivery;
import com.rutong.platform.wms.service.WmsDeliveryService;

@RestController
@RequestMapping("/wms/delivery")
@LogDesc(title = "服装交接")
public class WmsDeliveryController extends CrudController<WmsDelivery>{
	
	@Autowired
	private WmsDeliveryService deliveryService;

	@Override
	public BaseService<WmsDelivery> getService() {
		return deliveryService;
	}
	
}
