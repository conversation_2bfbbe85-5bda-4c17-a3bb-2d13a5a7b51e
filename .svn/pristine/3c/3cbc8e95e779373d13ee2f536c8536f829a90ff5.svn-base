package com.rutong.platform.client.excel;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.entity.EmployeeProductConfigurationDetail;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import org.springframework.beans.BeanUtils;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import static com.rutong.platform.client.service.EmployeeProductConfigurationService.createError;



//服务记录与查询中的Excel表格导入

public class EmployeeProductConfigurationListener implements ReadListener<EmployeeProductConfiguration> {
    private final List<EmployeeProductConfiguration> dataList = new ArrayList<>();
    private final List<String> errorList = new ArrayList<>();

    List<Map<String, String>> errors = new ArrayList<>();
    private final Map<Integer, String> columnIndexToName = new HashMap<>(); // 列索引 -> 列名
    // 1. 定义正确的模板列名列表（按顺序）
    private static final List<String> EXPECTED_COLUMNS = Arrays.asList(
            "序号", "员工姓名", "员工电话", "部门", "分部门", "工作服品类","配置品类", "产品款号", "产品颜色",
            "LOGO位置", "产品规格", "协议各品类配置数量", "员工企业工号", "工作服RFID标签编码", "RFID标签号",
            "员工产品序列号", "存衣柜协议配置型号", "协议存衣柜配置数量", "员工更衣柜序列号", "检品分拣柜号", "脏衣柜协议配置型号",
            "协议脏衣柜配置数量", "脏衣柜序列号", "协议配置品类使用月份", "协议每周作息时间", "协议每周换洗日期",
            "协议每周换洗次数", "配置产品洗涤信息", "协议单次清洗租赁费", "协议每周清洗租赁费", "协议服装结算价",
            "本月服装余值", "客方本月服装丢失次数", "客方本月服装丢失费", "月合计费用", "协议启用日期",
            "协议截止日期", "离职日期", "服务类型", "属性", "状态"
    );
    private boolean headerValidated = false; // 表头是否验证通过

    private String contractNumber;

    public Map<String, Object> backData;

    //储存根据工作服rfid标签编码分组的数据
    private final Map<String, List<EmployeeProductConfiguration>> groupedData = new HashMap<>();


    public EmployeeProductConfigurationListener(String contractNumber) {
        this.contractNumber = contractNumber;
    }
    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        // 2. 将实际列名按顺序存入列表
        List<String> actualColumns = headMap.values().stream()
                .map(ReadCellData::getStringValue)  // 转换为 String
                .filter(Objects::nonNull)          // 过滤掉 null 值
                .collect(Collectors.toList());     // 收集为 List
        // 3. 验证列数和列名
        if (actualColumns.size() != EXPECTED_COLUMNS.size()) {
            String errorMsg = String.format("模板列数不正确，应为%d列，实际%d列",
                    EXPECTED_COLUMNS.size(), actualColumns.size());
            errors.add(createError(0, "模板错误", errorMsg));
            headerValidated = false;
            return;
        }

        // 4. 逐个对比列名和顺序
        for (int i = 0; i < EXPECTED_COLUMNS.size(); i++) {
            String expected = EXPECTED_COLUMNS.get(i);
            String actual = actualColumns.get(i);
            if (!expected.equals(actual)) {
                String errorMsg = String.format("第%d列应为【%s】，实际为【%s】",
                        i + 1, expected, actual);
                errors.add(createError(0, "模板错误", errorMsg));
                headerValidated = false;
                return;
            }
        }

        // 5. 验证通过后缓存列名映射
        headMap.forEach((index, cellData) -> {
            String columnName = cellData.getStringValue();
            columnIndexToName.put(index, columnName);
        });
        headerValidated = true;
    }

    @Override
    public void invoke(EmployeeProductConfiguration data, AnalysisContext context) {
        // 6. 如果表头未通过验证，直接跳过数据处理
        if (!headerValidated) {
            return;
        }
        //按照工作服rfid标签编码进行分组
        String rfidTagNumberWork = data.getRfidTagNumberWork();
        if (rfidTagNumberWork != null && !rfidTagNumberWork.trim().isEmpty()){
            //如果有工作服rfid标签编码，则根据标签编码进行分组
            groupedData.computeIfAbsent(rfidTagNumberWork, k -> new ArrayList<>()).add(data);
        }else {
            //如果没有工作服rfid标签编码，直接添加到dataList中
            dataList.add(data);
        }
    }

    /*
    *批量处理分组的数据
    *
    */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 移除service调用，现在在controller中直接调用service
        // 避免NullPointerException
        processGroupedData();
    }

    /**
     * 处理分组的数据,生成主记录和明细记录
     */
    private void processGroupedData() {
        boolean hasConsistencyError = false;                 //定义一致性错误标志
        StringBuilder errorMsgBuilder = new StringBuilder(); //错误信息构建器
        //获取groupedData中的键值对集合
        for (Map.Entry<String, List<EmployeeProductConfiguration>> entry : groupedData.entrySet()) {
            String rfidTagNumberWork = entry.getKey();
            List<EmployeeProductConfiguration> groupData = entry.getValue();
            if (!validateGroupDataConsistency(groupData)){
                hasConsistencyError = true;
                String errorMsg = String.format("相同的工作服RFID标签编码的多行数据中除“协议配置品类使用日期、协议每周换洗日期、协议单次清洗费用、协议每周清洗租赁费、协议每周换洗次数”外，其他字段必须完全相同", rfidTagNumberWork);
                errorMsgBuilder.append(errorMsg);
            }
        }
        if (hasConsistencyError){
            errors.add(createError(0, "数据校验失败", errorMsgBuilder.toString()));
            //数据校验失败，不导入任何数据
            return;
        }
        for (Map.Entry<String, List<EmployeeProductConfiguration>> entry : groupedData.entrySet()) {
            List<EmployeeProductConfiguration> groupData = entry.getValue();
            if (groupData.size() == 1){
                // 当只有一条记录时，创建主记录并添加子表记录
                EmployeeProductConfiguration mainRecord = createMainRecord(groupData);
                List<EmployeeProductConfigurationDetail> detailRecords = createDetailRecords(groupData);
                if (!detailRecords.isEmpty()){
                    mainRecord.setTableData(JSON.toJSONString(detailRecords));
                }
                dataList.add(mainRecord);
            }else {
                EmployeeProductConfiguration mainRecord = createMainRecord(groupData);
                List<EmployeeProductConfigurationDetail> detailRecords = createDetailRecords(groupData);
                if (!detailRecords.isEmpty()){
                    mainRecord.setTableData(JSON.toJSONString(detailRecords));
                }
                dataList.add(mainRecord);
            }
        }
    }

    /**
     * 创建主记录
     * @param groupData 分组数据
     * @return 主记录
     */
    private EmployeeProductConfiguration createMainRecord(List<EmployeeProductConfiguration> groupData) {
        EmployeeProductConfiguration mainRecord = new EmployeeProductConfiguration();
        EmployeeProductConfiguration firstRecord = groupData.get(0);

        //复制基础信息
        BeanUtils.copyProperties(firstRecord,mainRecord);

        //不设置id，自动生成
        mainRecord.setId(null);
        return mainRecord;
    }


    /**
     * 创建子表明细记录
     * @param groupData 分组数据
     * @return 明细记录
     */
    private List<EmployeeProductConfigurationDetail> createDetailRecords(List<EmployeeProductConfiguration> groupData) {

        

        List<EmployeeProductConfigurationDetail> detailRecords = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        for (EmployeeProductConfiguration record : groupData) {
            EmployeeProductConfigurationDetail detail = new EmployeeProductConfigurationDetail();
            detail.setEmployeeProductConfigurationId(UUID.randomUUID().toString());

            //日期
            String monthsStr = record.getProtocolUsageMonths();
            if (monthsStr != null && !(monthsStr.length() >= 19)) { // 判断字符串长度是否小于19(yyyy-MM-dd-yyyy-MM-dd)
                try {
                    Date startDate = dateFormat.parse(monthsStr.substring(0, 10)); // 前十个字符为开始日期
                    Date endDate = dateFormat.parse(monthsStr.substring(12, 22));  // 后十个字符为结束日期
                    detail.setStartTime(startDate);
                    detail.setEndTime(endDate);
                } catch (ParseException e) {
                    //解析失败时设置默认值
                    Calendar calendar = Calendar.getInstance();
                    detail.setStartTime(calendar.getTime());
                    calendar.add(Calendar.YEAR, 1);
                    detail.setEndTime(calendar.getTime());
                }
            }else{
                //解析失败时设置默认值
                Calendar calendar = Calendar.getInstance();
                detail.setStartTime(calendar.getTime());
                calendar.add(Calendar.YEAR, 1);
                detail.setEndTime(calendar.getTime());
            }
            //周洗涤次数
            detail.setWeeklyWashDate(record.getWeeklyWashDate());
            try {
                detail.setSingleWashRentalFee(record.getSingleWashRentalFee() != null ? record.getSingleWashRentalFee() : BigDecimal.ZERO);
            } catch (NumberFormatException e) {
                detail.setSingleWashRentalFee(BigDecimal.ZERO);
            }

            detailRecords.add(detail);
        }
        return detailRecords;
    }

    /**
     * 验证同一组内的记录是否只有指定字段不同
     * @param groupData 同一组的记录列表
     */
    private boolean validateGroupDataConsistency(List<EmployeeProductConfiguration> groupData){
        //如果组内记录数量小于等于1，则认为该组内记录是唯一的，不需要验证
        if (groupData.size() <= 1) {
            return true;
        }
        //获取第一组记录
        EmployeeProductConfiguration firstRecord = groupData.get(0);
        for (int i = 1; i < groupData.size(); i++) {
            //获取当前组记录
            EmployeeProductConfiguration currentRecord = groupData.get(i);
            // 比较多个字段是否相等，除协议配置品类使用月份、协议每周换洗日期、协议单次清洗租赁费、协议每周换洗次数外、协议每周清洗租赁费，其他字段必须完全相同
            if (!Objects.equals(firstRecord.getEmployeeName(), currentRecord.getEmployeeName()) ||                                    //员工姓名
                 !Objects.equals(firstRecord.getEmployeePhone(), currentRecord.getEmployeePhone()) ||                                 //员工电话
                 !Objects.equals(firstRecord.getDepartment(), currentRecord.getDepartment()) ||                                       // 部门
                 !Objects.equals(firstRecord.getSubDepartment(), currentRecord.getSubDepartment()) ||                                 // 分部门
                 !Objects.equals(firstRecord.getWorkWearConfigurationCategory(), currentRecord.getWorkWearConfigurationCategory()) || // 工作服配置品类
                 !Objects.equals(firstRecord.getConfigurationCategory(), currentRecord.getConfigurationCategory()) ||                 // 配置品类
                 !Objects.equals(firstRecord.getProductModelNumber(), currentRecord.getProductModelNumber()) ||                       // 产品款号
                 !Objects.equals(firstRecord.getProductColor(), currentRecord.getProductColor()) ||                                   // 产品颜色
                 !Objects.equals(firstRecord.getLogoPosition(), currentRecord.getLogoPosition()) ||                                   // Logo位置
                 !Objects.equals(firstRecord.getProductSpecification(), currentRecord.getProductSpecification()) ||                   // 产品规格
                 !Objects.equals(firstRecord.getProtocolConfigurationQuantity(), currentRecord.getProtocolConfigurationQuantity()) || // 协议各品类配置数量
                 !Objects.equals(firstRecord.getEmployeeId(), currentRecord.getEmployeeId()) ||                                       // 员工企业工号
                 !Objects.equals(firstRecord.getRfidTagNumberWork(), currentRecord.getRfidTagNumberWork()) ||                         // 工作服RFID标签编码
                 !Objects.equals(firstRecord.getRfidTagNumber(), currentRecord.getRfidTagNumber()) ||                                 // RFID标签号
                 !Objects.equals(firstRecord.getProductSerialNumber(), currentRecord.getProductSerialNumber()) ||                     // 员工产品序列号
                 !Objects.equals(firstRecord.getLockerModel(), currentRecord.getLockerModel()) ||                                     // 存衣柜协议配置型号
                 !Objects.equals(firstRecord.getLockerQuantity(), currentRecord.getLockerQuantity()) ||                               // 协议存衣柜配置数量
                 !Objects.equals(firstRecord.getLockerSerialNumber(), currentRecord.getLockerSerialNumber()) ||                       // 检品分拣柜号
                 !Objects.equals(firstRecord.getSortingBoxNumber(), currentRecord.getSortingBoxNumber()) ||                           // 员工更衣柜序列号
                 !Objects.equals(firstRecord.getDirtyLockerModel(), currentRecord.getDirtyLockerModel()) ||                           // 脏衣柜协议配置型号
                 !Objects.equals(firstRecord.getDirtyLockerQuantity(), currentRecord.getDirtyLockerQuantity()) ||                     // 协议脏衣柜配置数量
                 !Objects.equals(firstRecord.getDirtyLockerSerialNumber(), currentRecord.getDirtyLockerSerialNumber()) ||             // 脏衣柜序列号
                 //!Objects.equals(firstRecord.getProtocolUsageMonths(), currentRecord.getProtocolUsageMonths()) ||                   // 协议配置品类使用月份！！！！！
                 !Objects.equals(firstRecord.getWeeklySchedule(), currentRecord.getWeeklySchedule()) ||                               // 协议每周作息时间
                 //!Objects.equals(firstRecord.getWeeklyWashDate(), currentRecord.getWeeklyWashDate()) ||                             // 协议每周换洗日期！！！！！
                 //!Objects.equals(firstRecord.getWeeklyWashFrequency(), currentRecord.getWeeklyWashFrequency()) ||                   // 协议每周换洗次数！！！！！
                 !Objects.equals(firstRecord.getProductWashInfo(), currentRecord.getProductWashInfo()) ||                             // 配置产品洗涤信息
                 //!Objects.equals(firstRecord.getSingleWashRentalFee(), currentRecord.getSingleWashRentalFee()) ||                   // 协议单次清洗租赁费！！！！！
                 //!Objects.equals(firstRecord.getWeeklyWashRentalFee(), currentRecord.getWeeklyWashRentalFee()) ||                   // 协议每周清洗租赁费！！！！！
                 !Objects.equals(firstRecord.getClothingSettlementPrice(), currentRecord.getClothingSettlementPrice()) ||             // 服装结算价格
                 !Objects.equals(firstRecord.getClothingResidualValue(), currentRecord.getClothingResidualValue()) ||                 // 服装残值
                 !Objects.equals(firstRecord.getClothingLossCount(), currentRecord.getClothingLossCount()) ||                         // 服装丢失数量
                 !Objects.equals(firstRecord.getClothingLossFee(), currentRecord.getClothingLossFee()) ||                             // 服装丢失费用
                 !Objects.equals(firstRecord.getMonthlyTotalCost(), currentRecord.getMonthlyTotalCost()) ||                           // 月总费用
                 !Objects.equals(firstRecord.getProtocolStartDate(), currentRecord.getProtocolStartDate()) ||                         // 协议开始日期
                 !Objects.equals(firstRecord.getProtocolEndDate(), currentRecord.getProtocolEndDate()) ||                             // 协议结束日期
                 !Objects.equals(firstRecord.getResignationDate(), currentRecord.getResignationDate()) ||                             // 离职日期
                 !Objects.equals(firstRecord.getAttributes(), currentRecord.getAttributes()) ||                                       // 属性
                 !Objects.equals(firstRecord.getStatus(), currentRecord.getStatus()) ||                                               // 状态
                 !Objects.equals(firstRecord.getServiceType(), currentRecord.getServiceType())) {                                     // 服务类型
                return false;
            }
        }
        return true;
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) {
        if (exception instanceof ExcelDataConvertException) {
            ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
            // 获取异常的行号和列号
            Integer rowIndex = excelDataConvertException.getRowIndex();
            Integer columnIndex = excelDataConvertException.getColumnIndex();
            // 从缓存中获取列名，如果不存在则用列号代替
            String columnName = columnIndexToName.getOrDefault(
                    excelDataConvertException.getColumnIndex(),
                    "第" + columnIndex + "列"
            );
            errors.add(createError(rowIndex, "转换错误", String.format("【%s】数据格式错误: %s", columnName, excelDataConvertException.getMessage())));

            errorList.add(String.format("第%s行第%s列数据格式错误: %s",
                    rowIndex + 1, columnIndex + 1, excelDataConvertException.getMessage()));
        } else {
            errorList.add("导入异常: " + exception.getMessage());
        }
    }

    public List<EmployeeProductConfiguration> getDataList() {
        return dataList;
    }

    public  List<Map<String, String>> getErrorMessages() {
        return errors;
    }

    public Map<String, Object> getBackData() {
        return backData;
    }
}