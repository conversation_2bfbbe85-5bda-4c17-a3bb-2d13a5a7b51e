package com.rutong.platform.sys.entity;

import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Formula;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rutong.platform.common.entity.BaseEntity;

@Entity
@Table(name = "sys_user")
@DynamicUpdate
public class SysUser extends BaseEntity{
	
	public static final String DEPTID = "deptid";
	public static final String USERNAME = "username";
	
	@Column(nullable = false,unique = true,length = 40)
	private String username;
	// 用户类型 00 管理员 01 普通用户
	@Column(nullable = false,length = 2)
	private String usertype;
	@Column(nullable = false,length = 100)
	private String nickname;
	
	private String avatar;
	/** 密码 */
	@JsonIgnore
	@Column(nullable = false,length = 200)
	private String password;
	
    @Column(name = "deptid",nullable = false)
	private String deptid;
    
    @Formula("(select sd.deptname from sys_dept sd where sd.id = deptid)")
    private String deptname;
    
    @Transient
    private String[] roleids;
	
	public String getUsertype() {
		return usertype;
	}

	public void setUsertype(String usertype) {
		this.usertype = usertype;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getNickname() {
		return nickname;
	}

	public void setNickname(String nickname) {
		this.nickname = nickname;
	}

	public String getDeptid() {
		return deptid;
	}

	public void setDeptid(String deptid) {
		this.deptid = deptid;
	}

	public String getDeptname() {
		return deptname;
	}

	public void setDeptname(String deptname) {
		this.deptname = deptname;
	}

	public String[] getRoleids() {
		return roleids;
	}

	public void setRoleids(String[] roleids) {
		this.roleids = roleids;
	}

	public String getAvatar() {
		return avatar;
	}

	public void setAvatar(String avatar) {
		this.avatar = avatar;
	}

}
