package com.rutong.framework.core.jpa;

import javax.persistence.criteria.*;

import com.rutong.framework.utils.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

public class QueryHelp {

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <R, Q> Predicate getPredicate(Root<R> root, List<FilterCondition> filters, CriteriaBuilder cb) {
		List<Predicate> list = new ArrayList<>();
		if (filters == null) {
			return cb.and(list.toArray(new Predicate[0]));
		}
		for (FilterCondition filterCondition : filters) {
			String attributeName = filterCondition.getProperty();
			Object value = filterCondition.getValue();

			Join join = null;
			if (filterCondition.getOperator() != null) {
				switch (filterCondition.getOperator()) {
				case FilterCondition.BLURRY:
					String[] blurrys = attributeName.split(",");
					List<Predicate> orPredicate = new ArrayList<>();
					for (String s : blurrys) {
						orPredicate.add(cb.like(root.get(s).as(String.class), "%" + value + "%"));
					}
					Predicate[] p = new Predicate[orPredicate.size()];
					list.add(cb.or(orPredicate.toArray(p)));
					break;
				case FilterCondition.EQ:
					if (attributeName.contains(".")) {
						System.out.println(attributeName);
						String[] joinNames = attributeName.split("\\.");
						join = root.join(joinNames[0], JoinType.LEFT);
						list.add(cb.equal(join.get(joinNames[1]), value));
					} else {
						list.add(cb.equal(root.get(attributeName), value));
					}
					break;
				case FilterCondition.GE:
					list.add(cb.greaterThanOrEqualTo(root.get(attributeName), (Comparable) value));
					break;
				case FilterCondition.LE:
					list.add(cb.lessThanOrEqualTo(root.get(attributeName), (Comparable) value));
					break;
				case FilterCondition.LT:
					list.add(cb.lessThan(root.get(attributeName), (Comparable) value));
					break;
				case FilterCondition.GT:
					list.add(cb.greaterThan(root.get(attributeName), (Comparable) value));
					break;
				case FilterCondition.LEFTLIKE:
					list.add(cb.like(root.get(attributeName).as(String.class), "%" + value));
					break;
				case FilterCondition.RIGHTLIKE:
					list.add(cb.like(root.get(attributeName).as(String.class), value + "%"));
					break;
				case FilterCondition.NOTLIKE:
					list.add(cb.notLike(root.get(attributeName).as(String.class), "%" + value + "%"));
					break;
				case FilterCondition.LIKE:
					list.add(cb.like(root.get(attributeName).as(String.class), "%" + value + "%"));
					break;
				case FilterCondition.IN:
					if (value instanceof Collection) {
						list.add(root.get(attributeName).in(value));
					}
					if (value instanceof String) {
						String newVal = (String) value;
						list.add(root.get(attributeName).in(Arrays.asList(newVal.split(","))));
					}
					break;
				case FilterCondition.NE:
					list.add(cb.notEqual(root.get(attributeName), value));
					break;
				case FilterCondition.ISNOTNULL:
					list.add(cb.isNotNull(root.get(attributeName)));
					break;
				case FilterCondition.ISNULL:
					list.add(cb.isNull(root.get(attributeName)));
					break;
				case FilterCondition.BETWEEN:
					List<String> between = new ArrayList<>();
					list.add(cb.between(
							root.get(attributeName).as((Class<? extends Comparable>) between.get(0).getClass()),
							(Comparable) between.get(0), (Comparable) between.get(1)));
					break;
				default:
					break;
				}
			}

		}
		int size = list.size();
		return cb.and(list.toArray(new Predicate[size]));
	}

//	@SuppressWarnings("unchecked")
//	private static <T, R> Expression<T> getExpression(String attributeName, Join join, Root<R> root) {
//		if (StringUtils.isNotNull(join)) {
//			return join.get(attributeName);
//		} else {
//			return root.get(attributeName);
//		}
//	}

}
