package com.rutong.platform.sys.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.rutong.platform.common.entity.TreeEntity;
import com.rutong.platform.common.pojo.TreeData;
import com.rutong.platform.common.service.TreeService;
import com.rutong.platform.sys.entity.SysDept;
import com.rutong.platform.sys.entity.SysUser;

@Service
public class SysDeptService extends TreeService<SysDept> {

	public List<TreeData> getDeptTree() {
		String sql = "select sd.id,sd.parentid,sd.deptname as title from sys_dept sd where 1=1 ";
		return dao.executeSQLQuery(sql, TreeData.class);
	}

	public boolean checkChildren(SysDept entity) {
		Long countByProperty = dao.countByProperty(SysDept.class, TreeEntity.PARENTID,entity.getId());
		return countByProperty > 0;
	}

	public boolean checkUser(SysDept entity) {
		Long countByProperty = dao.countByProperty(SysUser.class, SysUser.DEPTID,entity.getId());
		return countByProperty > 0;
	}

}
