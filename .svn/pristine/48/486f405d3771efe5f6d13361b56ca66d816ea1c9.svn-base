package com.rutong.platform.app.controller;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.platform.app.utils.AppConstan;
import com.rutong.platform.client.entity.DemandResponse;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.DemandResponseService;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.device.entity.DevInCabinet;
import com.rutong.platform.device.entity.DevSorting;
import com.rutong.platform.device.service.InCabinetService;
import com.rutong.platform.device.service.SortingService;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.sys.service.SysUserService;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.entity.WmsException;
import com.rutong.platform.wms.entity.WmsExceptionType;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import com.rutong.platform.wms.service.WmsDeliveryService;
import com.rutong.platform.wms.service.WmsExceptionService;
import com.rutong.platform.wms.service.WmsExceptionTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 客户端接口
 */
@RestController
@RequestMapping(value = "/client")
public class ClientController {

    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private WmsDeliveryService wmsDeliveryService;
    @Autowired
    private InCabinetService inCabinetService;
    @Autowired
    private WmsExceptionTypeService exceptionTypeService;
    @Autowired
    private WmsExceptionService exceptionService;
    @Autowired
    private DemandResponseService demandResponseService;

    @Autowired
    private SortingService devSortingService;
    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;

    @PostMapping(value = "/loginByPhone.do")
    public ResultBean loginByName(String phone) {
        SysUser user = sysUserService.selectUserByPhone(phone);
        if (user == null) {
            return ResultBean.error("用户不存在", 500);
        }
        return ResultBean.success(user);
    }

    /**
     * 产线分拣格口
     */
    @GetMapping("/productionLineSorting.do")
    public ResultBean productionLineSorting(String ip, String rfid) {
        List<DevSorting> devSorting = devSortingService.findByIpInfo(ip);
        EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfid(rfid);
        String lockerSerialNumber = employeeProductConfiguration.getLockerSerialNumber();//更衣柜序列号
        DevInCabinet byCabNo = inCabinetService.findByCabNo(lockerSerialNumber);
        for (DevSorting sorting : devSorting) {
            if (sorting.getDevno().equals(byCabNo.getSortingno())) {
                return ResultBean.success(byCabNo);
            }
        }
        return ResultBean.error("不是当前口分拣", -1);
    }

    /**
     * 根据rifd查询服装
     *
     * @param rfid
     * @return
     */
    @GetMapping("/getClothingByRfid.do")
    public ResultBean getClothingByRfid(String rfid) {
        EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfid(rfid);
        if (employeeProductConfiguration == null) {
            return ResultBean.error("无效标签", 500);
        }
        return ResultBean.success(employeeProductConfiguration);
    }

    /**
     * 根据rifd查询服装及存衣柜信息
     *
     * @param rfid
     * @return
     */
    @GetMapping("/getClothingAndShelfByRfid.do")
    public ResultBean getClothingAndShelfByRfid(String rfid) {
        EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfid(rfid);
        if (employeeProductConfiguration == null) {
            return ResultBean.error("无效标签", 500);
        }
        DevInCabinet cabinet = inCabinetService.findByCabNo(employeeProductConfiguration.getLockerSerialNumber());
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("employeeProduct", employeeProductConfiguration);
        List<DemandResponse> demandResponses = demandResponseService.byRfid(employeeProductConfiguration.getRfidTagNumber());
        if (demandResponses.size() > 0) {
            map.put("error", demandResponses.get(0));
        } else {
            map.put("error", null);
        }
        map.put("cabinet", cabinet);
        return ResultBean.success(map);
    }


    @PostMapping("/changeLight.do")
    public ResultBean changeLight(String ip, Integer sid) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        Map<String, Object> map = new HashMap<>();
        DevInCabinet cabinet = inCabinetService.findByHostAndSid(ip, sid);
        if (cabinet == null) {
            return ResultBean.error();
        }
        if ("可分配".equals(cabinet.getType())) {
            cabinet.setStatus("占用");
        } else {
            List<DevInCabinet> listOff = new ArrayList<>();
            List<DevInCabinet> list = inCabinetService.findBySortingnoAndCol(cabinet.getSortingno(), cabinet.getCol());
            for (DevInCabinet devInCabinet : list) {
                if ("占用".equals(devInCabinet.getStatus()) || "不可分配".equals(devInCabinet.getType())) {
                    listOff.add(devInCabinet);
                }
                List<WmsDeliveryDetail> byRfid = wmsDeliveryDetailService.findByRfid(devInCabinet.getRfid(), simpleDateFormat.format(new Date()));
                for (WmsDeliveryDetail wmsDeliveryDetail : byRfid) {
                    wmsDeliveryDetail.setStatus(1L);
                    wmsDeliveryDetailService.updateInfo(wmsDeliveryDetail);
                }
                devInCabinet.setStatus("空闲");
                devInCabinet.setRfid(null);
                inCabinetService.updateInfo(devInCabinet);
            }
            map.put("type", "off");
            map.put("data", listOff);
            return ResultBean.success(map);
        }
        try {
            EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfid(cabinet.getRfid());
            String lockerSerialNumber = employeeProductConfiguration.getLockerSerialNumber();
            String[] sss = lockerSerialNumber.split("-");
            String client = sss[0];
            String col = sss[1];
            List<String> list = new ArrayList<>();
            List<DevInCabinet> bySortingnoAndCol = inCabinetService.findBySortingnoAndCol(client, Integer.parseInt(col));
            for (DevInCabinet devInCabinet : bySortingnoAndCol) {
                list.add(devInCabinet.getCabNo());
            }
            List<String> listOK = new ArrayList<>();


            List<WmsDeliveryDetail> wmsDeliveryDetails = wmsDeliveryDetailService.findByContractNumber(employeeProductConfiguration.getContractNumber(), "(客户端)收取", simpleDateFormat.format(new Date()));
            for (WmsDeliveryDetail wmsDeliveryDetail : wmsDeliveryDetails) {
                String lockerSerialNumber1 = wmsDeliveryDetail.getLockerSerialNumber();
                if (list.contains(lockerSerialNumber1)) {
                    listOK.add(lockerSerialNumber1);
                }
            }
            boolean status = true;
            for (String s : listOK) {
                DevInCabinet byCabNo = inCabinetService.findByCabNo(s);
                if (byCabNo.getStatus().equals("空闲")) {
                    status = false;
                }
            }
            if (status) {
                List<DevInCabinet> list1 = inCabinetService.findByHostAndColAndType(ip, col, "不可分配");
                map.put("type", "on");
                map.put("data", list1);
                return ResultBean.success(map);
            }
        } catch (NullPointerException e) {
            return ResultBean.error();
        }
        return ResultBean.error();
    }

    @PostMapping("/changeLightRfid.do")
    public ResultBean changeLightRifd(String ip, Integer sid, String rfid) {
        DevInCabinet cabinet = inCabinetService.findByHostAndSid(ip, sid);
        if (cabinet == null) {
            return ResultBean.error();
        }
        cabinet.setRfid(rfid);
        cabinet.setStatus("占用");
        inCabinetService.updateInfo(cabinet);

        return ResultBean.success();
    }


    @GetMapping("/getShelfByHostSid.do")
    public ResultBean getShelfByHostSid(String host, Integer sid) {
        DevInCabinet cabinet = inCabinetService.findByHostAndSid(host, sid);
        if (cabinet == null) {
            return ResultBean.error();
        }
        if (cabinet.getType().equals(AppConstan.DEVICE_TYPE_NO)) {
            //按列号查询
            List<DevInCabinet> caList = inCabinetService.findByCol(cabinet.getCol());
            return ResultBean.success(caList);
        }
        return ResultBean.success();
    }

    /**
     * 根据服装品类查询异常类型
     *
     * @param coltype
     * @return
     */
    @GetMapping("/getExceptionByClotype.do")
    public ResultBean getExceptionByClotype(String coltype) {
        List<WmsExceptionType> exceptionTypes = exceptionTypeService.finAllByColthingType(coltype);
        return ResultBean.success(exceptionTypes);
    }

    /**
     * 提交隧道机盘点到的服装
     */
    @PostMapping("/postClothingList.do")
    public ResultBean postClothingList(@RequestList(clazz = EmployeeProductConfiguration.class) List<EmployeeProductConfiguration> proConfigurations, String username) {
        //生成一次交接记录
        wmsDeliveryService.generateBatch(proConfigurations, null,"隧道机盘点");
        return ResultBean.success();
    }

    /**
     * 提交检品异常信息
     */
    @PostMapping("/postCheckException.do")
    public ResultBean postCheckException(WmsException exception) {
        exceptionService.save(exception);
        return ResultBean.success();
    }

}
