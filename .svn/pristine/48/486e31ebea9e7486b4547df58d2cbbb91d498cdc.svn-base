package com.rutong.platform.wechat.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 配置信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigInfoDTO {
    
    /**
     * 用户姓名
     */
    private String userName;
    
    /**
     * 员工工号
     */
    private String employeeId;
    
    /**
     * 配置信息列表
     */
    private List<ConfigItemDTO> configItems;
} 