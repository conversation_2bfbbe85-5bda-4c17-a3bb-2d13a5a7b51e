package com.rutong.platform.client.service;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.rutong.framework.constant.GlobalContsant;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.*;
import com.rutong.platform.client.util.WeekdayCounter;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.sys.service.SysDeptService;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ExpenseSettlementService extends BaseService<ExpenseSettlement> {
    //    public List<ExpenseSettlement> selectByContractNumber(String contractNumber) {
//        String sql = "select * from expense_settlement where contract_number= '" + contractNumber + "' order by settlement_end ";
//        return dao.executeSQLQuery(sql, ExpenseSettlement.class);
//    }
    @Autowired
    private ExpenseSettlementService expenseSettlementService;
    @Autowired
    private ExpenseSettlementItemService expenseSettlementItemService;
    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;
    @Autowired
    private SysDeptService sysDeptService;
    @Autowired
    private DemandResponseService demandResponseService;
    @Autowired
    private ClauseInfoService clauseInfoService;
    @Autowired
    private ClientInfoService clientInfoService;

    public ExpenseSettlement findBySettlementNumber(String settlementNumber) {
        return dao.findByPropertyFirst(ExpenseSettlement.class, ExpenseSettlement.FIELD_COL_SETTLEMENT_UMBER, settlementNumber);
    }

    public ExpenseSettlement selectByContractNumber(String contractNumber) {
        String sql = "from ExpenseSettlement where contractNumber = ?0 order by settlementEnd desc";
        ExpenseSettlement expenseSettlements = dao.executeQueryFirst(sql, ExpenseSettlement.class, contractNumber);
        return expenseSettlements;
    }

    @Override
    public void save(ExpenseSettlement expenseSettlement) {
        long startTime = System.currentTimeMillis();

        // 设置基本信息
        expenseSettlement.setSettlementNumber("JS" + System.currentTimeMillis());
        expenseSettlement.setStatus("待提交");

        String contractNumber = expenseSettlement.getContractNumber();
        ClientInfo byContractNumber = clientInfoService.findByContractNumber(contractNumber);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        expenseSettlement.setEnterpriseName(byContractNumber.getEnterpriseName());
        expenseSettlement.setFiller(loginUser.getUser().getNickname());

        // 时间验证
        ExpenseSettlement expenseSettlement1 = this.selectByContractNumber(contractNumber);
        if (expenseSettlement1 == null) {
            expenseSettlement.setSettlementStart(byContractNumber.getStartDate());
        } else {
            expenseSettlement.setSettlementStart(expenseSettlement1.getSettlementEnd());
        }

        if (expenseSettlement.getSettlementEnd().before(expenseSettlement.getSettlementStart())) {
            throw new ExcelAnalysisException("结算时间不能小于最后结算时间！");
        }

        super.save(expenseSettlement);

        ClauseInfo clauseInfoData = clauseInfoService.findByContractNumber(contractNumber);

        // 预查询所有员工产品配置
        EmployeeProductConfiguration employeeProductConfigurationSelect = new EmployeeProductConfiguration();
        employeeProductConfigurationSelect.setStatus("合作中");
        employeeProductConfigurationSelect.setContractNumber(contractNumber);
        List<EmployeeProductConfiguration> byRfidAndStatus = employeeProductConfigurationService.findAllByObject(employeeProductConfigurationSelect, null);

        // 创建基础过滤器
        List<FilterCondition> baseFilters = new ArrayList<>();
        FilterCondition contractFilter = new FilterCondition();
        contractFilter.setProperty("contractNumber");
        contractFilter.setOperator(FilterCondition.EQ);
        contractFilter.setValue(contractNumber);
        baseFilters.add(contractFilter);

        FilterCondition startDateFilter = new FilterCondition();
        startDateFilter.setProperty("createTime");
        startDateFilter.setOperator(FilterCondition.GE);
        startDateFilter.setValue(expenseSettlement.getSettlementStart());
        baseFilters.add(startDateFilter);

        FilterCondition endDateFilter = new FilterCondition();
        endDateFilter.setProperty("createTime");
        endDateFilter.setOperator(FilterCondition.LE);
        endDateFilter.setValue(expenseSettlement.getSettlementEnd());
        baseFilters.add(endDateFilter);

        // 批量查询所有相关的需求响应数据
        List<DemandResponse> allDemandResponses = demandResponseService.findAll(baseFilters, null);
        Map<String, List<DemandResponse>> demandResponseMap = new HashMap<>();
        for (DemandResponse response : allDemandResponses) {
            if ("缺失衣裤".equals(response.getConditionInfo()) && "已处理".equals(response.getStatus())) {
                String rfid = response.getRfidTagNumber();
                if (!demandResponseMap.containsKey(rfid)) {
                    demandResponseMap.put(rfid, new ArrayList<>());
                }
                demandResponseMap.get(rfid).add(response);
            }
        }

        // 批量查询所有相关的配送详情数据
        List<FilterCondition> deliveryFilters = new ArrayList<>(baseFilters);
        FilterCondition typeFilter = new FilterCondition();
        typeFilter.setProperty("type");
        typeFilter.setOperator(FilterCondition.EQ);
        typeFilter.setValue(GlobalContsant.CLOTHING_WASH_TYPE_7);
        deliveryFilters.add(typeFilter);

        List<WmsDeliveryDetail> allDeliveryDetails = wmsDeliveryDetailService.findAll(deliveryFilters, null);
        Map<String, List<WmsDeliveryDetail>> deliveryDetailMap = new HashMap<>();
        for (WmsDeliveryDetail detail : allDeliveryDetails) {
            String rfid = detail.getRfidTagNumber();
            if (!deliveryDetailMap.containsKey(rfid)) {
                deliveryDetailMap.put(rfid, new ArrayList<>());
            }
            deliveryDetailMap.get(rfid).add(detail);
        }

        // 处理结算项
        List<ExpenseSettlementItem> settlementItems = new ArrayList<>();
        BigDecimal sumProduct = BigDecimal.ZERO;
        BigDecimal sumAbstersion = BigDecimal.ZERO;
        BigDecimal sumContract = BigDecimal.ZERO;
        BigDecimal sumContractCount = BigDecimal.ZERO;
        BigDecimal sumAbstersionCount = BigDecimal.ZERO;
        BigDecimal sum = BigDecimal.ZERO;

        for (EmployeeProductConfiguration employeeProductConfiguration : byRfidAndStatus) {
            ExpenseSettlementItem expenseSettlementItem = new ExpenseSettlementItem();
            BeanUtils.copyProperties(employeeProductConfiguration, expenseSettlementItem);

            String rfidTagNumber = employeeProductConfiguration.getRfidTagNumber();

            // 处理衣物缺失费用
            List<DemandResponse> demandResponses = demandResponseMap.get(rfidTagNumber);
            if (demandResponses != null && !demandResponses.isEmpty()) {
                expenseSettlementItem.setClothingLossCount(demandResponses.size());
                BigDecimal lossFee = new BigDecimal(demandResponses.size())
                        .multiply(expenseSettlementItem.getClothingSettlementPrice() != null ?
                                expenseSettlementItem.getClothingSettlementPrice() : BigDecimal.ZERO);
                expenseSettlementItem.setClothingLossFee(lossFee);
            } else {
                expenseSettlementItem.setClothingLossCount(0);
                expenseSettlementItem.setClothingLossFee(BigDecimal.ZERO);
            }

            // 处理洗涤费用
            List<WmsDeliveryDetail> deliveryDetails = deliveryDetailMap.get(rfidTagNumber);
            BigDecimal clothingCount = BigDecimal.ZERO;
            BigDecimal clothingPrice = BigDecimal.ZERO;

            if (deliveryDetails != null) {
                clothingCount = BigDecimal.valueOf(deliveryDetails.size());
                if (employeeProductConfiguration.getSingleWashRentalFee() != null) {
                    clothingPrice = employeeProductConfiguration.getSingleWashRentalFee().multiply(clothingCount);
                }
            }

            // 计算合同洗涤次数
            int contractWashCount = WeekdayCounter.countMatchingDays(
                    employeeProductConfiguration.getWeeklyWashDate(),
                    expenseSettlement.getSettlementStart(),
                    expenseSettlement.getSettlementEnd()
            );

            // 设置各项费用
            expenseSettlementItem.setCycleCleaningCharge(clothingPrice);
            expenseSettlementItem.setMonthlyExpensesReceivableCleaning(
                    employeeProductConfiguration.getSingleWashRentalFee() != null ?
                            employeeProductConfiguration.getSingleWashRentalFee().multiply(new BigDecimal(contractWashCount)) :
                            BigDecimal.ZERO
            );
            expenseSettlementItem.setMonthlyExpensesReceivableCount(new BigDecimal(contractWashCount));
            expenseSettlementItem.setProductWashInfo(clothingCount);

            // 计算应收月费用
            BigDecimal monthlyReceivable = employeeProductConfiguration.getSingleWashRentalFee() != null ?
                    new BigDecimal(contractWashCount).multiply(employeeProductConfiguration.getSingleWashRentalFee()) :
                    BigDecimal.ZERO;
            expenseSettlementItem.setMonthlyExpensesReceivable(monthlyReceivable.add(expenseSettlementItem.getClothingLossFee()));

            // 确保费用不为null
            if (expenseSettlementItem.getClothingLossFee() == null) {
                expenseSettlementItem.setClothingLossFee(BigDecimal.ZERO);
            }
            if (expenseSettlementItem.getCycleCleaningCharge() == null) {
                expenseSettlementItem.setCycleCleaningCharge(BigDecimal.ZERO);
            }

            expenseSettlementItem.setMonthlyTotalCost(expenseSettlementItem.getCycleCleaningCharge().add(expenseSettlementItem.getClothingLossFee()));
            expenseSettlementItem.setMonthlyTotalCostCount(clothingCount);

            // 累加统计值
            sumProduct = sumProduct.add(expenseSettlementItem.getClothingLossFee() != null ? expenseSettlementItem.getClothingLossFee() : BigDecimal.ZERO);
            sumAbstersion = sumAbstersion.add(expenseSettlementItem.getCycleCleaningCharge() != null ? expenseSettlementItem.getCycleCleaningCharge() : BigDecimal.ZERO);
            sum = sum.add(expenseSettlementItem.getMonthlyTotalCost() != null ? expenseSettlementItem.getMonthlyTotalCost() : BigDecimal.ZERO);
            sumContract = sumContract.add(expenseSettlementItem.getMonthlyExpensesReceivable() != null ? expenseSettlementItem.getMonthlyExpensesReceivable() : BigDecimal.ZERO);

            sumContractCount = sumContractCount.add(new BigDecimal(contractWashCount));
            sumAbstersionCount = sumAbstersionCount.add(clothingCount);

            // 设置结算项基本信息
            expenseSettlementItem.setId(null);
            expenseSettlementItem.setEsId(expenseSettlement.getId());
            expenseSettlementItem.setEnterpriseName(byContractNumber.getEnterpriseName());

            settlementItems.add(expenseSettlementItem);
        }

        // 批量插入结算项
        if (!settlementItems.isEmpty()) {
            expenseSettlementItemService.saveAll(settlementItems);
        }

        // 更新结算信息
        expenseSettlement.setTotalMonthlyPayableFeeContract(sumContract);
        expenseSettlement.setTotalMonthlyPayableFeeContractCount(sumContractCount);
        expenseSettlement.setContractPaymentDate(clauseInfoData.getPaymentDate());
        expenseSettlement.setMonthlyCleaningRentalFee(sumAbstersion);
        expenseSettlement.setMonthlyProductFee(sumProduct);
        expenseSettlement.setTotalMonthlyPayableFee(sum);
        expenseSettlement.setTotalMonthlyPayableCount(sumAbstersionCount);

        super.update(expenseSettlement);

        System.out.println("结算保存完成，耗时：" + (System.currentTimeMillis() - startTime) + "ms");
    }
}
