package com.rutong.framework.bean;

public class GridParams {

	private int current;

	private int pageSize;
	
	public GridParams(int current, int pageSize) {
		this.current = current;
		this.pageSize = pageSize;
	}

	public int getStart() {
		return (current - 1) * pageSize;
	}

	public int getCurrent() {
		return current;
	}

	public void setCurrent(int current) {
		this.current = current;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

}
