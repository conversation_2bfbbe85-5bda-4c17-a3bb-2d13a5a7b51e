package com.rutong.platform.sys.entity;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 字典数据表 sys_dict_data
 */
@Entity
@Table(name = "sys_dict_data")
public class SysDictData extends BaseEntity{
	/** 字典排序 */
	private Long dictsort;

	/** 字典标签 */
	private String dictlabel;

	/** 字典键值 */
	private String dictvalue;

	/** 字典类型 */
	private String dicttype;

	public Long getDictsort() {
		return dictsort;
	}

	public void setDictsort(Long dictsort) {
		this.dictsort = dictsort;
	}

	public String getDictlabel() {
		return dictlabel;
	}

	public void setDictlabel(String dictlabel) {
		this.dictlabel = dictlabel;
	}

	public String getDictvalue() {
		return dictvalue;
	}

	public void setDictvalue(String dictvalue) {
		this.dictvalue = dictvalue;
	}

	public String getDicttype() {
		return dicttype;
	}

	public void setDicttype(String dicttype) {
		this.dicttype = dicttype;
	}
	
}
