package com.rutong.framework.core.jpa;

public class FilterCondition {
	public static final String IN = "in";
	public static final String NOTIN = "notIn";
	public static final String LIKE = "like";
	public static final String NOTLIKE = "notLike";
	public static final String LEFTLIKE = "leftLike";
	public static final String RIGHTLIKE = "rightLike";
	public static final String EQ = "eq";
	public static final String GT = "gt";
	public static final String GE = "ge";
	public static final String LT = "lt";
	public static final String LE = "le";
	public static final String NE = "ne";
	public static final String BETWEEN = "between";
	public static final String NOTBETWEEN = "notBetween";
	public static final String ISNOT = "isNot";
	public static final String ISNULL = "isNull";
	public static final String ISNOTNULL = "isNotNull";
	public static final String BLURRY = "blurry";  //多字段模糊查询
	
	/*
	 * 简单的关联查询
	 */
	public static final String JOIN_LEFT = "left";  
	public static final String JOIN_RIGHT = "right";  
	public static final String JOIN_INNER = "inner";
	
	private String property;
	private String operator;
	private Object value;
	
	public String getProperty() {
		return property;
	}
	public void setProperty(String property) {
		this.property = property;
	}
	public String getOperator() {
		return operator;
	}
	public void setOperator(String operator) {
		this.operator = operator;
	}
	
	public Object getValue() {
		return value;
	}
	public void setValue(Object value) {
		this.value = value;
	}
}
