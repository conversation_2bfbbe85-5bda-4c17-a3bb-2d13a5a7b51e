package com.rutong.platform.wechat.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.constant.Constants;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.security.service.TokenService;
import com.rutong.framework.utils.StringUtils;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.wechat.dto.UserInfoDTO;
import com.rutong.platform.wechat.service.UserInfoService;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户信息查询控制器
 */
@RestController
@RequestMapping(value = "wechat/userinfo")
public class UserInfoController {
    private static final Logger log = LoggerFactory.getLogger(UserInfoController.class);

    @Autowired
    private UserInfoService userInfoService;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 根据当前登录用户查询用户信息
     * 支持通过请求头Authorization或token参数传递token
     *
     * @return 用户信息
     */
    @GetMapping(value = "/getCurrentUserInfo.do")
    public ResultBean getCurrentUserInfo(HttpServletRequest request, 
        @RequestParam(value = "token", required = false) String tokenParam) {
        try {
            LoginUser loginUser;

            // 优先使用URL参数中的token
            if (StringUtils.isNotEmpty(tokenParam)) {
                // 处理可能带有前缀的token
                if (tokenParam.startsWith(Constants.TOKEN_PREFIX)) {
                    tokenParam = tokenParam.replace(Constants.TOKEN_PREFIX, "");
                }
                loginUser = tokenService.getLoginUserByToken(tokenParam);
            } else {
                // 从请求头获取token并处理
                loginUser = tokenService.getLoginUser(request);
            }

            if (loginUser == null) {
                return ResultBean.error("用户未登录或token无效", 401);
            }

            // 获取当前用户的手机号
            String phone = null;
            if (loginUser.getUser() != null) {
                phone = loginUser.getUser().getPhone();
            }
            
            // 如果SysUser中没有手机号，尝试从LoginUser直接获取
            if (StringUtils.isEmpty(phone)) {
                phone = loginUser.getPhone();
            }
            
            if (StringUtils.isEmpty(phone)) {
                return ResultBean.error("手机号不能为空", 500);
            }
            
            log.info("查询当前登录用户信息, phone: {}", phone);

            // 调用服务获取用户信息
            UserInfoDTO userInfo = userInfoService.getUserInfoByPhone(phone);
            return ResultBean.success(userInfo);
        } catch (Exception e) {
            log.error("查询当前用户信息失败", e);
            return ResultBean.error(e.getMessage(), 500);
        }
    }
    
    /**
     * 直接通过token参数获取用户信息
     * 适用于apipost等测试工具直接传入token参数的场景
     *
     * @param token 用户令牌
     * @return 用户信息
     */
    @GetMapping(value = "/getUserInfoByToken.do")
    public ResultBean getUserInfoByToken(@RequestParam(value = "token", required = true) String token) {
        try {
            if (StringUtils.isEmpty(token)) {
                return ResultBean.error("token不能为空", 400);
            }
            
            // 处理可能带有前缀的token
            if (token.startsWith(Constants.TOKEN_PREFIX)) {
                token = token.replace(Constants.TOKEN_PREFIX, "");
            }
            
            // 从缓存中获取用户信息
            LoginUser loginUser = tokenService.getLoginUserByToken(token);
            
            if (loginUser == null) {
                return ResultBean.error("无效的token或token已过期", 401);
            }
            
            // 获取当前用户的手机号
            String phone = null;
            if (loginUser.getUser() != null) {
                phone = loginUser.getUser().getPhone();
            }
            
            // 如果SysUser中没有手机号，尝试从LoginUser直接获取
            if (StringUtils.isEmpty(phone)) {
                phone = loginUser.getPhone();
            }
            
            if (StringUtils.isEmpty(phone)) {
                return ResultBean.error("手机号不能为空", 500);
            }
            
            log.info("根据token查询用户信息, token: {}, phone: {}", token, phone);
            
            // 调用服务获取用户信息
            UserInfoDTO userInfo = userInfoService.getUserInfoByPhone(phone);
            return ResultBean.success(userInfo);
        } catch (Exception e) {
            log.error("根据token查询用户信息失败", e);
            return ResultBean.error(e.getMessage(), 500);
        }
    }
}