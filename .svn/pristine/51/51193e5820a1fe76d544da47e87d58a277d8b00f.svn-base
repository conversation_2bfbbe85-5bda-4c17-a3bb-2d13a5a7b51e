package com.rutong.platform.sys.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.sys.service.SysUserService;

@RestController
@RequestMapping("/system/user")
public class SysUserController extends CrudController<SysUser>{
	
	@Autowired
	private SysUserService userService;

	@Override
	public BaseService<SysUser> getService() {
		return userService;
	}

	@Override
	public ResultBean save(SysUser entity) {
		//生成加密密码
		String encryptPassword = SecurityUtils.encryptPassword(entity.getPassword());
		entity.setPassword(encryptPassword);
		return super.save(entity);
	}
	
}
