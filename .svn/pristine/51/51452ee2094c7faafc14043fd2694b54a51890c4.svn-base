-- 创建洗涤记录表
CREATE TABLE IF NOT EXISTS `washing_record` (
  `id` varchar(40) NOT NULL COMMENT '主键ID',
  `client_name` varchar(255) DEFAULT NULL COMMENT '客户名称',
  `employee_name` varchar(255) DEFAULT NULL COMMENT '员工姓名',
  `washing_time` datetime DEFAULT NULL COMMENT '洗涤时间',
  `employee_phone` varchar(255) DEFAULT NULL COMMENT '员工电话',
  `department` varchar(255) DEFAULT NULL COMMENT '部门',
  `sub_department` varchar(255) DEFAULT NULL COMMENT '分部门',
  `config_type` varchar(255) DEFAULT NULL COMMENT '配置品类',
  `product_model` varchar(255) DEFAULT NULL COMMENT '产品款号',
  `product_color` varchar(255) DEFAULT NULL COMMENT '产品颜色',
  `logo_position` varchar(255) DEFAULT NULL COMMENT 'LOGO位置',
  `product_specification` varchar(255) DEFAULT NULL COMMENT '产品规格',
  `employee_id` varchar(255) DEFAULT NULL COMMENT '员工企业工号',
  `rfid_code` varchar(255) DEFAULT NULL COMMENT '工作服RFID标签编码',
  `rfid_tag_number` varchar(255) DEFAULT NULL COMMENT 'RFID标签号',
  `create_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `owner_user_id` varchar(40) NOT NULL COMMENT '所属用户ID',
  `owner_dept_id` varchar(40) DEFAULT NULL COMMENT '所属部门ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='洗涤记录表'; 