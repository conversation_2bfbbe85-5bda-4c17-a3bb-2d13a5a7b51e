package com.rutong.platform.wms.controller;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.hutool.core.io.FileUtil;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.Global;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.excel.EmployeeProductConfigurationExcel;
import com.rutong.platform.client.util.ExportUtil;
import com.rutong.platform.wms.entity.WmsClothingExamine;
import com.rutong.platform.wms.entity.WmsClothingExcel;
import com.rutong.platform.wms.service.WmsClothingExamineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
//import org.springframework.beans.BeanUtils;
import com.rutong.framework.utils.BeanUtils;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsClothing;
import com.rutong.platform.wms.entity.WmsClothingOutbound;
import com.rutong.platform.wms.excel.ClothingExcel;
import com.rutong.platform.wms.service.WmsClothingService;

@RestController
@RequestMapping("/wms/clothing")
@LogDesc(title = "服装库存")
public class WmsClothingController extends CrudController<WmsClothing> {

    @Autowired
    private WmsClothingService wmsClothingService;

    @Autowired
    private WmsClothingExamineService wmsClothingExamineService;

    @Override
    public BaseService<WmsClothing> getService() {
        return wmsClothingService;
    }

    /**
     * 在库列表
     */
    @Override
    public PageInfo<WmsClothing> findAllByPage(GridParams gridParams,
                                               @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
                                               @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        FilterCondition filterCondition = new FilterCondition();
        filterCondition.setProperty(WmsClothing.FIELD_STATUS);
        filterCondition.setOperator(FilterCondition.EQ);
        filterCondition.setValue(String.valueOf(0));
        filters.add(filterCondition);
        return super.findAllByPage(gridParams, filters, sorts);
    }

    /**
     * 租赁列表
     *
     * @param gridParams
     * @param filters
     * @param sorts
     * @return
     */
    @PostMapping(value = "/fetchdata2.do")
    public PageInfo<WmsClothing> findAllByPage2(GridParams gridParams,
                                                @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
                                                @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        FilterCondition filterCondition = new FilterCondition();
        filterCondition.setProperty(WmsClothing.FIELD_STATUS);
        filterCondition.setOperator(FilterCondition.EQ);
        filterCondition.setValue(String.valueOf(1));
        filters.add(filterCondition);
        return super.findAllByPage(gridParams, filters, sorts);
    }

    /**
     * 批量导入服装到在库服装
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importExcel.do")
    public ResultBean importExcel(@RequestParam("file") MultipartFile file) {
        List<WmsClothing> wmsClothings = new ArrayList<WmsClothing>();
        try {
            List<ClothingExcel> dataList = EasyExcel.read(file.getInputStream(), ClothingExcel.class, null)
                    .doReadAllSync();
            for (int i = 1; i < dataList.size(); i++) {
                ClothingExcel clothingExcel = dataList.get(i);
                // 从第三行开始插入
                WmsClothing clothing = wmsClothingService.findByCloNumber(clothingExcel.getCloNumber());
                if (clothing == null) {
                    WmsClothing wmsClothing = new WmsClothing();
                    BeanUtils.copyProperties(wmsClothing, clothingExcel);
                    wmsClothing.setStatus(0);
                    wmsClothings.add(wmsClothing);
                } else {
                    clothing.setCloCount(clothing.getCloCount() + Long.parseLong(clothingExcel.getCloCount()));
                    wmsClothingService.save(clothing);

                    wmsClothingService.updateOutbound(clothing, "", Long.parseLong(clothingExcel.getCloCount()), "入库");
                }
            }
            wmsClothingService.saveAll(wmsClothings);
        } catch (IOException e) {
            e.printStackTrace();
            return ResultBean.error(e.getMessage(), 500);
        }
        return ResultBean.success();
    }

    /**
     * 批量导入服装到服装审核
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importExcelToExamine.do")
    public ResultBean importExcelToExamine(@RequestParam("file") MultipartFile file) {
        List<WmsClothingExamine> wmsClothingExamines = new ArrayList<WmsClothingExamine>();
        try {
            List<ClothingExcel> dataList = EasyExcel.read(file.getInputStream(), ClothingExcel.class, null)
                    .doReadAllSync();
            for (int i = 1; i < dataList.size(); i++) {
                ClothingExcel clothingExcel = dataList.get(i);
                // 从第三行开始插入
                WmsClothingExamine clothing = wmsClothingExamineService.findByCloNumber(clothingExcel.getCloNumber());
                if (clothing == null) {
                    WmsClothingExamine wmsClothingExamine = new WmsClothingExamine();
                    BeanUtils.copyProperties(wmsClothingExamine, clothingExcel);
                    wmsClothingExamine.setExamineStatus(0);
                    wmsClothingExamine.setStatus(0);
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    wmsClothingExamine.setExamineUser(loginUser.getUser().getNickname());
                    wmsClothingExamine.setExamineTime(new Date());
                    wmsClothingExamine.setOperationType("入库");
                    wmsClothingExamines.add(wmsClothingExamine);
                } else {
                    clothing.setCloCount(clothing.getCloCount() + Long.parseLong(clothingExcel.getCloCount()));
                    wmsClothingExamineService.save(clothing);
                    wmsClothingExamineService.updateOutbound(clothing, "", Long.parseLong(clothingExcel.getCloCount()), "入库");
                }
            }
            wmsClothingExamineService.saveAll(wmsClothingExamines);
        } catch (IOException e) {
            e.printStackTrace();
            return ResultBean.error(e.getMessage(), 500);
        }
        return ResultBean.success();
    }

    /**
     * 转到出库
     */
    @PostMapping(value = "/outbound.do")
    public ResultBean outbound(@RequestBody WmsClothing data) {
        WmsClothing wmsClothing = wmsClothingService.findById(data.getId());
        // 添加空值检查，防止空指针异常
        if (wmsClothing == null) {
            return ResultBean.error("未找到对应的服装记录", 500);
        }
        long count = wmsClothing.getCloCount() - data.getCloCount();
        if (count < 0) {
            return ResultBean.error("库存不足无法出库", 500);
        } else if (count == 0) {
            wmsClothingService.delete(wmsClothing);
        } else {
            wmsClothing.setCloCount(count);
            wmsClothingService.update(wmsClothing);
        }
        wmsClothingService.updateOutbound(wmsClothing, data.getResult(), data.getCloCount(), "出库");
        return ResultBean.success();
    }

    /**
     * 出库审核
     * @param data
     */
    @PostMapping("/outboundexamine.do")
    public ResultBean outboundexamine(@RequestBody WmsClothing data) {
        if(data == null || data.getId() == null){
            return ResultBean.error("提交审核的数据和数据的id为空", 400);
        }
        WmsClothing wmsClothing = wmsClothingService.findById(data.getId());

        // 添加空值检查，防止空指针异常
        if (wmsClothing == null) {
            return ResultBean.error("未找到对应的服装记录", 500);
        }
        WmsClothingExamine examine = new WmsClothingExamine();
        //BeanUtils.copyProperties(examine,wmsClothing);//后面是源对象
        examine.setEnterpriseName(wmsClothing.getEnterpriseName());//客户名称
        examine.setCloType(wmsClothing.getCloType());//配置品类
        examine.setCloModel(wmsClothing.getCloModel());//服装款号
        examine.setCloColor(wmsClothing.getCloColor());//服装颜色
        examine.setCloLogo(wmsClothing.getCloLogo());//Logo位置
        examine.setCloSpec(wmsClothing.getCloSpec());//产品规格
        examine.setCloCount(data.getCloCount());//提交的服装数量
        examine.setCloNumber(wmsClothing.getCloNumber());//产品序列号
        examine.setOperationType("出库");//出库
        examine.setExamineStatus(0);//未审核（初始状态）
        examine.setResult(data.getResult());//出库原因
        examine.setExamineTime(new Date());//提交出入库时间
        LoginUser loginUser = SecurityUtils.getLoginUser();//出入库操作人
        examine.setExamineUser(loginUser.getUser().getNickname());
        examine.setStatus(wmsClothing.getStatus());//状态：0在库，1租赁wmsClothing = {WmsClothing@14667}
        examine.setId(null);
        wmsClothingExamineService.save(examine);
        return ResultBean.success();
    }

    /**
     * 导出在库服装
     * @param filters
     * @return
     * @throws IOException
     */
    @PostMapping(value = "/export.do")
    public ResultBean exportClothing(@RequestList(clazz = FilterCondition.class) List<FilterCondition> filters) throws IOException{
        String fileName = "在库服装";
        fileName = ExportUtil.encodingExcelFilename(fileName);
        String folder = Global.getTempPath() + File.separator + "pio" + File.separator;
        FileUtil.touch(folder + fileName);
        List<WmsClothingExcel> list = new ArrayList<>();
        // 导出的Excel按照创建时间进行降序排序
        List<SortCondition> sorts = new ArrayList<>();
        if (sorts.isEmpty()) {
            SortCondition sortCondition = new SortCondition();
            sortCondition.setProperty("createTime");
            sortCondition.setDirection(SortCondition.SORT_DESC);
            sorts.add(sortCondition);
        }
        List<WmsClothing> allByObject = wmsClothingService.findAll(filters,sorts);
        for (WmsClothing wmsClothing : allByObject) {
            WmsClothingExcel wmsClothingExcel = new WmsClothingExcel();
            BeanUtils.copyProperties(wmsClothingExcel,wmsClothing);
            list.add(wmsClothingExcel);
        }
        EasyExcel.write(folder + fileName, WmsClothingExcel.class)
                .sheet("服装库存")
                .doWrite(list);
        return ResultBean.success(fileName);
    }
}
