package com.rutong.platform.client.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSONArray;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.FilterConditionBuilder;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.*;
import com.rutong.platform.client.service.*;
import com.rutong.platform.client.util.WeekdayCounter;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.fms.entity.PendingInvoice;
import com.rutong.platform.fms.entity.ReceiptPayable;
import com.rutong.platform.fms.service.PendingInvoiceService;
import com.rutong.platform.fms.service.ReceiptPayableService;
import com.rutong.platform.sys.service.SysDeptService;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 费用结算与查询
 */
@RestController
@RequestMapping("/client/expenseSettlement")
public class ExpenseSettlementController extends CrudController<ExpenseSettlement> {

    @Autowired
    private ExpenseSettlementService expenseSettlementService;
    @Autowired
    private ExpenseSettlementItemService expenseSettlementItemService;
    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;
    @Autowired
    private SysDeptService sysDeptService;
    @Autowired
    private DemandResponseService demandResponseService;
    @Autowired
    private ClauseInfoService clauseInfoService;
    @Autowired
    private ClientInfoService clientInfoService;
    @Autowired
    private ReceiptPayableService receiptPayableService;
    @Autowired
    private PendingInvoiceService pendingInvoiceService;

    @Override
    public BaseService<ExpenseSettlement> getService() {
        return expenseSettlementService;
    }

    @PostMapping(value = "/audit.do")
    public ResultBean audit(ExpenseSettlement entity) {
        String status = entity.getStatus();
        if ("审核通过".equals(status)) {
            ReceiptPayable receiptPayable = new ReceiptPayable();
            receiptPayable.setSettlementNumber(entity.getSettlementNumber());
            receiptPayable.setEnterpriseName(entity.getEnterpriseName());
            receiptPayable.setContractNumber(entity.getContractNumber());
            receiptPayable.setTotalMoney(entity.getReconcilingAmount());
            receiptPayable.setReceiveMoney(new BigDecimal(0));
            receiptPayable.setRemainMoney(entity.getReconcilingAmount());
            receiptPayable.setStatus("未收款");
            receiptPayableService.save(receiptPayable);


            PendingInvoice pendingInvoice = new PendingInvoice();
            pendingInvoice.setSettlementNumber(entity.getSettlementNumber());
            pendingInvoice.setEnterpriseName(entity.getEnterpriseName());
            pendingInvoice.setContractNumber(entity.getContractNumber());
            pendingInvoice.setTotalMoney(entity.getReconcilingAmount());
            pendingInvoice.setReceiveMoney(new BigDecimal(0));
            pendingInvoice.setRemainMoney(entity.getReconcilingAmount());
            pendingInvoice.setStatus("未开票");
            pendingInvoiceService.save(pendingInvoice);
        }
        return super.update(entity);
    }

    @Override
    public ResultBean update(ExpenseSettlement entity) {
        if (!StringUtils.isEmpty(entity.getReconcilingAmount())) {
            entity.setMonthlyPaidAmount(new BigDecimal(0));
            entity.setMonthlyOutstandingAmount(entity.getReconcilingAmount());
        }
        return super.update(entity);
    }


    //    @PostMapping(value = "/processing.do")
//    public ResultBean processing(DemandResponse demandResponse) {
//        DemandResponse byId1 = demandResponseService.findById(demandResponse.getId());
//        LoginUser loginUser = SecurityUtils.getLoginUser();
//        byId1.setProcessingUser(loginUser.getUser().getNickname());
//        byId1.setProcessingTime(new Date());
//        byId1.setProcessingRemarks(demandResponse.getProcessingRemarks());
//        byId1.setStatus("已处理");
//        demandResponseService.update(byId1);
//        return ResultBean.success(byId1);
//    }
    @GetMapping("/export.do")
    public void export(HttpServletResponse response) throws IOException {
        // 设置文件名和响应头
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("UTF-8");
        String fileName = URLEncoder.encode("ExpenseSettlement", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 模拟数据，实际需要从数据库中获取
        List<ExpenseSettlementItem> data = new ArrayList<>();

        // 使用 EasyExcel 写入数据到响应流
        EasyExcel.write(response.getOutputStream(), ExpenseSettlementItem.class)
                .sheet("Expense Settlement")
                .doWrite(data);
    }


}
