package com.rutong.platform.wechat.service;

import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.wechat.dto.UserInfoDTO;
import com.rutong.platform.wechat.dto.WashingInfoDTO;
import com.rutong.platform.wechat.dto.WashingItemDTO;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 洗涤信息服务类
 */
@Service
public class WashingInfoService {
    
    private static final Logger log = LoggerFactory.getLogger(WashingInfoService.class);
    
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;
    
    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;

    @Autowired
    private UserInfoService userInfoService;

    /**
     * 获取当前登录用户的洗涤信息
     * 
     * @return 用户洗涤信息
     */
    public WashingInfoDTO getWashingInfoByLoginUser() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null || loginUser.getUser() == null) {
            log.warn("用户未登录或登录信息不完整");
            return null;
        }
        
        SysUser user = loginUser.getUser();
        String phone = user.getPhone();
        log.info("当前用户手机号: {}", phone);
        
        UserInfoDTO userInfo = userInfoService.getUserInfoByPhone(phone);
        if (userInfo == null) {
            log.warn("未找到用户信息，phone: {}", phone);
            return null;
        }
        log.info("获取到用户信息: userId={}, employeeId={}, name={}", 
                 userInfo.getUserId(), userInfo.getEmployeeId(), userInfo.getName());
        
        return getWashingInfoByEmployeeId(userInfo.getEmployeeId(), userInfo.getName());
    }
    
    /**
     * 根据员工工号获取洗涤信息
     *
     * @param employeeId 员工工号
     * @param userName 用户姓名
     * @return 洗涤信息
     */
    public WashingInfoDTO getWashingInfoByEmployeeId(String employeeId, String userName) {
        if (StringUtils.isBlank(employeeId)) {
            log.warn("员工工号为空，无法查询洗涤信息");
            return null;
        }
        
        log.info("开始查询员工[{}]的洗涤信息", employeeId);
        
        List<EmployeeProductConfiguration> configs = employeeProductConfigurationService.findByEmployeeId(employeeId);
        log.info("查询到的配置数量: {}", configs != null ? configs.size() : 0);
        
        if (configs == null || configs.isEmpty()) {
            log.info("未找到员工[{}]的产品配置信息", employeeId);
            WashingInfoDTO washingInfoDTO = new WashingInfoDTO();
            washingInfoDTO.setEmployeeId(employeeId);
            washingInfoDTO.setUserName(userName);
            washingInfoDTO.setWashingItems(new ArrayList<>());
            return washingInfoDTO;
        }
        
        List<String> rfidTags = configs.stream()
                .map(EmployeeProductConfiguration::getRfidTagNumber)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        
        log.info("找到RFID标签数量: {}", rfidTags.size());
        
        WmsDeliveryDetail washDetail = new WmsDeliveryDetail();
        washDetail.setStatus(0L);
        washDetail.setType("客户端收取");
        washDetail.setEmployeeID(employeeId);
        
        List<WmsDeliveryDetail> washRecords = wmsDeliveryDetailService.findByRfidTagsAndEmployeeId(rfidTags, employeeId);
        
        Map<String, Integer> washCounts = new HashMap<>();
        for (WmsDeliveryDetail record : washRecords) {
            String rfid = record.getRfidTagNumber();
            washCounts.put(rfid, washCounts.getOrDefault(rfid, 0) + 1);
        }
        
        Map<String, List<EmployeeProductConfiguration>> categoryMap = configs.stream()
                .collect(Collectors.groupingBy(EmployeeProductConfiguration::getConfigurationCategory));
        
        log.info("产品类别数量: {}", categoryMap.size());
        
        List<WashingItemDTO> washingItems = new ArrayList<>();
        for (Map.Entry<String, List<EmployeeProductConfiguration>> entry : categoryMap.entrySet()) {
            String category = entry.getKey();
            List<EmployeeProductConfiguration> items = entry.getValue();
            
            if (items.isEmpty()) {
                continue;
            }
            
            log.info("处理产品类别[{}]，包含[{}]个配置项", category, items.size());
            
            EmployeeProductConfiguration sample = items.get(0);
            
            String weeklyWashDate = sample.getWeeklyWashDate();
            Integer weeklyWashFrequency = 0;
            
            if (weeklyWashDate != null && !weeklyWashDate.trim().isEmpty()) {
                String[] washDays = weeklyWashDate.split(",");
                weeklyWashFrequency = washDays.length;
                log.info("产品类别[{}]的换洗日期为[{}]，计算得到的每周换洗次数为[{}]", category, weeklyWashDate, weeklyWashFrequency);
            } else {
                log.warn("产品类别[{}]的每周换洗日期为空，设置每周换洗次数默认值为0", category);
            }
            
            int totalWashCount = weeklyWashFrequency * 52;
            
            int washedCount = items.stream()
                    .map(EmployeeProductConfiguration::getRfidTagNumber)
                    .filter(rfid -> washCounts.containsKey(rfid))
                    .mapToInt(washCounts::get)
                    .sum();
            
            log.info("产品类别[{}]的总洗涤次数: {}, 已洗次数: {}", category, totalWashCount, washedCount);
            
            WashingItemDTO washingItem = new WashingItemDTO();
            washingItem.setConfigurationCategory(category);
            washingItem.setProductModelNumber(sample.getProductModelNumber());
            washingItem.setWeeklyWashDate(weeklyWashDate);
            washingItem.setWeeklyWashFrequency(weeklyWashFrequency);
            washingItem.setTotalWashCount(totalWashCount);
            washingItem.setWashedCount(washedCount);
            
            washingItems.add(washingItem);
        }
        
        log.info("最终生成的洗涤项目数量: {}", washingItems.size());
        
        WashingInfoDTO washingInfoDTO = new WashingInfoDTO();
        washingInfoDTO.setEmployeeId(employeeId);
        washingInfoDTO.setUserName(userName);
        washingInfoDTO.setWashingItems(washingItems);
        return washingInfoDTO;
    }
} 