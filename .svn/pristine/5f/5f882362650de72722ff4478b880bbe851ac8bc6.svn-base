package com.rutong.platform.inventory.entity;

import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName DeliveryRecord * @Description 出库记录单
 * <AUTHOR>
 * @Date 10:47 2025/4/1
 * @Version 1.0
 **/
@Data
@Entity
@Table(name = "inventory_delivery_record")
public class DeliveryRecord extends BaseEntity {

    private String orderId;       // 单号
    private Date plannedTime;   // 计划时间
    private Date actualTime;    // 实际时间
    private BigDecimal quantity;//总数
    private Long status;        // 状态枚举
}
