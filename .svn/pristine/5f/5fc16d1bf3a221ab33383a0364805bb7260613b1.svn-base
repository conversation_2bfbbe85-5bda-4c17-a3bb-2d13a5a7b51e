package com.rutong.platform.wechat.service;

import cn.hutool.core.bean.BeanUtil;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.DemandResponse;
import com.rutong.platform.client.service.DemandResponseService;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.wechat.dto.ChatInfoDTO;
import com.rutong.platform.wechat.dto.ChatItemDTO;
import com.rutong.platform.wechat.dto.UserInfoDTO;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 聊天信息服务
 */
@Service
public class ChatInfoService {
    
    private static final Logger log = LoggerFactory.getLogger(ChatInfoService.class);
    
    @Autowired
    private DemandResponseService demandResponseService;
    
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;

    @Autowired
    private UserInfoService userInfoService;
    
    @Autowired
    private SubmitChatInfoService submitChatInfoService;
    
    /**
     * 获取用户的聊天信息
     */
    public ChatInfoDTO getChatInfoByLoginUser() {
        
//        UserInfoDTO userInfo = userInfoService.getUserInfoByPhone(loginUser.getUser().getPhone());
//        if (userInfo == null) {
//            log.warn("未找到用户信息");
//            return null;
//        }
        
        return getChatInfoByEmployeeId();
    }
    
    /**
     * 根据员工工号获取聊天信息
     */
    public ChatInfoDTO getChatInfoByEmployeeId() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        EmployeeProductConfiguration epc = employeeProductConfigurationService.findById(loginUser.getUserId());
        List<EmployeeProductConfiguration> configs = employeeProductConfigurationService.findByEmployeeId(epc.getEmployeeId());
        
        List<ChatItemDTO> chatItems = new ArrayList<>();
        for (EmployeeProductConfiguration employeeProductConfiguration:configs ) {
            ChatItemDTO chatItem = new ChatItemDTO();
            chatItem.setId(employeeProductConfiguration.getId());
            chatItem.setConfigurationCategory(employeeProductConfiguration.getConfigurationCategory());
            chatItem.setProductModelNumber(employeeProductConfiguration.getProductModelNumber());
            chatItem.setAvailableStatuses(ChatItemDTO.ALL_STATUSES);
            chatItem.setSelectedStatuses(new ArrayList<>());

            chatItems.add(chatItem);
        }

        ChatInfoDTO chatInfoDTO = new ChatInfoDTO();
        chatInfoDTO.setUserName(epc.getEmployeeName());
        chatInfoDTO.setEmployeeId(epc.getEmployeeId());
        chatInfoDTO.setChatItems(chatItems);
        
        log.debug("返回员工[{}]的沟通事项信息，包含{}个配置项", epc.getEmployeeId(), chatItems.size());
        return chatInfoDTO;
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    
    /**
     * 保存聊天信息
     * 
     * @param chatInfoDTO 聊天信息DTO
     * @return 是否保存成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveChatInfo(ChatInfoDTO chatInfoDTO) {
        try {
            if (chatInfoDTO == null) {
                log.warn("聊天信息不能为空");
                return false;
            }
            
            if (StringUtils.isBlank(chatInfoDTO.getConfigurationCategory())) {
                log.warn("配置品类不能为空");
                return false;
            }
            
            if (StringUtils.isBlank(chatInfoDTO.getProductModelNumber())) {
                log.warn("产品款号不能为空");
                return false;
            }
            
            if (chatInfoDTO.getStatus() == null) {
                chatInfoDTO.setStatus(new ArrayList<>());
            }
            
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null || loginUser.getUser() == null) {
                log.warn("用户未登录");
                return false;
            }
            
            UserInfoDTO userInfo = userInfoService.getUserInfoByPhone(loginUser.getUser().getPhone());
            if (userInfo == null || StringUtils.isBlank(userInfo.getEmployeeId())) {
                log.warn("未找到用户信息或员工工号为空");
                return false;
            }

           List<String> statusList =  chatInfoDTO.getStatus();
           for(String status : statusList) {
               SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
               EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.findById(chatInfoDTO.getId());

               DemandResponse demandRespons = new DemandResponse();
               BeanUtil.copyProperties(employeeProductConfiguration, demandRespons);

               demandRespons.setOwnerDeptId("00");
               demandRespons.setCreateBy(employeeProductConfiguration.getEmployeeId());
               demandRespons.setCreateTime(new Date());
               demandRespons.setContractNumber(employeeProductConfiguration.getContractNumber());
               demandRespons.setRemarks("");
               demandRespons.setConditionInfo(status);
               demandRespons.setFilledBy(userInfo.getName());
               demandRespons.setStatus("待处理");
               demandRespons.setId(null);
               demandRespons.setDeliveryBatchNumber(simpleDateFormat.format(new Date()));
               demandResponseService.saveUnauth(demandRespons);
            }
            
            log.info("用户[{}]提交的聊天信息已更新到产品配置: configurationCategory={}, productModelNumber={}, status={}",
                    loginUser.getUsername(),
                    chatInfoDTO.getConfigurationCategory(),
                    chatInfoDTO.getProductModelNumber(),
                    String.join(",", chatInfoDTO.getStatus()));
            
            return true;
        } catch (Exception e) {
            log.error("保存聊天信息失败: {}", e.getMessage(), e);
            e.printStackTrace();
            return false;
        }
    }
} 