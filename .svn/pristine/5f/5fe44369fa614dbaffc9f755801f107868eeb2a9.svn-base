package com.rutong.platform.wms.excel;

import javax.persistence.Column;

import com.alibaba.excel.annotation.ExcelProperty;

public class ClothingExcel {

    @ExcelProperty(index = 0)
    private String enterpriseName;  //客户名称
    @ExcelProperty(index = 1)
    private String cloType;  //品类
    @ExcelProperty(index = 2)
    private String cloModel;  //款号
    @ExcelProperty(index = 3)
    private String cloColor;  //颜色
    @ExcelProperty(index = 4)
    private String cloLogo;  //logo位置
    @ExcelProperty(index = 5)
    private String cloSpec;  //规格
    //	@ExcelProperty(index = 5)
    //private String cloRfid;  //rfid号
    @ExcelProperty(index = 6)
    private String cloCount;  //数量
    @ExcelProperty(index = 7)
    private String cloNumber;  //序列号

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getCloCount() {
        return cloCount;
    }

    public void setCloCount(String cloCount) {
        this.cloCount = cloCount;
    }

    public String getCloType() {
        return cloType;
    }

    public void setCloType(String cloType) {
        this.cloType = cloType;
    }

    public String getCloModel() {
        return cloModel;
    }

    public void setCloModel(String cloModel) {
        this.cloModel = cloModel;
    }

    public String getCloColor() {
        return cloColor;
    }

    public void setCloColor(String cloColor) {
        this.cloColor = cloColor;
    }

    public String getCloLogo() {
        return cloLogo;
    }

    public void setCloLogo(String cloLogo) {
        this.cloLogo = cloLogo;
    }

    public String getCloSpec() {
        return cloSpec;
    }

    public void setCloSpec(String cloSpec) {
        this.cloSpec = cloSpec;
    }

//    public String getCloRfid() {
//        return cloRfid;
//    }
//
//    public void setCloRfid(String cloRfid) {
//        this.cloRfid = cloRfid;
//    }

    public String getCloNumber() {
        return cloNumber;
    }

    public void setCloNumber(String cloNumber) {
        this.cloNumber = cloNumber;
    }

}
