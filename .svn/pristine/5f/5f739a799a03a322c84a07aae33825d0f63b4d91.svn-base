package com.rutong.platform.fms.entity;

/**
 * @ClassName ExpenseSettlement * @Description TODO
 * <AUTHOR>
 * @Date 15:36 2024/11/7
 * @Version 1.0
 **/

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 待开发票
 */
@Data
@Entity
@Table(name = "fms_pending_invoice")
public class PendingInvoice extends BaseEntity {
    public static final String FIELD_COL_CONTRACT_NUMBER = "contractNumber";

    /**
     * 结算编号
     */
    private String settlementNumber;
    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 客户名称
     */
    private String enterpriseName;

    /** 总金额 */
    @JsonSerialize(using= ToStringSerializer.class)
    private BigDecimal totalMoney;

    /** 已收金额 */
    @JsonSerialize(using= ToStringSerializer.class)
    private BigDecimal receiveMoney;

    /** 未收金额 */
    @JsonSerialize(using= ToStringSerializer.class)
    private BigDecimal remainMoney;

    private String status; // 状况

    private String relatedRemarks; // 相关备注

    private String filler; // 填写人
}
