package com.rutong.platform.client.service;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.*;
import com.rutong.platform.client.util.WeekdayCounter;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.sys.service.SysDeptService;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Service
public class ExpenseSettlementService extends BaseService<ExpenseSettlement> {
    //    public List<ExpenseSettlement> selectByContractNumber(String contractNumber) {
//        String sql = "select * from expense_settlement where contract_number= '" + contractNumber + "' order by settlement_end ";
//        return dao.executeSQLQuery(sql, ExpenseSettlement.class);
//    }
    @Autowired
    private ExpenseSettlementService expenseSettlementService;
    @Autowired
    private ExpenseSettlementItemService expenseSettlementItemService;
    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;
    @Autowired
    private SysDeptService sysDeptService;
    @Autowired
    private DemandResponseService demandResponseService;
    @Autowired
    private ClauseInfoService clauseInfoService;
    @Autowired
    private ClientInfoService clientInfoService;

    public ExpenseSettlement findBySettlementNumber(String settlementNumber) {
        return dao.findByPropertyFirst(ExpenseSettlement.class, ExpenseSettlement.FIELD_COL_SETTLEMENT_UMBER, settlementNumber);
    }

    public ExpenseSettlement selectByContractNumber(String contractNumber) {
        String sql = "from ExpenseSettlement where contractNumber = ?0 order by settlementEnd desc";
        ExpenseSettlement expenseSettlements = dao.executeQueryFirst(sql, ExpenseSettlement.class, contractNumber);
        return expenseSettlements;
    }

    @Override
    public void save(ExpenseSettlement expenseSettlement) {
        expenseSettlement.setSettlementNumber("JS" + System.currentTimeMillis());
        expenseSettlement.setStatus("待提交");
        ClientInfo byContractNumber = clientInfoService.findByContractNumber(expenseSettlement.getContractNumber());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        expenseSettlement.setEnterpriseName(byContractNumber.getEnterpriseName());
        expenseSettlement.setFiller(loginUser.getUser().getNickname());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        ExpenseSettlement expenseSettlement1 = this.selectByContractNumber(expenseSettlement.getContractNumber());
        if (expenseSettlement1 == null) {
            expenseSettlement.setSettlementStart(byContractNumber.getStartDate());
        } else {
            expenseSettlement.setSettlementStart(expenseSettlement1.getSettlementEnd());
        }
        if (expenseSettlement.getSettlementEnd().before(expenseSettlement.getSettlementStart())) {
            throw new ExcelAnalysisException("结算时间不能小于最后结算时间！");
        }
        super.save(expenseSettlement);
        ClauseInfo clauseInfoData = clauseInfoService.findByContractNumber(expenseSettlement.getContractNumber());


        BigDecimal sumProduct = BigDecimal.ZERO;
        BigDecimal sumAbstersion = BigDecimal.ZERO;
        BigDecimal sumContract = BigDecimal.ZERO;//合同规定洗涤金额
        BigDecimal sumContractCount = BigDecimal.ZERO;//合同规定洗涤次数
        BigDecimal sumAbstersionCount = BigDecimal.ZERO;//实际洗涤次数
        BigDecimal sum = BigDecimal.ZERO;
//        for (WmsDeliveryDetail wmsDeliveryDetail : all) {
        EmployeeProductConfiguration employeeProductConfigurationSelect = new EmployeeProductConfiguration();
        employeeProductConfigurationSelect.setStatus("合作中");
        employeeProductConfigurationSelect.setContractNumber(expenseSettlement.getContractNumber());
        List<EmployeeProductConfiguration> byRfidAndStatus = employeeProductConfigurationService.findAllByObject(employeeProductConfigurationSelect, null);
        for (EmployeeProductConfiguration employeeProductConfiguration : byRfidAndStatus) {
            ExpenseSettlementItem expenseSettlementItem = new ExpenseSettlementItem();
            BeanUtils.copyProperties(employeeProductConfiguration, expenseSettlementItem);
            List<FilterCondition> filters = new ArrayList<>();
            FilterCondition filterCondition = new FilterCondition();
            filterCondition.setProperty("contractNumber");
            filterCondition.setOperator(FilterCondition.EQ);
            filterCondition.setValue(expenseSettlement.getContractNumber());
            filters.add(filterCondition);

            FilterCondition filter2 = new FilterCondition();
            filter2.setProperty("createTime");
            filter2.setOperator(FilterCondition.GE);
            filter2.setValue(expenseSettlement.getSettlementStart());
            filters.add(filter2);

            FilterCondition filter3 = new FilterCondition();
            filter3.setProperty("createTime");
            filter3.setOperator(FilterCondition.LE);
            filter3.setValue(expenseSettlement.getSettlementEnd());
            filters.add(filter3);

            List<SortCondition> sorts = new ArrayList<>();
            SortCondition sort1 = new SortCondition();
            sort1.setProperty("contractNumber");
            sort1.setDirection(SortCondition.SORT_ASC);
            sorts.add(sort1);

            FilterCondition filter4 = new FilterCondition();
            filter4.setProperty("rfidTagNumber");
            filter4.setOperator(FilterCondition.EQ);
            filter4.setValue(expenseSettlementItem.getRfidTagNumber());
            filters.add(filter4);

            //需求回复（衣服缺失）
            List<DemandResponse> allByObject = demandResponseService.findAll(filters, null);
            if (allByObject.size() > 0) {
                DemandResponse demandResponseData = allByObject.get(0);
                if (demandResponseData.getConditionInfo().equals("缺失衣裤") && demandResponseData.getStatus().equals("已处理")) {
                    expenseSettlementItem.setClothingLossCount(allByObject.size());
                    expenseSettlementItem.setClothingLossFee(new BigDecimal(allByObject.size()).multiply(expenseSettlementItem.getClothingSettlementPrice() != null ? expenseSettlementItem.getClothingSettlementPrice() : BigDecimal.ZERO));
                }
            }else{
                expenseSettlementItem.setClothingLossCount(0);
                expenseSettlementItem.setClothingLossFee(BigDecimal.ZERO);
            }


            FilterCondition filter5 = new FilterCondition();
            filter5.setProperty("type");
            filter5.setOperator(FilterCondition.EQ);
            filter5.setValue("客户端收取");
            filters.add(filter5);
            //计算洗涤价格（按照次数）
            BigDecimal clothingPrice = BigDecimal.ZERO;
            BigDecimal clothingCount = BigDecimal.ZERO;
            List<WmsDeliveryDetail> all = wmsDeliveryDetailService.findAll(filters, sorts);
            for (WmsDeliveryDetail wmsDeliveryDetail : all) {
                clothingCount = clothingCount.add(BigDecimal.ONE);
                if (employeeProductConfiguration.getSingleWashRentalFee() != null) {
                    clothingPrice = clothingPrice.add(employeeProductConfiguration.getSingleWashRentalFee());
                }
            }
            int count = WeekdayCounter.countMatchingDays(employeeProductConfiguration.getWeeklyWashDate(), expenseSettlement.getSettlementStart(), expenseSettlement.getSettlementEnd());
            //加上丢失费用合计
            try {
                BigDecimal singleWashRentalFee = employeeProductConfiguration.getSingleWashRentalFee();
                if (singleWashRentalFee == null) {
                    singleWashRentalFee = BigDecimal.ZERO;
                }
                BigDecimal clothingLossFee = expenseSettlementItem.getClothingLossFee();
                if (clothingLossFee == null) {
                    clothingLossFee = BigDecimal.ZERO;
                }
                expenseSettlementItem.setMonthlyExpensesReceivable(new BigDecimal(count).multiply(singleWashRentalFee).add(clothingLossFee));
            } catch (Exception e) {
                System.out.println("错误：" + e.getMessage());
                e.printStackTrace();
            }
            expenseSettlementItem.setMonthlyExpensesReceivableCleaning(new BigDecimal(count).multiply(employeeProductConfiguration.getSingleWashRentalFee() != null ? employeeProductConfiguration.getSingleWashRentalFee() : BigDecimal.ZERO));
            expenseSettlementItem.setMonthlyExpensesReceivableCount(new BigDecimal(count));//合同洗涤次数
            expenseSettlementItem.setProductWashInfo(new BigDecimal(all.size()));
//                DemandResponse demandResponse = new DemandResponse();
//                demandResponse.setRfidTagNumber(employeeProductConfiguration.getRfidTagNumber());
            expenseSettlementItem.setCycleCleaningCharge(clothingPrice);
            
            // 确保 clothingLossFee 不为 null
            if (expenseSettlementItem.getClothingLossFee() == null) {
                expenseSettlementItem.setClothingLossFee(BigDecimal.ZERO);
            }
            
            // 确保 cycleCleaningCharge 不为 null
            if (expenseSettlementItem.getCycleCleaningCharge() == null) {
                expenseSettlementItem.setCycleCleaningCharge(BigDecimal.ZERO);
            }
            
            expenseSettlementItem.setMonthlyTotalCost(expenseSettlementItem.getCycleCleaningCharge().add(expenseSettlementItem.getClothingLossFee()));
            expenseSettlementItem.setMonthlyTotalCostCount(clothingCount);//实际洗涤次数
            
            // 安全地累加 sumAbstersion
            if (expenseSettlementItem.getCycleCleaningCharge() != null) {
                sumAbstersion = sumAbstersion.add(expenseSettlementItem.getCycleCleaningCharge());
            }
            
            // 安全地累加 sumProduct
            if (expenseSettlementItem.getClothingLossFee() != null) {
                sumProduct = sumProduct.add(expenseSettlementItem.getClothingLossFee());
            }
            
            // 安全地累加 sum
            if (expenseSettlementItem.getMonthlyTotalCost() != null) {
                sum = sum.add(expenseSettlementItem.getMonthlyTotalCost());
            }
            
            // 安全地累加 sumContract
            if (expenseSettlementItem.getMonthlyExpensesReceivable() != null) {
                sumContract = sumContract.add(expenseSettlementItem.getMonthlyExpensesReceivable());
            }

            sumContractCount = sumContractCount.add(new BigDecimal(count));
            sumAbstersionCount = sumAbstersionCount.add(clothingCount);

            expenseSettlementItem.setId(null);
            expenseSettlementItem.setEsId(expenseSettlement.getId());
            expenseSettlementItem.setEnterpriseName(byContractNumber.getEnterpriseName());
            expenseSettlementItemService.save(expenseSettlementItem);
        }
        expenseSettlement.setTotalMonthlyPayableFeeContract(sumContract);
        expenseSettlement.setTotalMonthlyPayableFeeContractCount(sumContractCount);
        expenseSettlement.setContractPaymentDate(clauseInfoData.getPaymentDate());
        expenseSettlement.setMonthlyCleaningRentalFee(sumAbstersion);
        expenseSettlement.setMonthlyProductFee(sumProduct);
        expenseSettlement.setTotalMonthlyPayableFee(sum);
        expenseSettlement.setTotalMonthlyPayableCount(sumAbstersionCount);
        super.update(expenseSettlement);
    }
}
