package com.rutong.framework.security.auth;

import com.rutong.framework.core.exception.ServiceException;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.security.service.PermissionService;
import com.rutong.framework.utils.StringUtils;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.sys.service.SysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.List;

public class SmsAuthenticationProviderWeChat implements AuthenticationProvider {
    private static final Logger log = LoggerFactory.getLogger(SmsAuthenticationProviderWeChat.class);
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;


    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String phoneNumber = authentication.getPrincipal().toString();
        List<EmployeeProductConfiguration> byEmployeePhone = employeeProductConfigurationService.findByEmployeePhone(phoneNumber);
        if (byEmployeePhone.size()==0) {
            log.info("登录用户：{} 不存在.", phoneNumber);
            throw new ServiceException("手机号不存在！");
        }
        EmployeeProductConfiguration employeeProductConfiguration = byEmployeePhone.get(0);
        // 根据手机号查找用户
        UserDetails userDetails = createLoginUser(employeeProductConfiguration);
        return new SmsAuthenticationTokenWeChat(userDetails, userDetails.getAuthorities());
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return SmsAuthenticationTokenWeChat.class.isAssignableFrom(authentication);
    }

    public UserDetails createLoginUser(EmployeeProductConfiguration user) {
        // 创建SysUser对象并设置必要的信息
        SysUser sysUser = new SysUser();
        // 设置手机号
        sysUser.setPhone(user.getEmployeePhone());
        // 设置用户名
        sysUser.setUsername(user.getEmployeeName());
        // 设置用户ID
        sysUser.setId(user.getId());
        
        // 创建LoginUser对象，并设置手机号
        LoginUser loginUser = new LoginUser(user.getId(), null, sysUser, null);
        // 同时设置LoginUser的phone字段作为备份
        loginUser.setPhone(user.getEmployeePhone());
        
        return loginUser;
    }
}
