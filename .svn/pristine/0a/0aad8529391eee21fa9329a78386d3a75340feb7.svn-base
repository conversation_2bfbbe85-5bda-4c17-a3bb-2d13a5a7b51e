package com.rutong.platform.sys.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysNotice;
import com.rutong.platform.sys.service.SysNoticeService;

@RestController
@RequestMapping(value = "/system/notice")
@LogDesc(title = "系统通知")
public class SysNoticeController extends CrudController<SysNotice>{
	
	@Autowired
	private SysNoticeService noticeService;

	@Override
	public BaseService<SysNotice> getService() {
		return noticeService;
	}

	/**
	 * 查询通知数量
	 * @return
	 */
	@GetMapping(value = "/queryNoticesCount.do")
	public ResultBean queryNoticesCount() {
		long count = noticeService.count();
		return ResultBean.success(count);
	}
}
