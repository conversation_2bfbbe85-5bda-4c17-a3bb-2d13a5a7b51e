package com.rutong.platform.client.controller;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.BusinessCommunication;
import com.rutong.platform.client.entity.BusinessCommunicationItem;
import com.rutong.platform.client.service.BusinessCommunicationItemService;
import com.rutong.platform.client.service.BusinessCommunicationService;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.service.SysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/client/businessCommunication")
public class BusinessCommunicationController extends CrudController<BusinessCommunication> {

    @Autowired
    private BusinessCommunicationService businessCommunicationService;
    @Autowired
    private BusinessCommunicationItemService businessCommunicationItemService;
    @Autowired
    private SysDeptService sysDeptService;

    @Override
    public BaseService<BusinessCommunication> getService() {
        return businessCommunicationService;
    }

//    @Override
//    public ResultBean save(BusinessCommunication entity) {
//        LoginUser loginUser = SecurityUtils.getLoginUser();
//
//        LocalDate now = LocalDate.now();
//        entity.setCommunicationRecord(loginUser.getUser().getDeptname());
//        entity.setCommunicationRecordId(loginUser.getUser().getDeptid());
//        entity.setMaintainer(loginUser.getUser().getNickname());
//        entity.setMaintainerId(loginUser.getUser().getId());
//        entity.setQuestionDate(now.getDayOfMonth());
//        entity.setQuestionYear(now.getYear());
//        entity.setQuestionMonth(now.getMonthValue());
//        entity.setStatus(0L);
//        businessCommunicationService.save(entity);
//        return ResultBean.success(entity);
//    }

    @PostMapping(value = "/back.do")
    public ResultBean back(BusinessCommunication entity) {
        BusinessCommunication byId = businessCommunicationService.findById(entity.getId());

        LoginUser loginUser = SecurityUtils.getLoginUser();

        BusinessCommunicationItem businessCommunicationItem = new BusinessCommunicationItem();
        businessCommunicationItem.setBackDate(new Date());
        businessCommunicationItem.setContractNumber(byId.getContractNumber());
        businessCommunicationItem.setPrimaryId(byId.getId());
        businessCommunicationItem.setReplierId(loginUser.getUserId());
        businessCommunicationItem.setReplier(loginUser.getUsername());
//        businessCommunicationItem.setReplyContent(entity.getReplyContent());
        businessCommunicationItemService.save(businessCommunicationItem);
        return ResultBean.success(businessCommunicationItem);
    }

    @PostMapping(value = "/statusOver.do")
    public ResultBean statusOver(BusinessCommunication entity) {
        BusinessCommunication byId = businessCommunicationService.findById(entity.getId());
        byId.setStatus(1L);
        BusinessCommunication update = businessCommunicationService.update(byId);
        return ResultBean.success(update);
    }

    @PostMapping(value = "/backList.do")
    public ResultBean backList(BusinessCommunication entity) {
        List<BusinessCommunicationItem> businessCommunicationItemList = businessCommunicationItemService.findByPrimaryIdList(entity.getId());
        Map<String, Object> map = new HashMap<>();
        map.put("messages", businessCommunicationItemList);
        map.put("currentUser", SecurityUtils.getLoginUser().getUser());
        return ResultBean.success(map);
    }

    @PostMapping(value = "/getUserDeptList.do")
    public ResultBean getUserDeptList(BusinessCommunication entity) {

        return ResultBean.success(businessCommunicationService.getUserDeptList());
    }
}
