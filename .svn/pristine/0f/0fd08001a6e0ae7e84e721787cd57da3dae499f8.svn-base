package com.rutong.platform.wechat.service;

import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.wechat.dto.ConfigInfoDTO;
import com.rutong.platform.wechat.dto.ConfigItemDTO;
import com.rutong.platform.wechat.dto.UserInfoDTO;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.wechat.dto.ChatInfoDTO;
import com.rutong.platform.wechat.dto.ChatItemDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 配置信息服务类
 */
@Service
public class ConfigInfoService {
    
    private static final Logger log = LoggerFactory.getLogger(ConfigInfoService.class);
    
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;

    @Autowired
    private UserInfoService userInfoService;
    
    @Autowired
    private ChatInfoService chatInfoService;
    
    /**
     * 获取当前登录用户的配置信息
     * 
     * @return 配置信息
     */
    public ConfigInfoDTO getConfigInfoByLoginUser() {
        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null || loginUser.getUser() == null) {
            log.warn("用户未登录或登录信息不完整");
            return null;
        }
        
        SysUser user = loginUser.getUser();
        String phone = user.getPhone();
        
        // 通过UserInfoService获取完整的用户信息
        UserInfoDTO userInfo = userInfoService.getUserInfoByPhone(phone);
        if (userInfo == null) {
            log.warn("未找到用户信息，phone: {}", phone);
            return null;
        }
        
        log.info("查询用户配置信息，用户ID: {}, 手机号: {}", userInfo.getUserId(), phone);
        
        // 直接使用手机号查询配置信息，而不是通过employeeId
        return getConfigInfoByPhone(phone, userInfo.getName());
    }
    
    /**
     * 根据员工手机号获取配置信息
     *
     * @param phone 员工手机号
     * @param userName 用户姓名
     * @return 配置信息
     */
    public ConfigInfoDTO getConfigInfoByPhone(String phone, String userName) {
        if (StringUtils.isBlank(phone)) {
            log.warn("员工手机号为空，无法查询配置信息");
            return null;
        }
        
        log.info("开始查询员工手机号[{}]的配置信息", phone);
        
        // 查询该手机号关联的所有配置
        List<EmployeeProductConfiguration> allConfigs = employeeProductConfigurationService.findByEmployeePhone(phone);
        
        // 过滤出合作中的配置
        List<EmployeeProductConfiguration> activeConfigs = allConfigs.stream()
            .filter(config -> "合作中".equals(config.getStatus()))
            .collect(Collectors.toList());
        
        if (activeConfigs.isEmpty()) {
            log.info("未找到员工手机号[{}]的有效配置信息", phone);
            ConfigInfoDTO configInfoDTO = new ConfigInfoDTO();
            configInfoDTO.setEmployeeId(phone); // 使用手机号代替员工工号
            configInfoDTO.setUserName(userName);
            configInfoDTO.setEmployeePhone(phone); // 设置员工手机号
            configInfoDTO.setConfigItems(new ArrayList<>());
            return configInfoDTO;
        }
        
        // 获取该手机号关联的员工工号(可能有多个，使用第一个)
        String employeeId = activeConfigs.get(0).getEmployeeId();
        
        // 将所有配置转换为ConfigItemDTO
        List<ConfigItemDTO> configItems = new ArrayList<>();
        
        for (EmployeeProductConfiguration config : activeConfigs) {
            ConfigItemDTO configItem = new ConfigItemDTO();
            configItem.setConfigurationCategory(config.getConfigurationCategory());
            configItem.setProductModelNumber(config.getProductModelNumber());
            configItem.setProductSpecification(config.getProductSpecification());
            configItem.setProductSerialNumber(config.getProductSerialNumber());
            configItem.setProtocolStartDate(config.getProtocolStartDate());
            configItem.setProtocolEndDate(config.getProtocolEndDate());
            
            configItems.add(configItem);
            
            log.debug("添加配置项: phone={}, category={}, productModelNumber={}, serialNumber={}", 
                phone, config.getConfigurationCategory(), config.getProductModelNumber(), config.getProductSerialNumber());
        }
        
        log.info("成功构建配置项列表，共{}项", configItems.size());
        
        ConfigInfoDTO configInfoDTO = new ConfigInfoDTO();
        configInfoDTO.setEmployeeId(employeeId); // 保留原来的员工工号用于兼容
        configInfoDTO.setUserName(userName);
        configInfoDTO.setEmployeePhone(phone); // 设置员工手机号
        configInfoDTO.setConfigItems(configItems);
        return configInfoDTO;
    }
    
    /**
     * 根据员工工号获取配置信息
     *
     * @param employeeId 员工工号
     * @param userName 用户姓名
     * @return 配置信息
     */
    public ConfigInfoDTO getConfigInfoByEmployeeId(String employeeId, String userName) {
        if (StringUtils.isBlank(employeeId)) {
            log.warn("员工工号为空，无法查询配置信息");
            return null;
        }
        
        log.info("开始查询员工[{}]的配置信息", employeeId);
        
        // 直接查询所有合作中的配置，不再依赖ChatInfoDTO进行筛选
        List<FilterCondition> filters = new ArrayList<>();
        
        FilterCondition employeeFilter = new FilterCondition();
        employeeFilter.setProperty("employeeId");
        employeeFilter.setOperator(FilterCondition.EQ);
        employeeFilter.setValue(employeeId);
        filters.add(employeeFilter);
        
        FilterCondition statusFilter = new FilterCondition();
        statusFilter.setProperty("status");
        statusFilter.setOperator(FilterCondition.EQ);
        statusFilter.setValue("合作中");
        filters.add(statusFilter);
        
        List<EmployeeProductConfiguration> allConfigs = employeeProductConfigurationService.findAll(filters, null);
        if (allConfigs == null || allConfigs.isEmpty()) {
            log.info("未找到员工[{}]的配置信息", employeeId);
            ConfigInfoDTO configInfoDTO = new ConfigInfoDTO();
            configInfoDTO.setEmployeeId(employeeId);
            configInfoDTO.setUserName(userName);
            configInfoDTO.setEmployeePhone(""); // 未找到配置时，设置空的员工手机号
            configInfoDTO.setConfigItems(new ArrayList<>());
            return configInfoDTO;
        }
        
        // 获取手机号（取第一条配置的手机号）
        String phone = allConfigs.get(0).getEmployeePhone();
        
        // 直接将所有配置转换为ConfigItemDTO
        List<ConfigItemDTO> configItems = new ArrayList<>();
        
        for (EmployeeProductConfiguration config : allConfigs) {
            ConfigItemDTO configItem = new ConfigItemDTO();
            configItem.setConfigurationCategory(config.getConfigurationCategory());
            configItem.setProductModelNumber(config.getProductModelNumber());
            configItem.setProductSpecification(config.getProductSpecification());
            configItem.setProductSerialNumber(config.getProductSerialNumber());
            configItem.setProtocolStartDate(config.getProtocolStartDate());
            configItem.setProtocolEndDate(config.getProtocolEndDate());
            
            configItems.add(configItem);
            
            log.debug("添加配置项: employeeId={}, category={}, productModelNumber={}, serialNumber={}", 
                employeeId, config.getConfigurationCategory(), config.getProductModelNumber(), config.getProductSerialNumber());
        }
        
        log.info("成功构建配置项列表，共{}项", configItems.size());
        
        ConfigInfoDTO configInfoDTO = new ConfigInfoDTO();
        configInfoDTO.setEmployeeId(employeeId);
        configInfoDTO.setUserName(userName);
        configInfoDTO.setEmployeePhone(phone); // 设置员工手机号
        configInfoDTO.setConfigItems(configItems);
        return configInfoDTO;
    }
} 