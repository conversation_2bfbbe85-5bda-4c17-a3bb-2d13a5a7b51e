package com.rutong.platform.wms.service;

import com.rutong.framework.utils.BeanUtils;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsClothingExamine;
import com.rutong.platform.wms.entity.WmsClothingOutbound;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class WmsClothingExamineService extends BaseService<WmsClothingExamine> {

    @Autowired
    private WmsClothingOutboundService outboundService;

    public WmsClothingExamine findByCloRfid(String cloRfid) {
        return dao.findByPropertyFirst(WmsClothingExamine.class, WmsClothingExamine.FIELD_COL_RFID, cloRfid);
    }

    public WmsClothingExamine findByCloNumber(String cloNumber) {
        return dao.findByPropertyFirst(WmsClothingExamine.class, WmsClothingExamine.FIELD_COL_NUMBER, cloNumber);
    }

    public void updateOutbound(WmsClothingExamine wmsClothingExamine, String result, Long count, String type) {
        WmsClothingOutbound wmsClothingOutbound = new WmsClothingOutbound();
        BeanUtils.copyProperties(wmsClothingOutbound, wmsClothingExamine);
        wmsClothingOutbound.setId(null);
        wmsClothingOutbound.setOutboundTime(new Date());
        wmsClothingOutbound.setOutboundResult(result);
        wmsClothingOutbound.setOperationType(type);
        wmsClothingOutbound.setCloCount(count);
        outboundService.save(wmsClothingOutbound);
    }

}
