package com.rutong.platform.client.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 租赁服装信息Excel导出模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LeaseClothingExcel {
    
    @ExcelProperty(value = "序号", index = 0)
    private Integer index;
    
    @ExcelProperty(value = "员工姓名", index = 1)
    private String employeeName;
    
    @ExcelProperty(value = "员工电话", index = 2)
    private String employeePhone;
    
    @ExcelProperty(value = "企业名称", index = 3)
    private String enterpriseName;
    
    @ExcelProperty(value = "部门", index = 4)
    private String department;
    
    @ExcelProperty(value = "分部门", index = 5)
    private String subDepartment;
    
    @ExcelProperty(value = "配置品类", index = 6)
    private String configType;
    
    @ExcelProperty(value = "产品款号", index = 7)
    private String productModel;
    
    @ExcelProperty(value = "产品颜色", index = 8)
    private String productColor;
    
    @ExcelProperty(value = "LOGO位置", index = 9)
    private String logoPosition;
    
    @ExcelProperty(value = "产品规格", index = 10)
    private String productSpecification;
    
    @ExcelProperty(value = "员工企业工号", index = 11)
    private String employeeId;
    
    @ExcelProperty(value = "RFID标签号", index = 12)
    private String rfidTagNumber;
    
    @ExcelProperty(value = "产品序列号", index = 13)
    private String productSerialNumber;
    
    @ExcelProperty(value = "存衣柜协议配置型号", index = 14)
    private String cabinetConfigModel;
    
    @ExcelProperty(value = "员工更衣柜序列号", index = 15)
    private String lockerSerialNumber;
    
    @ExcelProperty(value = "检品分拣柜号", index = 16)
    private String sortingBoxNumber;
    
    @ExcelProperty(value = "脏衣柜协议配置型号", index = 17)
    private String dirtyClothingCabinetModel;
    
    @ExcelProperty(value = "脏衣柜序列号", index = 18)
    private String dirtyClothingCabinetSerialNumber;
    
    @ExcelProperty(value = "协议配置品类使用月份", index = 19)
    private String configurationUseMonths;
    
    @ExcelProperty(value = "协议每周作息时间", index = 20)
    private String weeklyWorkSchedule;
    
    @ExcelProperty(value = "协议每周换洗日期", index = 21)
    private String weeklyWashDate;
    
    @ExcelProperty(value = "协议每周换洗次数", index = 22)
    private Integer weeklyWashCount;
    
    @ExcelProperty(value = "配置产品洗涤信息", index = 23)
    private String washingInformation;
    
    @ExcelProperty(value = "协议单次清洗租赁费", index = 24)
    private BigDecimal singleWashRentalFee;
    
    @ExcelProperty(value = "协议每周清洗租赁费", index = 25)
    private BigDecimal weeklyWashRentalFee;
    
    @ExcelProperty(value = "协议服装结算价", index = 26)
    private BigDecimal clothingSettlementPrice;
    
    @ExcelProperty(value = "本月服装余值", index = 27)
    private BigDecimal currentMonthClothingValue;
    
    @ExcelProperty(value = "协议启用日期", index = 28)
    private Date protocolStartDate;
    
    @ExcelProperty(value = "协议截止日期", index = 29)
    private Date protocolEndDate;
    
    @ExcelProperty(value = "离职日期", index = 30)
    private Date resignationDate;
    
    @ExcelProperty(value = "属性", index = 31)
    private String attribute;
    
    @ExcelProperty(value = "状态", index = 32)
    private String status;
} 