package com.rutong.platform.client.controller;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.Global;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.*;
import com.rutong.platform.client.excel.EmployeeProductConfigurationExcel;
import com.rutong.platform.client.excel.EmployeeProductConfigurationListener;
import com.rutong.platform.client.excel.ExpenseSettlementItemExcel;
import com.rutong.platform.client.service.ClientInfoService;
import com.rutong.platform.client.service.EmployeeProductConfigurationHistoryService;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.client.util.ExportUtil;
import com.rutong.platform.client.util.GenerateXML;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import org.hibernate.Session;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import com.rutong.framework.security.utils.DataAuthUtil;
import java.math.BigDecimal;

@RestController
@RequestMapping("/client/employeeProductConfiguration")
public class EmployeeProductConfigurationController extends CrudController<EmployeeProductConfiguration> {

    // 日期格式常量
    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final java.text.SimpleDateFormat dateFormatter = new java.text.SimpleDateFormat(DATE_FORMAT);

    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;

    @Autowired
    private EmployeeProductConfigurationHistoryService employeeProductConfigurationHistoryService;

    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;

    @Autowired
    private ClientInfoService clientInfoService;

    @Autowired
    private com.rutong.framework.core.dao.Dao dao;

    @Override
    public BaseService<EmployeeProductConfiguration> getService() {
        return employeeProductConfigurationService;
    }

    @Override
    public ResultBean save(EmployeeProductConfiguration entity) {
        ClientInfo byContractNumber = clientInfoService.findByContractNumber(entity.getContractNumber());
        EmployeeProductConfiguration allByObject = employeeProductConfigurationService.byRfid(entity.getRfidTagNumber());
        if (allByObject != null) {
            throw new ExcelAnalysisException("RFID标签信息：" + entity.getRfidTagNumber() + "，已存在无法重复添加");
        }
        EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byProductSerialNumber(entity.getProductSerialNumber());
        if (employeeProductConfiguration != null) {
            throw new ExcelAnalysisException("产品序列号：" + entity.getProductSerialNumber() + "，已存在无法重复添加");
        }

        entity.setEnterpriseName(byContractNumber.getEnterpriseName());
//        employeeProductConfiguration.setContractNumber(contractNumber);
        employeeProductConfigurationService.save(entity);
        return ResultBean.success(entity);
    }

    @PostMapping(value = "/fetchdataGroup.do")
    public PageInfo<EmployeeProductConfiguration> fetchdataGroup(GridParams gridParams,
                                                                 @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
                                                                 @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        if (sorts.size() == 0) {
            SortCondition sortCondition = new SortCondition();
            sortCondition.setDirection(SortCondition.SORT_DESC);
            sortCondition.setProperty(BaseEntity.FIELD_CREATE_TIME);
            sorts.add(sortCondition);
        }
        return employeeProductConfigurationService.fetchdataGroup(gridParams, filters, sorts);
    }

    @Override
    @PostMapping(value = "/fetchdata.do")
    public PageInfo<EmployeeProductConfiguration> findAllByPage(GridParams gridParams,
            @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
            @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        // 如果没有排序条件，默认按创建时间倒序排序
        if (sorts.size() == 0) {
            SortCondition sortCondition = new SortCondition();
            sortCondition.setDirection(SortCondition.SORT_DESC);
            sortCondition.setProperty(BaseEntity.FIELD_CREATE_TIME);

            // 创建一个新的排序条件对象，服务记录与查询和服装打印按照Excel表格导入顺序显示
            SortCondition sortCondition3 = new SortCondition();
            // 设置排序方向为降序
            sortCondition3.setDirection(SortCondition.SORT_DESC);
            // 设置排序属性为serialNumber
            sortCondition3.setProperty("serialNumber");
            // 将排序条件对象添加到排序条件列表中
            sorts.add(sortCondition3);
        }

        System.out.println("接收到的筛选条件: " + filters);

        // 检查是否包含日期范围筛选条件并处理，同时收集其他过滤条件
        List<FilterCondition> processedFilters = new ArrayList<>();

        // 日期处理过滤标记
        boolean dateProcessed = false;

        try {
            for (FilterCondition filter : filters) {
                // 检查是否是日期范围条件
                if (("protocolStartDate".equals(filter.getProperty()) || "protocolEndDate".equals(filter.getProperty()))
                        && "between".equals(filter.getOperator())) {

                    // 避免重复处理日期条件
                    if (dateProcessed) {
                        continue;
                    }

                    // 安全获取日期范围值
                    Object value = filter.getValue();
                    if (value == null) {
                        System.out.println("日期条件值为null，跳过处理");
                        continue;
                    }

                    // 处理数组值
                    String startDateStr = null;
                    String endDateStr = null;

                    if (value instanceof Object[]) {
                        Object[] dateRange = (Object[]) value;
                        // 安全检查数组长度
                        if (dateRange.length >= 2 && dateRange[0] != null && dateRange[1] != null) {
                            startDateStr = dateRange[0].toString().replaceAll("^\"|\"$", "");
                            endDateStr = dateRange[1].toString().replaceAll("^\"|\"$", "");
                        } else {
                            System.out.println("日期范围数组不完整或包含null值，跳过处理");
                            continue;
                        }
                    } else if (value instanceof String) {
                        // 如果是字符串，尝试解析为日期范围
                        String strValue = (String) value;
                        if (strValue.contains(",")) {
                            String[] parts = strValue.split(",");
                            if (parts.length >= 2) {
                                startDateStr = parts[0].replaceAll("^\"|\"$", "");
                                endDateStr = parts[1].replaceAll("^\"|\"$", "");
                            }
                        }
                    } else if (value instanceof List) {
                        // 如果是列表，尝试获取日期值
                        List<?> listValue = (List<?>) value;
                        if (listValue.size() >= 2 && listValue.get(0) != null && listValue.get(1) != null) {
                            startDateStr = listValue.get(0).toString().replaceAll("^\"|\"$", "");
                            endDateStr = listValue.get(1).toString().replaceAll("^\"|\"$", "");
                        }
                    }

                    // 检查日期字符串是否有效
                    if (startDateStr == null || endDateStr == null || startDateStr.isEmpty() || endDateStr.isEmpty()) {
                        System.out.println("无法解析日期范围值，跳过处理");
                        continue;
                    }

                    System.out.println("成功提取日期范围: " + startDateStr + " 至 " + endDateStr);

                    try {
                        // 将字符串日期转换为Date对象
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        Date startDate = sdf.parse(startDateStr);
                        Date endDate = sdf.parse(endDateStr);

                        // 根据筛选字段确定应用逻辑
                        String propertyName = filter.getProperty();
                        System.out.println("处理日期字段: " + propertyName);

                        if ("protocolStartDate".equals(propertyName)) {
                            // 协议启用日期筛选：查询协议启用日期在范围内的记录
                            // 协议启用日期 >= 查询开始日期
                            FilterCondition startDateGEFilter = new FilterCondition();
                            startDateGEFilter.setProperty("protocolStartDate");
                            startDateGEFilter.setOperator(FilterCondition.GE);
                            startDateGEFilter.setValue(startDate);
                            processedFilters.add(startDateGEFilter);

                            // 协议启用日期 <= 查询结束日期
                            FilterCondition startDateLEFilter = new FilterCondition();
                            startDateLEFilter.setProperty("protocolStartDate");
                            startDateLEFilter.setOperator(FilterCondition.LE);
                            startDateLEFilter.setValue(endDate);
                            processedFilters.add(startDateLEFilter);

                            System.out.println("已添加协议启用日期筛选条件: 启用日期在 " + startDate + " 和 " + endDate + " 之间");
                        } else if ("protocolEndDate".equals(propertyName)) {
                            // 协议截止日期筛选
                            // 协议截止日期 >= 查询开始日期
                            FilterCondition endDateGEFilter = new FilterCondition();
                            endDateGEFilter.setProperty("protocolEndDate");
                            endDateGEFilter.setOperator(FilterCondition.GE);
                            endDateGEFilter.setValue(startDate);
                            processedFilters.add(endDateGEFilter);

                            // 协议截止日期 <= 查询结束日期
                            FilterCondition endDateLEFilter = new FilterCondition();
                            endDateLEFilter.setProperty("protocolEndDate");
                            endDateLEFilter.setOperator(FilterCondition.LE);
                            endDateLEFilter.setValue(endDate);
                            processedFilters.add(endDateLEFilter);

                            System.out.println("已添加协议截止日期筛选条件: 截止日期在 " + startDate + " 和 " + endDate + " 之间");
                        }
                    } catch (ParseException e) {
                        System.err.println("日期格式转换错误: " + e.getMessage());
                        e.printStackTrace();
                    }

                    // 标记日期条件已处理
                    dateProcessed = true;
                } else {
                    // 对于非日期条件，直接添加到处理后的过滤器列表
                    processedFilters.add(filter);
                }
            }
        } catch (Exception e) {
            System.err.println("处理筛选条件时出错: " + e.getMessage());
            e.printStackTrace();
            // 出错时使用原始过滤条件
            processedFilters = filters;
        }

        System.out.println("处理后的筛选条件: " + processedFilters);

        // 增加数据权限
        DataAuthUtil.setDataAuth(processedFilters);

        // 使用处理后的条件进行查询
        return employeeProductConfigurationService.findAllByPage(gridParams, processedFilters, sorts);
    }

    /**
     * 将驼峰命名转换为下划线命名
     */
    private String camelToUnderscore(String input) {
        return input.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }

    /**
     * 将下划线命名转换为驼峰命名
     */
    private String underscoreToCamel(String input) {
        StringBuilder result = new StringBuilder();
        boolean nextUpper = false;

        for (int i = 0; i < input.length(); i++) {
            char currentChar = input.charAt(i);

            if (currentChar == '_') {
                nextUpper = true;
            } else {
                if (nextUpper) {
                    result.append(Character.toUpperCase(currentChar));
                    nextUpper = false;
                } else {
                    result.append(Character.toLowerCase(currentChar));
                }
            }
        }

        return result.toString();
    }

    /**
     * 使用反射设置对象属性值
     */
    private void setPropertyValue(Object obj, String propertyName, Object value) throws Exception {
        if (value == null) return;

        try {
            // 首先尝试直接获取字段
            java.lang.reflect.Field field = null;
            try {
                field = obj.getClass().getDeclaredField(propertyName);
            } catch (NoSuchFieldException e) {
                // 尝试在父类中寻找字段
                Class<?> superClass = obj.getClass().getSuperclass();
                while (superClass != null) {
                    try {
                        field = superClass.getDeclaredField(propertyName);
                        break;
                    } catch (NoSuchFieldException ex) {
                        superClass = superClass.getSuperclass();
                    }
                }

                if (field == null) {
                    throw new NoSuchFieldException("找不到属性: " + propertyName);
                }
            }

            field.setAccessible(true);

            // 转换值类型
            Class<?> fieldType = field.getType();
            Object convertedValue = convertValueToType(value, fieldType);

            // 设置属性值
            field.set(obj, convertedValue);
        } catch (Exception e) {
            System.err.println("设置属性值失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 将值转换为指定类型
     */
    private Object convertValueToType(Object value, Class<?> targetType) {
        if (value == null) return null;

        if (targetType.isAssignableFrom(value.getClass())) {
            return value;
        }

        // 字符串转换
        if (value instanceof String) {
            String strValue = (String) value;

            // 转换为Date
            if (Date.class.isAssignableFrom(targetType)) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return sdf.parse(strValue);
                } catch (ParseException e) {
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        return sdf.parse(strValue);
                    } catch (ParseException ex) {
                        return null;
                    }
                }
            }

            // 转换为数值类型
            if (Integer.class.isAssignableFrom(targetType) || int.class.equals(targetType)) {
                return Integer.parseInt(strValue);
            }
            if (Long.class.isAssignableFrom(targetType) || long.class.equals(targetType)) {
                return Long.parseLong(strValue);
            }
            if (Double.class.isAssignableFrom(targetType) || double.class.equals(targetType)) {
                return Double.parseDouble(strValue);
            }
            if (Float.class.isAssignableFrom(targetType) || float.class.equals(targetType)) {
                return Float.parseFloat(strValue);
            }
            if (BigDecimal.class.isAssignableFrom(targetType)) {
                return new BigDecimal(strValue);
            }
            if (Boolean.class.isAssignableFrom(targetType) || boolean.class.equals(targetType)) {
                return Boolean.parseBoolean(strValue);
            }
        }

        // 处理数值类型之间的转换
        if (value instanceof Number) {
            Number numValue = (Number) value;

            if (Integer.class.isAssignableFrom(targetType) || int.class.equals(targetType)) {
                return numValue.intValue();
            }
            if (Long.class.isAssignableFrom(targetType) || long.class.equals(targetType)) {
                return numValue.longValue();
            }
            if (Double.class.isAssignableFrom(targetType) || double.class.equals(targetType)) {
                return numValue.doubleValue();
            }
            if (Float.class.isAssignableFrom(targetType) || float.class.equals(targetType)) {
                return numValue.floatValue();
            }
            if (BigDecimal.class.isAssignableFrom(targetType)) {
                return new BigDecimal(numValue.toString());
            }
        }

        // 尝试使用toString()方法
        return value.toString();
    }

    @PostMapping(value = "/update.do")
    public ResultBean update(EmployeeProductConfiguration entity) {
        // 获取当前实体
        EmployeeProductConfiguration byId = employeeProductConfigurationService.findById(entity.getId());

        // 添加调试信息
        System.out.println("正在检查字段变更：");

        // 检查所有可能的字段变更并记录到历史表
        checkAndRecordFieldChange(entity, byId, "employeeName", "员工姓名");
        checkAndRecordFieldChange(entity, byId, "employeePhone", "员工电话");
        checkAndRecordFieldChange(entity, byId, "department", "部门");
        checkAndRecordFieldChange(entity, byId, "subDepartment", "分部门");
        checkAndRecordFieldChange(entity, byId, "configurationCategory", "配置品类");
        checkAndRecordFieldChange(entity, byId, "productModelNumber", "产品款号");
        checkAndRecordFieldChange(entity, byId, "productColor", "产品颜色");
        checkAndRecordFieldChange(entity, byId, "logoPosition", "LOGO位置");
        checkAndRecordFieldChange(entity, byId, "productSpecification", "产品规格");
        checkAndRecordFieldChange(entity, byId, "protocolConfigurationQuantity", "协议各品类配置数量");
        checkAndRecordFieldChange(entity, byId, "employeeId", "员工企业工号");
        checkAndRecordFieldChange(entity, byId, "rfidTagNumberWork", "工作服RFID标签编码");
        checkAndRecordFieldChange(entity, byId, "rfidTagNumber", "RFID标签号");
        checkAndRecordFieldChange(entity, byId, "productSerialNumber", "产品序列号");
        checkAndRecordFieldChange(entity, byId, "lockerModel", "存衣柜协议配置型号");
        checkAndRecordFieldChange(entity, byId, "lockerQuantity", "协议存衣柜配置数量");
        checkAndRecordFieldChange(entity, byId, "lockerSerialNumber", "员工更衣柜序列号");
        checkAndRecordFieldChange(entity, byId, "sortingBoxNumber", "检品分拣柜号");
        checkAndRecordFieldChange(entity, byId, "dirtyLockerModel", "脏衣柜协议配置型号");
        checkAndRecordFieldChange(entity, byId, "dirtyLockerQuantity", "协议脏衣柜配置数量");
        checkAndRecordFieldChange(entity, byId, "dirtyLockerSerialNumber", "脏衣柜序列号");
        checkAndRecordFieldChange(entity, byId, "protocolUsageMonths", "协议配置品类使用月份");
        checkAndRecordFieldChange(entity, byId, "weeklySchedule", "协议每周作息时间");
        checkAndRecordFieldChange(entity, byId, "weeklyWashDate", "协议每周换洗日期");
        checkAndRecordFieldChange(entity, byId, "weeklyWashFrequency", "协议每周换洗次数");
        checkAndRecordFieldChange(entity, byId, "productWashInfo", "配置产品洗涤信息");
        checkAndRecordFieldChange(entity, byId, "singleWashRentalFee", "协议单次清洗租赁费");
        checkAndRecordFieldChange(entity, byId, "weeklyWashRentalFee", "协议每周清洗租赁费");
        checkAndRecordFieldChange(entity, byId, "clothingSettlementPrice", "协议服装结算价");
        checkAndRecordFieldChange(entity, byId, "clothingResidualValue", "本月服装余值");
        checkAndRecordFieldChange(entity, byId, "clothingLossCount", "客方本月服装丢失次数");
        checkAndRecordFieldChange(entity, byId, "clothingLossFee", "客方本月服装丢失费");
        checkAndRecordFieldChange(entity, byId, "monthlyTotalCost", "月合计费用");
        checkAndRecordFieldChange(entity, byId, "protocolStartDate", "协议启用日期");
        checkAndRecordFieldChange(entity, byId, "protocolEndDate", "协议截止日期");
        checkAndRecordFieldChange(entity, byId, "resignationDate", "离职日期");
        checkAndRecordFieldChange(entity, byId, "attributes", "属性");
        checkAndRecordFieldChange(entity, byId, "status", "状态");
        checkAndRecordFieldChange(entity, byId, "serviceType", "服务类型");
        checkAndRecordFieldChange(entity, byId, "enterpriseName", "客户名称");
        checkAndRecordFieldChange(entity, byId, "contractNumber", "合同编号");

        // 使用service层的update方法更新实体
        EmployeeProductConfiguration updatedEntity = employeeProductConfigurationService.update(entity);
        System.out.println("实体更新完成");

        return ResultBean.success(updatedEntity);
    }

    /**
     * 检查字段变更并记录到历史表
     * @param newEntity 新实体
     * @param oldEntity 旧实体
     * @param fieldName 字段名
     * @param displayName 显示名称
     */
    private void checkAndRecordFieldChange(EmployeeProductConfiguration newEntity, EmployeeProductConfiguration oldEntity,
                                          String fieldName, String displayName) {
        try {
            // 使用反射获取字段值
            String methodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            System.out.println("尝试调用方法: " + methodName);

            // 检查方法是否存在
            java.lang.reflect.Method getMethod = null;
            Object newValue = null;
            Object oldValue = null;

            try {
                getMethod = EmployeeProductConfiguration.class.getMethod(methodName);

                // 调用get方法获取值
                newValue = getMethod.invoke(newEntity);
                oldValue = getMethod.invoke(oldEntity);
            } catch (NoSuchMethodException e) {
                System.out.println("方法 " + methodName + " 不存在，尝试使用字段直接访问");

                // 如果方法不存在，尝试直接访问字段
                try {
                    // 尝试在DTO中查找字段
                    java.lang.reflect.Field field = null;
                    try {
                        field = EmployeeProductConfiguration.class.getDeclaredField(fieldName);
                    } catch (NoSuchFieldException ex) {
                        // 如果在当前类中找不到，尝试在父类中查找
                        try {
                            field = EmployeeProductConfiguration.class.getSuperclass().getDeclaredField(fieldName);
                        } catch (NoSuchFieldException ex2) {
                            throw new Exception("字段 " + fieldName + " 在实体类及其父类中都不存在");
                        }
                    }

                    field.setAccessible(true);
                    newValue = field.get(newEntity);
                    oldValue = field.get(oldEntity);
                } catch (Exception fieldException) {
                    System.out.println("字段访问异常: " + fieldException.getMessage());
                    fieldException.printStackTrace();

                    // 尝试使用替代方法名
                    String alternativeMethodName = null;

                    // 尝试可能的别名
                    if (fieldName.equals("employeeId")) {
                        alternativeMethodName = "getEmployeeNumber";
                    } else if (fieldName.equals("protocolUsageMonths")) {
                        alternativeMethodName = "getLaundryUsingMonth";
                    } else if (fieldName.equals("weeklySchedule")) {
                        alternativeMethodName = "getWeekdayUsingInfo";
                    } else if (fieldName.equals("weeklyWashDate")) {
                        alternativeMethodName = "getWeekWashingDays";
                    } else if (fieldName.equals("weeklyWashFrequency")) {
                        alternativeMethodName = "getWeeklyChangeTimes";
                    } else if (fieldName.equals("singleWashRentalFee")) {
                        alternativeMethodName = "getOnceWashingFee";
                    } else if (fieldName.equals("weeklyWashRentalFee")) {
                        alternativeMethodName = "getWeeklyCleaningFee";
                    } else if (fieldName.equals("lockerSerialNumber")) {
                        alternativeMethodName = "getClothesNumber";
                    } else if (fieldName.equals("attributes")) {
                        alternativeMethodName = "getAttributeSet";
                    }

                    if (alternativeMethodName != null) {
                        try {
                            System.out.println("尝试使用替代方法: " + alternativeMethodName);
                            java.lang.reflect.Method altMethod = EmployeeProductConfiguration.class.getMethod(alternativeMethodName);
                            newValue = altMethod.invoke(newEntity);
                            oldValue = altMethod.invoke(oldEntity);
                        } catch (Exception altEx) {
                            System.out.println("替代方法调用失败: " + altEx.getMessage());
                            return;
                        }
                    } else {
                        return;
                    }
                }
            }

            // 特殊处理日期类型
            if (newValue instanceof Date || oldValue instanceof Date) {
                if (newValue == null && oldValue == null) {
                    // 两个都为null，视为没变化
                    return;
                }

                if (newValue == null || oldValue == null) {
                    // 一个为null，一个不为null，视为有变化
                    String newValueStr = newValue == null ? "" : dateFormatter.format(newValue);
                    String oldValueStr = oldValue == null ? "" : dateFormatter.format(oldValue);

                    recordChange(oldEntity, displayName, oldValueStr, newValueStr);
                    return;
                }

                // 两个都不为null，比较日期内容
                Date newDate = (Date)newValue;
                Date oldDate = (Date)oldValue;

                // 如果新日期不等于旧日期
                if (!newDate.equals(oldDate)) {
                    // 将新日期格式化为字符串
                    String newValueStr = dateFormatter.format(newDate);
                    // 将旧日期格式化为字符串
                    String oldValueStr = dateFormatter.format(oldDate);

                    // 记录日期变化
                    recordChange(oldEntity, displayName, oldValueStr, newValueStr);
                }
                return;
            }

            // 特殊处理数字类型
            if (newValue instanceof Number || oldValue instanceof Number) {
                if (newValue == null && oldValue == null) {
                    // 两个都为null，视为没变化
                    return;
                }

                String newValueStr = newValue == null ? "0" : String.valueOf(newValue);
                String oldValueStr = oldValue == null ? "0" : String.valueOf(oldValue);

                // 去除尾部的.0
                if (newValueStr.endsWith(".0")) {
                    newValueStr = newValueStr.substring(0, newValueStr.length() - 2);
                }
                if (oldValueStr.endsWith(".0")) {
                    oldValueStr = oldValueStr.substring(0, oldValueStr.length() - 2);
                }

                System.out.println("检查数字字段 [" + displayName + "]: 原值=[" + oldValueStr + "], 新值=[" + newValueStr + "]");

                if (!newValueStr.equals(oldValueStr)) {
                    recordChange(oldEntity, displayName, oldValueStr, newValueStr);
                }
                return;
            }

            // 默认字符串比较
            String newValueStr = newValue == null ? "" : String.valueOf(newValue);
            String oldValueStr = oldValue == null ? "" : String.valueOf(oldValue);

            System.out.println("检查字段 [" + displayName + "]: 原值=[" + oldValueStr + "], 新值=[" + newValueStr + "]");

            // 检查是否有变更
            if (!newValueStr.equals(oldValueStr)) {
                recordChange(oldEntity, displayName, oldValueStr, newValueStr);
            }
        } catch (Exception e) {
            System.out.println("检查字段 [" + displayName + "] 变更时发生异常: " + e.getMessage());
            System.out.println("异常类型: " + e.getClass().getName());
            System.out.println("异常栈信息:");
            e.printStackTrace();

            // 尝试使用Map方式访问属性
            try {
                java.lang.reflect.Method getAttributesMethod = EmployeeProductConfiguration.class.getMethod("getAttributes");
                if (getAttributesMethod != null) {
                    Map<String, Object> newAttributes = (Map<String, Object>) getAttributesMethod.invoke(newEntity);
                    Map<String, Object> oldAttributes = (Map<String, Object>) getAttributesMethod.invoke(oldEntity);

                    if (newAttributes != null && oldAttributes != null) {
                        Object newValue = newAttributes.get(fieldName);
                        Object oldValue = oldAttributes.get(fieldName);

                        String newValueStr = newValue == null ? "" : String.valueOf(newValue);
                        String oldValueStr = oldValue == null ? "" : String.valueOf(oldValue);

                        System.out.println("Map方式检查字段 [" + displayName + "]: 原值=[" + oldValueStr + "], 新值=[" + newValueStr + "]");

                        if (!newValueStr.equals(oldValueStr)) {
                            recordChange(oldEntity, displayName, oldValueStr, newValueStr);
                        }
                    }
                }
            } catch (Exception ex) {
                System.out.println("Map方式访问字段失败: " + ex.getMessage());
            }
        }
    }

    /**
     * 记录字段变更
     */
    private void recordChange(EmployeeProductConfiguration oldEntity, String displayName, String oldValueStr, String newValueStr) {
        EmployeeProductConfigurationHistory history = createHistoryRecord(oldEntity);
        history.setChangeType(displayName + "变更");
        history.setOldValue(oldValueStr);
        history.setNewValue(newValueStr);
        employeeProductConfigurationHistoryService.save(history);
        System.out.println("检测到" + displayName + "变更，已记录历史");
    }

    /**
     * 创建历史记录对象
     */
    private EmployeeProductConfigurationHistory createHistoryRecord(EmployeeProductConfiguration source) {
        EmployeeProductConfigurationHistory history = new EmployeeProductConfigurationHistory();
        BeanUtils.copyProperties(source, history);
        history.setParentId(source.getId());
        history.setId(null);

        // 手动设置创建时间和更新时间
        Date now = new Date();
        history.setCreateTime(now);
        history.setUpdateTime(now);

        // 设置操作人
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser != null) {
            history.setOperator(loginUser.getUsername());
            history.setCreateBy(loginUser.getUsername());
            history.setUpdateBy(loginUser.getUsername());

            // 设置数据所有者
            history.setOwnerUserId(loginUser.getUsername());
            if (loginUser.getDeptId() != null) {
                history.setOwnerDeptId(loginUser.getDeptId());
            }
        } else {
            // 如果没有登录用户，使用系统用户ID
            history.setOwnerUserId("system");
        }

        return history;
    }

    @PostMapping(value = "/getHistory.do")
    public ResultBean getHistory(EmployeeProductConfiguration entity) {
        List<SortCondition> sorts = new ArrayList<>();
        SortCondition sortCondition = new SortCondition();
        sortCondition.setDirection(SortCondition.SORT_DESC);
        sortCondition.setProperty(BaseEntity.FIELD_CREATE_TIME);
        sorts.add(sortCondition);

        // 调试日志
        System.out.println("getHistory方法被调用，参数entity: " + (entity != null ? entity.toString() : "null"));
        if (entity != null) {
            System.out.println("ID: " + entity.getId());
            System.out.println("RFID标签号: " + entity.getRfidTagNumber());
        }

        // 查询与当前ID相关的历史记录
        List<EmployeeProductConfigurationHistory> historyRecords = new ArrayList<>();

        try {
            if (entity != null && entity.getId() != null && !entity.getId().trim().isEmpty()) {
                // 使用服务方法查询历史记录
                System.out.println("通过ID查询历史记录: " + entity.getId());
                List<Map<String, Object>> results = employeeProductConfigurationHistoryService.findAllRelatedHistoryByParentId(entity.getId());

                System.out.println("SQL查询结果数量: " + results.size());
                for (Map<String, Object> result : results) {
                    String id = (String) result.get("id");
                    System.out.println("处理历史记录ID: " + id);
                    EmployeeProductConfigurationHistory history = employeeProductConfigurationHistoryService.findById(id);
                    if (history != null) {
                        System.out.println("找到历史记录: 变更类型=" + history.getChangeType() + ", 旧值=" + history.getOldValue() + ", 新值=" + history.getNewValue());
                        historyRecords.add(history);
                    } else {
                        System.out.println("未找到ID为" + id + "的历史记录");
                    }
                }
            } else {
                System.out.println("当前实体为null或ID为空，无法查询历史记录");
            }
        } catch (Exception e) {
            System.out.println("查询历史记录时发生异常: " + e.getMessage());
            e.printStackTrace();
            return ResultBean.error("查询历史记录时发生异常: " + e.getMessage(), 500);
        }

        return ResultBean.success(historyRecords);
    }

    @PostMapping(value = "/getxdjl.do")
    public ResultBean getxdjl(EmployeeProductConfiguration entity) {
        List<WmsDeliveryDetail> byContractNumberAndProductSerialNumber = wmsDeliveryDetailService.findByContractNumberAndProductSerialNumber(entity.getContractNumber(), entity.getProductSerialNumber(), "(客户端)收取");
        return ResultBean.success(byContractNumberAndProductSerialNumber);
    }

    /**
     * 条款服装导入
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importExcel.do")
    public ResultBean importExcel(@RequestParam("file") MultipartFile file, String contractNumber) {
        try {
            // 创建自定义监听器
            EmployeeProductConfigurationListener listener = new EmployeeProductConfigurationListener(contractNumber);
            Map<String, Object> result = new HashMap<>();
            // 使用自定义监听器读取Excel
            EasyExcel.read(file.getInputStream(), EmployeeProductConfiguration.class, listener)
                    .sheet()
                    .doRead();

            // 检查是否有转换异常
            if (!listener.getErrorMessages().isEmpty()) {
                // 如果有数据转换异常，直接返回错误信息
                result.put("errorList", listener.getErrorMessages());
                result.put("successCount", 0);
                result.put("newCount",0);
                result.put("updateCount",0);

                return ResultBean.success(result);
            }

            // 没有转换异常，则处理导入数据
            result = employeeProductConfigurationService.importData(listener.getDataList(), contractNumber);

            return ResultBean.success(result);
        } catch (IOException e) {
            e.printStackTrace();
            return ResultBean.error(e.getMessage(), 500);
        }
    }

    @PostMapping("/export.do")
    public ResultBean export(@RequestList(clazz = FilterCondition.class) List<FilterCondition> filters) throws IOException {

        String fileName = "服务记录与查询";
        fileName = ExportUtil.encodingExcelFilename(fileName);
        String folder = Global.getTempPath() + File.separator + "pio" + File.separator;
        FileUtil.touch(folder + fileName);
        // 模拟数据，实际需要从数据库中获取
        List<EmployeeProductConfigurationExcel> list = new ArrayList<>();


        List<EmployeeProductConfiguration> allByObject = employeeProductConfigurationService.findAll(filters, null);

        for (EmployeeProductConfiguration employeeProductConfiguration : allByObject) {
            EmployeeProductConfigurationExcel employeeProductConfigurationExcel = new EmployeeProductConfigurationExcel();
            BeanUtils.copyProperties(employeeProductConfiguration, employeeProductConfigurationExcel);
            list.add(employeeProductConfigurationExcel);
        }
        EasyExcel.write(folder + fileName, EmployeeProductConfigurationExcel.class)
                .sheet("服务记录与查询")
                .doWrite(list);

        return ResultBean.success(fileName);
    }

    @RequestMapping(value = "/getPrintData", produces = {"application/xml;charset=utf-8"})
    @ResponseBody
    public String getData(String ids) {
// 将字符串 ids 转换为 List<Long>
        List<String> idList = Arrays.stream(ids.split(","))
                .map(String::valueOf)  // 将每个部分转换为 Long 类型
                .collect(Collectors.toList());
        List<EmployeeProductConfiguration> employeeProductConfigurationList = new ArrayList<>();
        for (String aLong : idList) {
            EmployeeProductConfiguration byId = employeeProductConfigurationService.findById(aLong);
            employeeProductConfigurationList.add(byId);
        }
        String string = GenerateXML.generateXMLString(employeeProductConfigurationList);
        return string;
    }

    @PostMapping(value = "/checkHistoryTable.do")
    public ResultBean checkHistoryTable(String id) {
        System.out.println("====== 开始检查历史记录表 ======");
        System.out.println("输入的ID: " + id);

        Map<String, Object> result = new HashMap<>();

        try {
            // 获取表结构
            List<Map<String, Object>> tableStructure = employeeProductConfigurationHistoryService.getTableStructure();
            result.put("tableStructure", tableStructure);
            System.out.println("表结构信息: " + tableStructure.size() + "列");

            // 查询指定ID的实体
            if (id != null && !id.trim().isEmpty()) {
                EmployeeProductConfiguration entity = employeeProductConfigurationService.findById(id);
                if (entity != null) {
                    result.put("entity", entity);
                    System.out.println("实体信息: " + entity.toString());

                    // 查询历史记录
                    List<Map<String, Object>> historyRecords = employeeProductConfigurationHistoryService.findAllRelatedHistoryByParentId(id);
                    result.put("historyRecords", historyRecords);
                    System.out.println("历史记录数量: " + historyRecords.size());
                } else {
                    System.out.println("未找到ID为" + id + "的实体");
                    result.put("error", "未找到指定ID的实体");
                }
            } else {
                System.out.println("未提供ID参数");
                result.put("error", "未提供ID参数");
            }

        } catch (Exception e) {
            System.out.println("检查过程中发生异常: " + e.getMessage());
            e.printStackTrace();
            result.put("exception", e.getMessage());
        }

        System.out.println("====== 检查完成 ======");
        return ResultBean.success(result);
    }

    /*@PostMapping(value = "/testHistoryQuery.do")
    public ResultBean testHistoryQuery(String id) {
        System.out.println("====== 开始诊断历史记录查询 ======");
        System.out.println("输入的ID: " + id);

        Map<String, Object> result = new HashMap<>();

        try {
            if (id != null && !id.trim().isEmpty()) {
                // 1. 首先查询当前实体
                EmployeeProductConfiguration entity = employeeProductConfigurationService.findById(id);
                result.put("currentEntity", entity);
                System.out.println("当前实体: " + (entity != null ? entity.toString() : "null"));

                // 2. 查询历史记录
                List<Map<String, Object>> historyResults = employeeProductConfigurationHistoryService.findAllRelatedHistoryByParentId(id);
                result.put("historyQueryResults", historyResults);
                System.out.println("历史记录查询结果数量: " + historyResults.size());

                // 3. 获取完整的历史记录对象
                List<EmployeeProductConfigurationHistory> historyRecords = new ArrayList<>();
                for (Map<String, Object> historyResult : historyResults) {
                    String historyId = (String) historyResult.get("id");
                    EmployeeProductConfigurationHistory history = employeeProductConfigurationHistoryService.findById(historyId);
                    if (history != null) {
                        historyRecords.add(history);
                        System.out.println("历史记录: ID=" + history.getId() +
                                           ", 变更类型=" + history.getChangeType() +
                                           ", 旧值=" + history.getOldValue() +
                                           ", 新值=" + history.getNewValue() +
                                           ", 操作人=" + history.getOperator() +
                                           ", 创建时间=" + history.getCreateTime());
                    } else {
                        System.out.println("未找到ID为" + historyId + "的历史记录");
                    }
                }
                result.put("historyRecords", historyRecords);

                // 4. 查询历史表中所有与该ID相关的记录（直接SQL查询）
                String sql = "SELECT * FROM employee_product_configuration_history WHERE parent_id = ?0";
                List<Map<String, Object>> directSqlResults = dao.executeSQLQuery(sql, id);
                result.put("directSqlResults", directSqlResults);
                System.out.println("直接SQL查询结果数量: " + directSqlResults.size());
            } else {
                System.out.println("ID为空，无法进行诊断");
                result.put("error", "ID为空，无法进行诊断");
            }
        } catch (Exception e) {
            System.out.println("诊断过程中发生异常: " + e.getMessage());
            e.printStackTrace();
            result.put("exception", e.getMessage());
        }

        System.out.println("====== 诊断结束 ======");
        return ResultBean.success(result);
    }*/

   /* @PostMapping(value = "/testRfidWorkChange.do")
    public ResultBean testRfidWorkChange(String id, String newValue) {
        System.out.println("====== 开始测试工作服RFID标签编码字段修改 ======");
        System.out.println("输入的ID: " + id);
        System.out.println("输入的新值: " + newValue);

        Map<String, Object> result = new HashMap<>();

        try {
            if (id != null && !id.trim().isEmpty()) {
                // 1. 查询当前实体
                EmployeeProductConfiguration entity = employeeProductConfigurationService.findById(id);
                if (entity != null) {
                    String oldValue = entity.getRfidTagNumberWork();
                    System.out.println("当前工作服RFID标签编码: " + oldValue);

                    // 2. 修改字段值
                    entity.setRfidTagNumberWork(newValue);

                    // 3. 手动检查并记录变更
                    if (!Objects.equals(oldValue, newValue)) {
                        EmployeeProductConfigurationHistory history = createHistoryRecord(entity);
                        history.setChangeType("工作服RFID标签编码变更");
                        history.setOldValue(oldValue == null ? "" : oldValue);
                        history.setNewValue(newValue == null ? "" : newValue);
                        employeeProductConfigurationHistoryService.save(history);
                        System.out.println("手动记录工作服RFID标签编码变更: 原值=[" + oldValue + "], 新值=[" + newValue + "]");
                    }

                    // 4. 更新实体
                    EmployeeProductConfiguration updatedEntity = employeeProductConfigurationService.update(entity);
                    result.put("success", true);
                    result.put("entity", updatedEntity);
                    result.put("message", "工作服RFID标签编码修改成功");
                } else {
                    result.put("success", false);
                    result.put("message", "未找到ID为" + id + "的记录");
                }
            } else {
                result.put("success", false);
                result.put("message", "ID不能为空");
            }
        } catch (Exception e) {
            System.out.println("测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("exception", e.getMessage());
        }

        System.out.println("====== 测试结束 ======");
        return ResultBean.success(result);
    }*/



}
