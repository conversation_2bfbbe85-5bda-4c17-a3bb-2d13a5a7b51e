package com.rutong.platform.wechat.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.constant.Constants;
import com.rutong.framework.manager.AsyncManager;
import com.rutong.framework.manager.factory.AsyncFactory;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.MessageUtils;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.framework.utils.StringUtils;
import com.rutong.platform.wechat.service.LogoutService;

/**
 * 小程序退出登录控制器
 */
@RestController
@RequestMapping(value = "/wechat/logout")
public class LogoutController {
    private static final Logger log = LoggerFactory.getLogger(LogoutController.class);
    
    @Autowired
    private LogoutService logoutService;
    
    /**
     * 退出登录接口
     * 
     * @return 操作结果
     */
    @PostMapping
    public ResultBean logout() {
        try {
            // 获取当前登录用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null) {
                return ResultBean.error("用户未登录", 401);
            }
            
            String userName = loginUser.getUsername();
            String token = loginUser.getToken();
            
            log.info("用户退出登录: {}", userName);
            
            // 调用登出服务删除用户Token
            logoutService.logout(token);
            
            // 记录用户退出日志
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(userName, Constants.LOGOUT,
                    MessageUtils.message("user.logout.success")));
            
            return ResultBean.success("退出成功");
        } catch (Exception e) {
            log.error("退出登录失败", e);
            return ResultBean.error(e.getMessage(), 500);
        }
    }
    
    /**
     * 通过Token退出登录接口（无需当前用户已登录）
     * 
     * @param token 用户令牌
     * @return 操作结果
     */
    @PostMapping(value = "/logoutByToken.do")
    public ResultBean logoutByToken(String token) {
        try {
            if (StringUtils.isEmpty(token)) {
                return ResultBean.error("Token不能为空", 400);
            }
            
            log.info("用户通过Token退出登录");
            
            // 调用登出服务删除用户Token
            logoutService.logout(token);
            
            return ResultBean.success("退出成功");
        } catch (Exception e) {
            log.error("通过Token退出登录失败", e);
            return ResultBean.error(e.getMessage(), 500);
        }
    }
} 