package com.rutong.framework.security.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.rutong.framework.bean.UserStatus;
import com.rutong.framework.core.exception.ServiceException;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.MessageUtils;
import com.rutong.framework.utils.StringUtils;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.sys.service.SysUserService;

/**
 * 用户验证处理
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {
	private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

	@Autowired
	private SysUserService userService;

	@Autowired
	private PasswordService passwordService;

	@Autowired
	private PermissionService permissionService;

	@Override
	public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
		SysUser user = userService.selectUserByUserName(username);
		if (StringUtils.isNull(user)) {
			log.info("登录用户：{} 不存在.", username);
			throw new ServiceException(MessageUtils.message("user.not.exists"));
		}
		passwordService.validate(user);
		return createLoginUser(user);
	}

	public UserDetails createLoginUser(SysUser user) {
		return new LoginUser(user.getId(), user.getDeptid(), user, permissionService.getMenuPermission(user));
	}
}
