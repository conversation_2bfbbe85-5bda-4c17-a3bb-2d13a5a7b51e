package com.rutong.platform.wms.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsClothing;
import com.rutong.platform.wms.service.WmsClothingService;

@RestController
@RequestMapping("/wms/clothing")
@LogDesc(title = "服装库存")
public class WmsClothingController extends CrudController<WmsClothing>{
	
	@Autowired
	private WmsClothingService wmsClothingService;

	@Override
	public BaseService<WmsClothing> getService() {
		return wmsClothingService;
	}
	
	/**
	 * 在库列表
	 */
	@Override
	public PageInfo<WmsClothing> findAllByPage(GridParams gridParams,
			@RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
			@RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
		FilterCondition filterCondition = new FilterCondition();
		filterCondition.setProperty(WmsClothing.FIELD_STATUS);
		filterCondition.setOperator(FilterCondition.EQ);
		filterCondition.setValue(String.valueOf(0));
		filters.add(filterCondition);
		return super.findAllByPage(gridParams, filters, sorts);
	}

	/**
	 * 租赁列表
	 * @param gridParams
	 * @param filters
	 * @param sorts
	 * @return
	 */
	@PostMapping(value = "/fetchdata2.do")
	public PageInfo<WmsClothing> findAllByPage2(GridParams gridParams,
			@RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
			@RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
		FilterCondition filterCondition = new FilterCondition();
		filterCondition.setProperty(WmsClothing.FIELD_STATUS);
		filterCondition.setOperator(FilterCondition.EQ);
		filterCondition.setValue(String.valueOf(1));
		filters.add(filterCondition);
		return super.findAllByPage(gridParams, filters, sorts);
	}
}
