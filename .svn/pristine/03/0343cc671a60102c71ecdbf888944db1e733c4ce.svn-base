package com.rutong.platform.device.controller;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.utils.DataAuthUtil;
import com.rutong.platform.client.entity.ClientInfo;
import com.rutong.platform.client.service.ClientInfoService;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.device.entity.DevRecordSorting;
import com.rutong.platform.device.entity.DevSorting;
import com.rutong.platform.device.service.DevRecordSortingService;
import com.rutong.platform.device.service.SortingService;
import com.rutong.platform.wms.service.WmsExceptionService;
import com.rutong.platform.wms.service.WmsExceptionTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.alibaba.excel.EasyExcel;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.rutong.platform.device.excel.DevRecordSortingExcel;
import org.apache.commons.lang3.StringUtils;

@RestController
@RequestMapping("/device/recordSorting")
@LogDesc(title = "分拣记录")
public class DevRecordSortingController extends CrudController<DevRecordSorting> {

    @Autowired
    private DevRecordSortingService devRecordSortingService;

    @Autowired
    private ClientInfoService clientInfoService;
    @Autowired
    private WmsExceptionService exceptionService;

    @Override
    public BaseService<DevRecordSorting> getService() {
        return devRecordSortingService;
    }

    @Override
    public PageInfo<DevRecordSorting> findAllByPage(GridParams gridParams,
                                                    @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
                                                    @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        if (sorts.size() == 0) {
            SortCondition sortCondition = new SortCondition();
            sortCondition.setDirection(SortCondition.SORT_DESC);
            sortCondition.setProperty(BaseEntity.FIELD_CREATE_TIME);
            sorts.add(sortCondition);
        }

        // 处理时间范围过滤
        for (int i = 0; i < filters.size(); i++) {
            FilterCondition filter = filters.get(i);
            // 如果是日期字符串，需要转换为Date对象
            if ((filter.getProperty().equals("sortingTime") || filter.getProperty().equals("createTime"))
                    && filter.getValue() instanceof String) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String dateStr = (String) filter.getValue();
                    Date date = sdf.parse(dateStr);
                    filter.setValue(date);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }

            // 处理客户名称查询，将clientId映射到enterpriseName字段
            if (filter.getProperty().equals("clientId")) {
                filter.setProperty("enterpriseName");
                // 如果是UUID格式，需要通过clientId查询对应的客户名称
                if (filter.getValue() instanceof String) {
                    String clientId = (String) filter.getValue();
                    // 通过ID查询客户信息
                    ClientInfo clientInfo = clientInfoService.findById(clientId);
                    if (clientInfo != null && clientInfo.getEnterpriseName() != null) {
                        // 使用客户名称作为查询条件
                        filter.setValue(clientInfo.getEnterpriseName());
                    } else {
                        // 如果找不到客户信息，移除这个过滤条件，避免查询不到数据
                        filters.remove(i);
                        i--; // 因为移除了一个元素，索引需要减1
                    }
                }
            }
        }

        //增加数据权限
        DataAuthUtil.setDataAuth(filters);
        return getService().findAllByPage(gridParams, filters, sorts);
    }

    /**
     * 导出分拣记录为Excel
     *
     * @param response HTTP响应
     * @param filters  筛选条件列表（与查询列表使用相同参数）
     * @param ids      勾选的记录ID列表，多个ID用逗号分隔
     */
//    @GetMapping("/export.do")
    public void exportRecordSorting(HttpServletResponse response,
                                    @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
                                    String ids) throws Exception {

        List<DevRecordSorting> list;

        // 优先根据勾选的ID导出
        if (StringUtils.isNotBlank(ids)) {
            String[] idArray = ids.split(",");
            list = new java.util.ArrayList<>();
            for (String id : idArray) {
                DevRecordSorting record = devRecordSortingService.findById(id);
                if (record != null) {
                    list.add(record);
                }
            }
        } else {
            // 如果没有勾选，则根据搜索条件导出

            // 处理时间范围过滤
            for (int i = 0; i < filters.size(); i++) {
                FilterCondition filter = filters.get(i);
                // 如果是日期字符串，需要转换为Date对象
                if ((filter.getProperty().equals("sortingTime") || filter.getProperty().equals("createTime"))
                        && filter.getValue() instanceof String) {
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String dateStr = (String) filter.getValue();
                        Date date = sdf.parse(dateStr);
                        filter.setValue(date);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                }

                // 处理客户名称查询，将clientId映射到enterpriseName字段
                if (filter.getProperty().equals("clientId")) {
                    filter.setProperty("enterpriseName");
                    // 如果是UUID格式，需要通过clientId查询对应的客户名称
                    if (filter.getValue() instanceof String) {
                        String clientId = (String) filter.getValue();
                        // 通过ID查询客户信息
                        ClientInfo clientInfo = clientInfoService.findById(clientId);
                        if (clientInfo != null && clientInfo.getEnterpriseName() != null) {
                            // 使用客户名称作为查询条件
                            filter.setValue(clientInfo.getEnterpriseName());
                        } else {
                            // 如果找不到客户信息，移除这个过滤条件，避免查询不到数据
                            filters.remove(i);
                            i--; // 因为移除了一个元素，索引需要减1
                        }
                    }
                }
            }

            //增加数据权限
            DataAuthUtil.setDataAuth(filters);

            // 根据条件查询数据
            if (filters.isEmpty()) {
                list = devRecordSortingService.findAll();
            } else {
                list = devRecordSortingService.findAll(filters, null);
            }
        }

        // 转换为Excel导出结构体
        List<DevRecordSortingExcel> excelList = new java.util.ArrayList<>();
        int i = 1;
        for (DevRecordSorting record : list) {
            DevRecordSortingExcel excel = new DevRecordSortingExcel();
            excel.setIndex(i++);
            excel.setSortingMan(record.getSortingMan());
            excel.setCabNo(record.getCabNo());
            excel.setSortingno(record.getSortingno());
            excel.setClientName(record.getClientName());
            excel.setEmployeeName(record.getEmployeeName());
            excel.setEmployeePhone(record.getEmployeePhone());
            excel.setDepartment(record.getDepartment());
            excel.setSubDepartment(record.getSubDepartment());
            excel.setConfigurationCategory(record.getConfigurationCategory());
            excel.setProductModelNumber(record.getProductModelNumber());
            excel.setProductColor(record.getProductColor());
            excel.setLogoPosition(record.getLogoPosition());
            excel.setProductSpecification(record.getProductSpecification());
            excel.setEmployeeId(record.getEmployeeId());
            excel.setRfidTagNumber(record.getRfidTagNumber());
            excel.setProductSerialNumber(record.getProductSerialNumber());
            excel.setSortingTime(record.getSortingTime());
            excelList.add(excel);
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("分拣记录导出", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), DevRecordSortingExcel.class)
                .sheet("分拣记录")
                .doWrite(excelList);
    }

    /**
     * 导出分拣记录为Excel
     *
     * @param response HTTP响应
     * @param filters  筛选条件列表（与查询列表使用相同参数）
     * @param ids      勾选的记录ID列表，多个ID用逗号分隔
     */
    @GetMapping("/export.do")
    public void exportRecordSortingNew(HttpServletResponse response,
                                       @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
                                       String ids) throws Exception {


        List<DevRecordSorting> all = devRecordSortingService.findAll();
        exportMergedWorkWearData(all, response);
    }

    public void exportDynamicWorkWearData(List<DevRecordSorting> dataList, HttpServletResponse response) throws IOException {
        // 获取所有品类
        // 获取所有品类
        Set<String> categories = dataList.stream()
                .map(DevRecordSorting::getWorkWearConfigurationCategory)
                .collect(Collectors.toSet());
        // 动态构建表头
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("日期"));
        head.add(Collections.singletonList("客户名称"));

        for (String category : categories) {
            head.add(Collections.singletonList(category));
        }

        head.add(Collections.singletonList("备注"));

        // 获取并合并数据
//        List<DevRecordSorting> originalData = workWearRepository.findAll();
        List<List<Object>> data = mergeDataWithDynamicCategories(dataList, categories);

        // 导出Excel
        EasyExcel.write(response.getOutputStream())
                .head(head)
                .sheet("动态品类销售报表")
                .doWrite(data);
    }

    private List<List<Object>> mergeDataWithDynamicCategories(List<DevRecordSorting> dataList, Set<String> categories) {
        Map<String, List<Object>> mergedMap = new LinkedHashMap<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        List<String> categoryList = new ArrayList<>(categories);

        for (DevRecordSorting record : dataList) {
            String key = dateFormat.format(record.getCreateTime()) + "|" + record.getEnterpriseName();

            if (!mergedMap.containsKey(key)) {
                List<Object> row = new ArrayList<>();
                row.add(dateFormat.format(record.getCreateTime()));
                row.add(record.getEnterpriseName());

                // 初始化所有品类数量为0
                for (int i = 0; i < categoryList.size(); i++) {
                    row.add(0);
                }

                row.add(record.getAttributes());
                mergedMap.put(key, row);
            }

            List<Object> existingRow = mergedMap.get(key);
            int categoryIndex = categoryList.indexOf(record.getWorkWearConfigurationCategory());

            if (categoryIndex >= 0) {
                Integer currentValue = (Integer) existingRow.get(2 + categoryIndex);
                Integer newValue = record.getProtocolConfigurationQuantity() != null ?
                        record.getProtocolConfigurationQuantity() : 0;
                existingRow.set(2 + categoryIndex, currentValue + newValue);
            }

            // 合并备注
            if (record.getAttributes() != null && !record.getAttributes().isEmpty()) {
                String existingRemark = (String) existingRow.get(existingRow.size() - 1);
                if (existingRemark != null && !existingRemark.isEmpty()) {
                    existingRow.set(existingRow.size() - 1, existingRemark + ";" + record.getAttributes());
                }
            }
        }

        return new ArrayList<>(mergedMap.values());
    }

    public void exportMergedWorkWearData(List<DevRecordSorting> dataList,
                                         HttpServletResponse response) throws IOException {
        // 获取所有品类
        Set<String> categories = dataList.stream()
                .map(DevRecordSorting::getWorkWearConfigurationCategory)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 动态构建表头
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("日期"));
        head.add(Collections.singletonList("客户名称"));

        // 添加动态品类列
        for (String category : categories) {
            head.add(Collections.singletonList(category));
        }
        head.add(Collections.singletonList("共计"));
        head.add(Collections.singletonList("备注"));
        head.add(Collections.singletonList("修补件数"));

        // 合并数据
        List<List<Object>> data = mergeData(dataList, new ArrayList<>(categories));

        // 写入Excel
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("合并工作服销售报表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        EasyExcel.write(response.getOutputStream())
                .head(head)
                .sheet("合并销售报表")
                .doWrite(data);
    }

    private List<List<Object>> mergeData(List<DevRecordSorting> dataList, List<String> categories) {
        Map<String, List<Object>> mergedDataMap = new LinkedHashMap<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        for (DevRecordSorting item : dataList) {
            String mergeKey = simpleDateFormat.format(item.getCreateTime()) + "|" + item.getEnterpriseName() + "|" + item.getContractNumber();

            if (!mergedDataMap.containsKey(mergeKey)) {
                List<Object> row = new ArrayList<>();
                row.add(simpleDateFormat.format(item.getCreateTime())); // 日期
                row.add(item.getEnterpriseName()); // 客户名称

                // 初始化所有品类数量为0
                for (int i = 0; i < categories.size(); i++) {
                    row.add(0);
                }

                // 添加共计列初始值0
                row.add(0);

                // 备注列
                row.add(item.getAttributes());

                // 修补件数列（初始为0）
                row.add(0);

                mergedDataMap.put(mergeKey, row);
            }

            List<Object> existingRow = mergedDataMap.get(mergeKey);
            int categoryIndex = categories.indexOf(item.getWorkWearConfigurationCategory());

            if (categoryIndex >= 0) {
                // 累加品类数量（每次加1）
                Integer currentValue = (Integer) existingRow.get(2 + categoryIndex);
                existingRow.set(2 + categoryIndex, currentValue + 1);

                // 更新总数（共计列）
                Integer currentTotal = (Integer) existingRow.get(2 + categories.size()); // 共计列位置
                existingRow.set(2 + categories.size(), currentTotal + 1);
            }

            // 合并备注信息
            if (item.getAttributes() != null && !item.getAttributes().isEmpty()) {
                String existingRemark = (String) existingRow.get(2 + categories.size() + 1); // 备注列位置
                if (existingRemark != null && !existingRemark.isEmpty()) {
                    existingRow.set(2 + categories.size() + 1, existingRemark + ";" + item.getAttributes());
                } else {
                    existingRow.set(2 + categories.size() + 1, item.getAttributes());
                }
            }
            // 设置修补件数（这里需要根据业务逻辑获取实际值）
            // 假设从item对象中获取修补件数
//            Integer repairCount = item.getRepairCount() != null ? item.getRepairCount() : 0;
//            existingRow.set(2 + categories.size() + 2, repairCount); // 修补件数是最后一列
        }
        for (String key : mergedDataMap.keySet()) {
            List<Object> objects = mergedDataMap.get(key);
            BigInteger bigInteger = exceptionService.groupByWorkWearConfigurationCategory(key);
            objects.set(2 + categories.size() + 2, bigInteger); // 修补件数是最后一列
        }
        return new ArrayList<>(mergedDataMap.values());
    }
}
