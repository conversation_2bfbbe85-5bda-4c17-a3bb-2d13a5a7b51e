package com.rutong.platform.client.controller;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSONArray;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.FilterConditionBuilder;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.*;
import com.rutong.platform.client.service.*;
import com.rutong.platform.client.util.WeekdayCounter;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.service.SysDeptService;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 费用结算与查询
 */
@RestController
@RequestMapping("/client/expenseSettlement")
public class ExpenseSettlementController extends CrudController<ExpenseSettlement> {

    @Autowired
    private ExpenseSettlementService expenseSettlementService;
    @Autowired
    private ExpenseSettlementItemService expenseSettlementItemService;
    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;
    @Autowired
    private SysDeptService sysDeptService;
    @Autowired
    private DemandResponseService demandResponseService;
    @Autowired
    private ClauseInfoService clauseInfoService;
    @Autowired
    private ClientInfoService clientInfoService;

    @Override
    public BaseService<ExpenseSettlement> getService() {
        return expenseSettlementService;
    }

    //
    @Override
    public ResultBean save(ExpenseSettlement expenseSettlement) {


        ClientInfo byContractNumber = clientInfoService.findByContractNumber(expenseSettlement.getContractNumber());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        expenseSettlement.setEnterpriseName(byContractNumber.getEnterpriseName());
        expenseSettlement.setFiller(loginUser.getUser().getNickname());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        ExpenseSettlement expenseSettlement1 = expenseSettlementService.selectByContractNumber(expenseSettlement.getContractNumber());
        if (expenseSettlement1 == null) {
            expenseSettlement.setSettlementStart(byContractNumber.getStartDate());
        } else {
            expenseSettlement.setSettlementStart(expenseSettlement1.getSettlementEnd());
        }
        if (expenseSettlement.getSettlementEnd().before(expenseSettlement.getSettlementStart())) {
            throw new ExcelAnalysisException("结算时间不能小于最后结算时间！");
        }
        expenseSettlementService.save(expenseSettlement);
        ClauseInfo clauseInfoData = clauseInfoService.findByContractNumber(expenseSettlement.getContractNumber());


        BigDecimal sumProduct = new BigDecimal(0);
        BigDecimal sumAbstersion = new BigDecimal(0);
        BigDecimal sumContract = new BigDecimal(0);//合同规定洗涤金额
        BigDecimal sumContractCount = new BigDecimal(0);//合同规定洗涤次数
        BigDecimal sumAbstersionCount = new BigDecimal(0);//实际洗涤次数
        BigDecimal sum = new BigDecimal(0);
//        for (WmsDeliveryDetail wmsDeliveryDetail : all) {
        EmployeeProductConfiguration employeeProductConfigurationSelect = new EmployeeProductConfiguration();
        employeeProductConfigurationSelect.setStatus("合作中");
        employeeProductConfigurationSelect.setContractNumber(expenseSettlement.getContractNumber());
        List<EmployeeProductConfiguration> byRfidAndStatus = employeeProductConfigurationService.findAllByObject(employeeProductConfigurationSelect, null);
        for (EmployeeProductConfiguration employeeProductConfiguration : byRfidAndStatus) {
            ExpenseSettlementItem expenseSettlementItem = new ExpenseSettlementItem();
            BeanUtils.copyProperties(employeeProductConfiguration, expenseSettlementItem);
            List<FilterCondition> filters = new ArrayList<>();
            FilterCondition filterCondition = new FilterCondition();
            filterCondition.setProperty("contractNumber");
            filterCondition.setOperator(FilterCondition.EQ);
            filterCondition.setValue(expenseSettlement.getContractNumber());
            filters.add(filterCondition);

            FilterCondition filter2 = new FilterCondition();
            filter2.setProperty("createTime");
            filter2.setOperator(FilterCondition.GE);
            filter2.setValue(expenseSettlement.getSettlementStart());
            filters.add(filter2);

            FilterCondition filter3 = new FilterCondition();
            filter3.setProperty("createTime");
            filter3.setOperator(FilterCondition.LE);
            filter3.setValue(expenseSettlement.getSettlementEnd());
            filters.add(filter3);

            List<SortCondition> sorts = new ArrayList<>();
            SortCondition sort1 = new SortCondition();
            sort1.setProperty("contractNumber");
            sort1.setDirection(SortCondition.SORT_ASC);
            sorts.add(sort1);

            FilterCondition filter4 = new FilterCondition();
            filter4.setProperty("rfidTagNumber");
            filter4.setOperator(FilterCondition.EQ);
            filter4.setValue(expenseSettlementItem.getRfidTagNumber());
            filters.add(filter4);

            //需求回复（衣服缺失）
            List<DemandResponse> allByObject = demandResponseService.findAll(filters, null);
            if (allByObject.size() > 0) {
                DemandResponse demandResponseData = allByObject.get(0);
                if (demandResponseData.getConditionInfo().equals("缺失衣裤") && demandResponseData.getStatus().equals("已处理")) {
                    expenseSettlementItem.setClothingLossCount(allByObject.size());
                    expenseSettlementItem.setClothingLossFee(new BigDecimal(allByObject.size()).multiply(expenseSettlementItem.getClothingSettlementPrice()));
                }
            }


            FilterCondition filter5 = new FilterCondition();
            filter5.setProperty("type");
            filter5.setOperator(FilterCondition.EQ);
            filter5.setValue("客户端收取");
            filters.add(filter5);
            //计算洗涤价格（按照次数）
            BigDecimal clothingPrice = new BigDecimal(0);
            BigDecimal clothingCount = new BigDecimal(0);
            List<WmsDeliveryDetail> all = wmsDeliveryDetailService.findAll(filters, sorts);
            for (WmsDeliveryDetail wmsDeliveryDetail : all) {
                clothingCount = clothingCount.add(new BigDecimal(1));
                clothingPrice = clothingPrice.add(employeeProductConfiguration.getSingleWashRentalFee());
            }
            int count = WeekdayCounter.countMatchingDays(employeeProductConfiguration.getWeeklyWashDate(), expenseSettlement.getSettlementStart(), expenseSettlement.getSettlementEnd());
            //加上丢失费用合计
            expenseSettlementItem.setMonthlyExpensesReceivable((new BigDecimal(count).multiply(employeeProductConfiguration.getSingleWashRentalFee())).add(expenseSettlementItem.getClothingLossFee()));
            expenseSettlementItem.setMonthlyExpensesReceivableCleaning(new BigDecimal(count).multiply(employeeProductConfiguration.getSingleWashRentalFee()));
            expenseSettlementItem.setMonthlyExpensesReceivableCount(new BigDecimal(count));//合同洗涤次数
            expenseSettlementItem.setProductWashInfo(new BigDecimal(all.size()));
//                DemandResponse demandResponse = new DemandResponse();
//                demandResponse.setRfidTagNumber(employeeProductConfiguration.getRfidTagNumber());
            expenseSettlementItem.setCycleCleaningCharge(clothingPrice);
            expenseSettlementItem.setMonthlyTotalCost(expenseSettlementItem.getCycleCleaningCharge().add(expenseSettlementItem.getClothingLossFee()));
            expenseSettlementItem.setMonthlyTotalCostCount(clothingCount);//实际洗涤次数
            sumAbstersion = sumAbstersion.add(expenseSettlementItem.getCycleCleaningCharge());
            sumProduct = sumProduct.add(expenseSettlementItem.getClothingLossFee());
            sum = sum.add(expenseSettlementItem.getMonthlyTotalCost());
            sumContract = sumContract.add(expenseSettlementItem.getMonthlyExpensesReceivable());

            sumContractCount = sumContractCount.add(new BigDecimal(count));
            sumAbstersionCount = sumAbstersionCount.add(clothingCount);

            expenseSettlementItem.setId(null);
            expenseSettlementItem.setEsId(expenseSettlement.getId());
            expenseSettlementItem.setEnterpriseName(byContractNumber.getEnterpriseName());
            expenseSettlementItemService.save(expenseSettlementItem);
        }

//        }
        expenseSettlement.setTotalMonthlyPayableFeeContract(sumContract);
        expenseSettlement.setTotalMonthlyPayableFeeContractCount(sumContractCount);
        expenseSettlement.setContractPaymentDate(clauseInfoData.getPaymentDate());
        expenseSettlement.setMonthlyCleaningRentalFee(sumAbstersion);
        expenseSettlement.setMonthlyProductFee(sumProduct);
        expenseSettlement.setTotalMonthlyPayableFee(sum);
        expenseSettlement.setTotalMonthlyPayableCount(sumAbstersionCount);
        expenseSettlementService.update(expenseSettlement);
        return ResultBean.success(expenseSettlement);
    }


//    @PostMapping(value = "/processing.do")
//    public ResultBean processing(DemandResponse demandResponse) {
//        DemandResponse byId1 = demandResponseService.findById(demandResponse.getId());
//        LoginUser loginUser = SecurityUtils.getLoginUser();
//        byId1.setProcessingUser(loginUser.getUser().getNickname());
//        byId1.setProcessingTime(new Date());
//        byId1.setProcessingRemarks(demandResponse.getProcessingRemarks());
//        byId1.setStatus("已处理");
//        demandResponseService.update(byId1);
//        return ResultBean.success(byId1);
//    }


}
