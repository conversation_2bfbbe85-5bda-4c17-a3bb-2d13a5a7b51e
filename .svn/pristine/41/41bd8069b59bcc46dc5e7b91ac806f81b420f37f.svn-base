服务记录与查询导入逻辑修改说明：

1. 修改批量导入功能的验证规则：
   - 工作服RFID标签号(rfidTagNumberWork)不能为空，必须填写
   - RFID标签号(rfidTagNumber)可以为空，但如果填写则不能与数据库中已有的重复

2. 保留工作服RFID标签号不能重复的限制

3. 优化更新逻辑：
   - 首先根据工作服RFID标签号(rfidTagNumberWork)匹配数据库中的记录
   - 如果找到匹配记录，则检查RFID标签号：
     * 如果新的RFID标签号与该记录原有的RFID标签号相同，允许更新
     * 如果新的RFID标签号与其他记录的RFID标签号重复，不允许更新
   - 更新时保留原记录的ID、创建时间和创建人信息
   - 其他所有字段都会被更新为新导入的数据

4. 导入结果现在会显示：
   - 新增记录数量
   - 更新记录数量
   - 总成功处理记录数量

修改日期：2024-07-08
