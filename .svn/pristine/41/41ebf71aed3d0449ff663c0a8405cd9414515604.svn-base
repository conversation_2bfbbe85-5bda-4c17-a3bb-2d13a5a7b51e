package com.rutong.platform.washing.entity;

import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * @ClassName WashingRecord 
 * @Description 洗涤记录
 * @Version 1.0
 **/
@Data
@Entity
@Table(name = "washing_record")
public class WashingRecord extends BaseEntity {
    
    private String clientName;           // 客户名称
    private String employeeName;         // 员工姓名
    private Date washingTime;            // 洗涤时间
    private String employeePhone;        // 员工电话
    private String department;           // 部门
    private String subDepartment;        // 分部门
    private String configType;           // 配置品类
    private String productModel;         // 产品款号
    private String productColor;         // 产品颜色
    private String logoPosition;         // LOGO位置
    private String productSpecification; // 产品规格
    private String employeeId;           // 员工企业工号
    private String rfidCode;             // 工作服RFID标签编码
    private String rfidTagNumber;        // RFID标签号
} 