package com.rutong.framework.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

import com.rutong.framework.bean.TreeNode;

public class TreeBuilder {

	/**
	 * 组装list树形数据
	 * 
	 * @param dirs 集合数据
	 * @return
	 */
	public static <T extends TreeNode> List<T> buildListToTree(List<T> dirs) {
		List<T> roots = findRoots(dirs);
		List<T> notRoots = (List<T>) CollectionUtils.subtract(dirs, roots);
		for (T root : roots) {
			root.setChildren(findChildren(root, notRoots));
		}
		return roots;
	}

	private static <T extends TreeNode> List<T> findRoots(List<T> allTreeNodes) {
		List<T> results = new ArrayList<T>();
		for (T node : allTreeNodes) {
			boolean isRoot = true;
			String parentId = node.getParentid();
			node.setParentid((StringUtils.isEmpty(parentId) ? "00" : parentId));
			for (T comparedOne : allTreeNodes) {
				String id = comparedOne.getId();
				if (node.getParentid().equals(id)) {
					isRoot = false;
					break;
				}
			}
			if (isRoot) {
				node.setLevel(0);
				results.add(node);
			}
		}
		sort(results);
		return results;
	}

	private static <T extends TreeNode> List<T> findChildren(T root, List<T> allTreeNodes) {
		List<T> children = new ArrayList<T>();
		for (T comparedOne : allTreeNodes) {
			String id = root.getId();
			if (comparedOne.getParentid().equals(id)) {
				comparedOne.setLevel(root.getLevel() + 1);
				children.add(comparedOne);
			}
		}
		List<T> notChildren = (List<T>) CollectionUtils.subtract(allTreeNodes, children);
		for (T child : children) {
			List<T> tmpChildren = findChildren(child, notChildren);
			if (tmpChildren == null || tmpChildren.size() < 1) {
				child.setLeaf(true);
			} else {
				child.setLeaf(false);
			}
			child.setChildren(tmpChildren);
		}
		sort(children);
		return children;
	}

	private static <T extends TreeNode> void sort(List<T> list) {
		Collections.sort(list, new Comparator<T>() {
			public int compare(T a, T b) {
				Integer one = null;
				Integer two = null;
				one = a.getOrderno();
				two = b.getOrderno();
				if (one == null || two == null) {
					return 0;
				}
				return one - two;
			}
		});
	}

}
