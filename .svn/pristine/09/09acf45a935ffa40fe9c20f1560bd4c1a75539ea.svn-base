package com.rutong.platform.common.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import com.fasterxml.jackson.annotation.JsonFormat;

@MappedSuperclass
public abstract class BaseEntity {
	public static final String ID = "id";
	public static final String FIELD_CREATE_BY = "createBy";
	public static final String FIELD_CREATE_TIME = "createTime";
	public static final String FIELD_UPDATE_BY = "updateBy";
	public static final String FIELD_UPDATE_TIME = "updateTime";
	public static final String FIELD_OWNER_USER_ID = "ownerUserId";
	public static final String FIELD_OWNER_DEPT_ID = "ownerDeptId";

	@Id
	@GenericGenerator(name = "generator", strategy = "org.hibernate.id.UUIDGenerator")
	@GeneratedValue(generator = "generator")
	@Column(unique = true, nullable = false, length = 40)
	protected String id;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	/** 创建者 */
	@CreatedBy
	@Column(nullable = false)
	protected String createBy;

	/** 创建时间 */
	@CreatedDate
	@Column(nullable = false,length = 19)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	protected Date createTime;

	/** 更新者 */
	@LastModifiedBy
	protected String updateBy;

	/** 更新时间 */
	@LastModifiedDate
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	protected Date updateTime;
	
	/**
	 * 数据权限
	 */
	@Column(nullable = false)
	protected String ownerUserId;

	/**
	 * 数据权限
	 */
	@Column
	protected String ownerDeptId;

	/** 备注 */
	protected String remark;

	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdateBy() {
		return updateBy;
	}

	public void setUpdateBy(String updateBy) {
		this.updateBy = updateBy;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getOwnerUserId() {
		return ownerUserId;
	}

	public void setOwnerUserId(String ownerUserId) {
		this.ownerUserId = ownerUserId;
	}

	public String getOwnerDeptId() {
		return ownerDeptId;
	}

	public void setOwnerDeptId(String ownerDeptId) {
		this.ownerDeptId = ownerDeptId;
	}
	
}
