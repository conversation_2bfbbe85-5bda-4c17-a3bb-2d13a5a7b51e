package com.rutong.platform.sys.service;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Service;

import com.rutong.framework.constant.UserConstants;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.StringUtils;
import com.rutong.platform.common.pojo.TreeData;
import com.rutong.platform.common.service.TreeService;
import com.rutong.platform.sys.entity.SysMenu;
import com.rutong.platform.sys.pojo.MenuDataItem;

@Service
public class SysMenuService extends TreeService<SysMenu> {

	public Set<String> selectMenuPermsByUserId(String userid) {
		String sql = "select distinct m.perms from sys_menu m " + "	left join sys_role_menu rm on m.id = rm.menuid "
				+ "	left join sys_user_role ur on rm.roleid = ur.roleid "
				+ "	left join sys_role r on r.id = ur.roleid " + "	where ur.userid = '" + userid+"'";
		List<SysMenu> perms = dao.executeSQLQuery(sql, SysMenu.class);
		Set<String> permsSet = new HashSet<>();
		for (SysMenu menu : perms) {
			if (StringUtils.isNotEmpty(menu.getPerms())) {
				permsSet.addAll(Arrays.asList(menu.getPerms().trim().split(",")));
			}
		}
		return permsSet;
	}

	public List<MenuDataItem> selectMenuTreeByUserId(LoginUser loginUser) {
		String sql = "select distinct m.id, m.parentid, m.menuname as name, m.path,"
				+ " ifnull(m.perms,'') as authority, m.icon, m.orderno " + "	from sys_menu m where m.menutype = 0 ";
		if (!loginUser.getUser().getUsertype().equals(UserConstants.SUPER_ADMIN)) {
			sql += "";
		}
		sql += "order by m.orderno asc";
		return dao.executeSQLQuery(sql, MenuDataItem.class);
	}

	public List<TreeData> getMenuTree() {
		String sql = "select t_.id,t_.parentid,t_.menuname as title from sys_menu t_ where 1=1 order by t_.orderno asc";
		return dao.executeSQLQuery(sql, TreeData.class);
	}

}
