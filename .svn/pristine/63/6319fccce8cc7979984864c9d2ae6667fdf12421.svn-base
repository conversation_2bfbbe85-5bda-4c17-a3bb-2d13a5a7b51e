package com.rutong.platform.wms.entity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rutong.platform.common.entity.BaseEntity;

/**
 * 配送明细
 */
@Entity
@Table(name = "wms_delivery_detail")
public class WmsDeliveryDetail extends BaseEntity{

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "delivery_id")
	private WmsDelivery delivery;
	
	//合同人员及服装
	 /**
     * 合同编号
     */
    private String contractNumber;
    private String employeeName;
    private String department;

    private String subDepartment;
    
    private String configurationCategory;

    private String productModelNumber;

    private String productColor;

    private String logoPosition;

    private String productSpecification;
    private String employeeID;

    private String rfidTagNumber;

    private String productSerialNumber;
    private String lockerSerialNumber;
    
    private String type; //仓储端接单,客户端送达,客户端收取,洗涤端送达,外洗端送达,外洗端收取,外洗回检端送达

	public WmsDelivery getDelivery() {
		return delivery;
	}

	public void setDelivery(WmsDelivery delivery) {
		this.delivery = delivery;
	}

	public String getContractNumber() {
		return contractNumber;
	}

	public void setContractNumber(String contractNumber) {
		this.contractNumber = contractNumber;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public String getSubDepartment() {
		return subDepartment;
	}

	public void setSubDepartment(String subDepartment) {
		this.subDepartment = subDepartment;
	}

	public String getConfigurationCategory() {
		return configurationCategory;
	}

	public void setConfigurationCategory(String configurationCategory) {
		this.configurationCategory = configurationCategory;
	}

	public String getProductModelNumber() {
		return productModelNumber;
	}

	public void setProductModelNumber(String productModelNumber) {
		this.productModelNumber = productModelNumber;
	}

	public String getProductColor() {
		return productColor;
	}

	public void setProductColor(String productColor) {
		this.productColor = productColor;
	}

	public String getLogoPosition() {
		return logoPosition;
	}

	public void setLogoPosition(String logoPosition) {
		this.logoPosition = logoPosition;
	}

	public String getProductSpecification() {
		return productSpecification;
	}

	public void setProductSpecification(String productSpecification) {
		this.productSpecification = productSpecification;
	}

	public String getEmployeeID() {
		return employeeID;
	}

	public void setEmployeeID(String employeeID) {
		this.employeeID = employeeID;
	}

	public String getRfidTagNumber() {
		return rfidTagNumber;
	}

	public void setRfidTagNumber(String rfidTagNumber) {
		this.rfidTagNumber = rfidTagNumber;
	}

	public String getProductSerialNumber() {
		return productSerialNumber;
	}

	public void setProductSerialNumber(String productSerialNumber) {
		this.productSerialNumber = productSerialNumber;
	}

	public String getLockerSerialNumber() {
		return lockerSerialNumber;
	}

	public void setLockerSerialNumber(String lockerSerialNumber) {
		this.lockerSerialNumber = lockerSerialNumber;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
    
}
