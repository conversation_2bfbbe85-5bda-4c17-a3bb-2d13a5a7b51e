package com.rutong.platform.common.service;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.core.dao.Dao;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.BeanUtils;
import com.rutong.framework.utils.FilterConditionBuilder;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.sys.entity.SysNotice;

@Transactional
public abstract class BaseService<T extends BaseEntity> {
	@Resource
	protected Dao dao;

	@SuppressWarnings("unchecked")
	public Class<T> getPersistentClass() {
		Type superClass = getClass().getGenericSuperclass();
		if (superClass instanceof ParameterizedType) {
			Type[] actualTypeArguments = ((ParameterizedType) superClass).getActualTypeArguments();
			if (actualTypeArguments.length > 0) {
				Class<T> type = (Class<T>) actualTypeArguments[0];
				return type;
			}
		}
		return null;
	}

	public T findById(String id) {
		return dao.findById(getPersistentClass(), id);
	}

	public List<T> findAll() {
		return dao.findAll(getPersistentClass());
	}

	public void save(T t) {
		LoginUser loginUser = SecurityUtils.getLoginUser();
		t.setCreateBy(loginUser.getUsername());
		t.setCreateTime(new Date());
		dao.persist(t);
	}

	public void saveAll(List<T> ts) {
		int batchSize = 50; // 每批次保存的数量
		for (int i = 0; i < ts.size(); i++) {
			save(ts.get(i));
			if (i % batchSize == 0 && i > 0) {
				dao.flush(); // 刷新持久化上下文，执行批量插入
				dao.clear(); // 清除持久化上下文，避免内存泄漏
			}
		}
		dao.flush(); // 刷新持久化上下文，执行批量插入
		dao.clear(); // 清除持久化上下文，避免内存泄漏
	}

	public T update(T t) {
		T object = findById(t.getId());
		BeanUtils.copyProperties(object, t);
		LoginUser loginUser = SecurityUtils.getLoginUser();
		object.setUpdateBy(loginUser.getUsername());
		object.setUpdateTime(new Date());
		dao.persist(object);
		return object;
	}

    public List<T> findAllByObject(Object o, List<SortCondition> sorts) {
        return dao.findAll(getPersistentClass(), null, FilterConditionBuilder.buildFilters(o), sorts);
    }

    public List<T> findAll(List<FilterCondition> filters, List<SortCondition> sorts) {
		return dao.findAll(getPersistentClass(), null, filters, sorts);
	}

	public PageInfo<T> findAllByPage(GridParams gridParams, List<FilterCondition> filters, List<SortCondition> sorts) {
		Long count = dao.count(getPersistentClass(), filters);
		List<T> dataList = new ArrayList<T>();
		if (count > 0L) {
			dataList = dao.findAll(getPersistentClass(), gridParams, filters, sorts);
		}
		return new PageInfo<T>(count, dataList);
	}

	public void delete(T entity) {
		dao.delete(findById(entity.getId()));
	}

	public long count() {
		return dao.countByProperty(SysNotice.class);
	}


	public PageInfo<T> findAllByTree(GridParams gridParams, List<FilterCondition> filters, List<SortCondition> sorts) {
		return null;
	}

}
