package com.rutong.platform.device.service;

import java.math.BigInteger;

import org.springframework.stereotype.Service;

import com.rutong.framework.constant.GlobalContsant;
import com.rutong.framework.core.exception.ServiceException;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.device.entity.DevInCabinet;

@Service
public class InCabinetService extends BaseService<DevInCabinet> {

	@Override
	public void save(DevInCabinet t) {
		Integer startNo = t.getStartNo();
		Integer endNo = t.getEndNo();
		// 先查询状态
		String sql = "select count(1) from dev_in_cabinet dc where dc.cab_no >= ?0 and dc.cab_no <= ?1";
		BigInteger count = dao.countSQLQuery(sql, startNo, endNo);
		if (count.longValue() > 0) {
			throw new ServiceException("生成柜号重复");
		}
		for (; startNo <= endNo; startNo++) {
			DevInCabinet devInCabinet = new DevInCabinet();
			devInCabinet.setCabNo(startNo);
			devInCabinet.setStatus(GlobalContsant.DEV_IN_CABINET_STATUS_0);
			super.save(devInCabinet);
		}
	}

	public DevInCabinet findByCabNo(String lockerSerialNumber) {
		return dao.findByPropertyFirst(DevInCabinet.class, DevInCabinet.FIELD_CABNO,lockerSerialNumber);
	}
}
