package com.rutong.platform.sys.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.Log;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysConfig;
import com.rutong.platform.sys.service.SysConfigService;

@RestController
@RequestMapping("/system/config")
@LogDesc(title = "配置管理")
public class SysConfigController extends CrudController<SysConfig> {

	@Autowired
	private SysConfigService sysConfigService;

	@Override
	public BaseService<SysConfig> getService() {
		return sysConfigService;
	}

	@Log(desc = "清空缓存")
	@PostMapping(value = "/refreshCache.do")
	public ResultBean refreshCache() {
		sysConfigService.resetConfigCache();
		return ResultBean.success();
	}

}
