package com.rutong.platform.wechat.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 洗涤信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WashingInfoDTO {
    
    /**
     * 用户姓名
     */
    private String userName;
    
    /**
     * 员工工号
     */
    private String employeeId;
    
    /**
     * 洗涤信息列表
     */
    private List<WashingItemDTO> washingItems;
} 