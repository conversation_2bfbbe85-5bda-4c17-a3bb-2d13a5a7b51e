package com.rutong.platform.client.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * @ClassName BusinessCommunication * @Description 合同条款
 * <AUTHOR>
 * @Date 15:51 2024/8/22
 * @Version 1.0
 **/
@Data
@Entity
@Table(name = "cli_clause_info")
public class ClauseInfo extends BaseEntity {
    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 条款名称
     */
    private String name;
    /**
     * 条款内容
     */
    private String content;

    /**
     * 备注
     */
    private String remarks;
}
