package com.rutong.platform.wechat.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.*;
import java.time.LocalDateTime;

// 使用Lombok的@Data注解，自动生成getter、setter、toString、equals和hashCode方法
@Data
@Entity
@Table(name = "wechat_faq")
public class WechatFaq {
    // 唯一标识符，用于标识每个FAQ记录，使用String类型避免JavaScript精度损失
    @Id
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "uuid2")
    @Column(columnDefinition = "VARCHAR(36)")
    private String id;
    
    // 问题内容，存储FAQ的问题部分
    @Column(nullable = false)
    private String question;
    
    // 答案内容，存储FAQ的答案部分
    @Column(nullable = false)
    private String answer;
    
    // 创建时间，记录FAQ创建的日期和时间
    @Column(name = "create_time")
    private LocalDateTime createTime;
    
    // 更新时间，记录FAQ最后更新的日期和时间
    @Column(name = "update_time")
    private LocalDateTime updateTime;
} 