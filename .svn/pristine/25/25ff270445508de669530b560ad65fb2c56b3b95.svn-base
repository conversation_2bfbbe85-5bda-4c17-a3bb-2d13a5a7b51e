package com.rutong.platform.app.controller;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.utils.StringUtils;
import com.rutong.platform.app.service.AppService;
import com.rutong.platform.app.utils.AppConstan;
import com.rutong.platform.client.entity.DemandResponse;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.DemandResponseService;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.device.entity.DevInCabinet;
import com.rutong.platform.device.entity.DevSorting;
import com.rutong.platform.device.service.InCabinetService;
import com.rutong.platform.device.service.SortingService;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.sys.service.SysUserService;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.entity.WmsException;
import com.rutong.platform.wms.entity.WmsExceptionType;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import com.rutong.platform.wms.service.WmsDeliveryService;
import com.rutong.platform.wms.service.WmsExceptionService;
import com.rutong.platform.wms.service.WmsExceptionTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 客户端接口
 */
@RestController
@RequestMapping(value = "/client")
public class ClientController {

	@Autowired
	private EmployeeProductConfigurationService employeeProductConfigurationService;
	@Autowired
	private SysUserService sysUserService;
	@Autowired
	private WmsDeliveryService wmsDeliveryService;
	@Autowired
	private InCabinetService inCabinetService;
	@Autowired
	private WmsExceptionTypeService exceptionTypeService;
	@Autowired
	private WmsExceptionService exceptionService;
	@Autowired
	private DemandResponseService demandResponseService;

	@Autowired
	private AppService appService;

	@PostMapping(value = "/loginByCardno.do")
	public ResultBean loginByName(String cardno) {
		SysUser user = sysUserService.findByCardno(cardno);
		if(StringUtils.isNull(user)) {
			return ResultBean.error("无效工号", 500);
		}
		return ResultBean.success(appService.login(user.getPhone()));
	}


	/**
	 * 根据rifd查询服装
	 *
	 * @param rfid
	 * @return
	 */
	@GetMapping("/getClothingByRfid.do")
	public ResultBean getClothingByRfid(String rfid) {
		EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfid(rfid);
		if (employeeProductConfiguration == null) {
			return ResultBean.error("无效标签", 500);
		}
		return ResultBean.success(employeeProductConfiguration);
	}

	/**
	 * 根据rifd查询服装及存衣柜信息
	 *
	 * @param rfid
	 * @return
	 */
	@GetMapping("/getClothingAndShelfByRfid.do")
	public ResultBean getClothingAndShelfByRfid(String rfid) {
		EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfid(rfid);
		if (employeeProductConfiguration == null) {
			return ResultBean.error("无效标签", 500);
		}
		DevInCabinet cabinet = inCabinetService.findByCabNo(employeeProductConfiguration.getLockerSerialNumber());
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("employeeProduct", employeeProductConfiguration);
		List<DemandResponse> demandResponses = demandResponseService
				.byRfid(employeeProductConfiguration.getRfidTagNumber());
		if (demandResponses.size() > 0) {
			map.put("error", demandResponses.get(0));
		} else {
			map.put("error", null);
		}
		map.put("cabinet", cabinet);
		return ResultBean.success(map);
	}

	/**
	 * 根据服装品类查询异常类型
	 *
	 * @param coltype
	 * @return
	 */
	@GetMapping("/getExceptionByClotype.do")
	public ResultBean getExceptionByClotype(String coltype) {
		List<WmsExceptionType> exceptionTypes = exceptionTypeService.finAllByColthingType(coltype);
		return ResultBean.success(exceptionTypes);
	}

	/**
	 * 提交隧道机盘点到的服装
	 */
	@PostMapping("/postClothingList.do")
	public ResultBean postClothingList(
			@RequestList(clazz = EmployeeProductConfiguration.class) List<EmployeeProductConfiguration> proConfigurations,
			String type) {
		// 生成一次交接记录
		wmsDeliveryService.generateBatch(proConfigurations, null, type,"pc");
		return ResultBean.success();
	}

	/**
	 * 提交检品异常信息
	 */
	@PostMapping("/postCheckException.do")
	public ResultBean postCheckException(WmsException exception) {
		exceptionService.save(exception);
		return ResultBean.success();
	}

}
