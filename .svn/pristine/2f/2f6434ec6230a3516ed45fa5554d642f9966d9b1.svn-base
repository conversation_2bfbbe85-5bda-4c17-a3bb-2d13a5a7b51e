package com.rutong.platform.wechat.controller;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.Log;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.wechat.dto.ConfigInfoDTO;
import com.rutong.platform.wechat.service.ConfigInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 配置信息控制器
 */
@RestController
@RequestMapping(value = "/wechat/configinfo")
@LogDesc(title = "配置信息")
public class ConfigInfoController {
    
    private static final Logger log = LoggerFactory.getLogger(ConfigInfoController.class);
    
    @Autowired
    private ConfigInfoService configInfoService;
    
    /**
     * 获取当前登录用户的配置信息
     * 根据用户的手机号查询配置信息，而不是员工工号，确保只返回当前登录用户的配置信息
     * 
     * @return 配置信息
     */
    @Log(title = "查询配置信息", desc = "查询当前登录用户的配置信息")
    @GetMapping(value = "/getCurrentUserConfigInfo.do")
    public ResultBean getCurrentUserConfigInfo() {
        try {
            // 检查用户是否登录
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null || loginUser.getUser() == null) {
                log.warn("用户未登录，无法查询配置信息");
                return ResultBean.error("用户未登录", 401);
            }
            
            // 查询配置信息
            ConfigInfoDTO configInfo = configInfoService.getConfigInfoByLoginUser();
            
            // 返回结果
            return ResultBean.success(configInfo);
        } catch (Exception e) {
            log.error("查询配置信息失败", e);
            return ResultBean.error("查询配置信息失败，请稍后重试", 500);
        }
    }
} 