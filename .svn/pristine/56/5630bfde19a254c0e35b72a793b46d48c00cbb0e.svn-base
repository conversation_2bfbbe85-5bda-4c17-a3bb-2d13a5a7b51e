package com.rutong.platform.wms.entity;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Transient;
import java.util.Date;

/**
 * 出库服装导出实体类
 */
@Entity
@Data
@ExcelIgnoreUnannotated
public class WmsClothingOutboundExcel extends BaseEntity{

	public static final String FIELD_STATUS = "status";
	public static final String FIELD_COL_RFID = "cloRfid";
	public static final String FIELD_COL_NUMBER = "cloNumber";

	@ExcelProperty(value = "客户名称" , index = 0)
	private String enterpriseName; //客户名称

	@ExcelProperty(value = "配置品类" , index = 1)
	private String cloType; // 品类

	@ExcelProperty(value = "产品款号" , index = 2)
	private String cloModel; // 款号

	@ExcelProperty(value = "产品颜色" , index = 3)
	private String cloColor; // 颜色

	@ExcelProperty(value = "LOGO位置" , index = 4)
	private String cloLogo; // logo位置

	@ExcelProperty(value = "产品规格" , index = 5)
	private String cloSpec; // 规格

	@ExcelProperty(value = "数量" , index = 6)
	private Long cloCount; // 数量

	@ExcelProperty(value = "产品序列号" , index = 7)
	private String cloNumber; // 序列号

	@ExcelProperty(value = "出库时间" , index = 8)
	private Date outboundTime;

	private String cloRfid; // rfid号

	@Column(nullable = false)
	private Integer status; // 0在库 1租赁

	private Date leaseTime; // 最后租赁时间

	@Transient
	private String result;  //原因



}
