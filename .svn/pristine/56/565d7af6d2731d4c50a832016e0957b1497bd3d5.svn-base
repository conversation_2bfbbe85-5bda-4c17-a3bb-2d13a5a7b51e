package com.rutong.platform.sys.controller;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.constant.CacheConstants;
import com.rutong.framework.constant.Constants;
import com.rutong.framework.core.redis.RedisCache;
import com.rutong.framework.utils.ValidateCode;
import com.rutong.framework.utils.sign.Base64;
import com.rutong.framework.utils.uuid.IdUtils;
import com.rutong.platform.sys.service.SysConfigService;

/**
 * 验证码操作处理
 */
@RestController
public class CaptchaController {

	@Autowired
	private RedisCache redisCache;

	@Autowired
	private SysConfigService configService;

	/**
	 * 生成验证码
	 */
	@GetMapping("/captchaImage.do")
	public ResultBean getCode(HttpServletResponse response) throws IOException {
		Map<String, Object> data = new HashMap<String, Object>();
		boolean captchaEnabled = configService.selectCaptchaEnabled();
		data.put("captchaEnabled", captchaEnabled);
		if (!captchaEnabled) {
			return ResultBean.success(data);
		}

		// 保存验证码信息
		String uuid = IdUtils.simpleUUID();
		String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;

		// 转换流信息写出
		FastByteArrayOutputStream os = new FastByteArrayOutputStream();
		String code = ValidateCode.generateCode(os);
		redisCache.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);

		data.put("uuid", uuid);
		data.put("img", Base64.encode(os.toByteArray()));
		return ResultBean.success(data);
	}
}
