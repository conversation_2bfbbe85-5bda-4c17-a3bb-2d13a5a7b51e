package com.rutong.platform.en.entity;

import com.rutong.platform.sys.entity.BaseEntity;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Entity;
import javax.persistence.Table;
@Entity
@Table(name = "en_workers")
@DynamicUpdate
public class EnWorkers extends BaseEntity {
    // name: 员工姓名
    private String name;
    // phone: 员工电话
    private String phone;
    // sex: 员工性别
    private String sex;
    // dept: 员工所在部门
    private String dept;
    // post: 员工职位
    private String post;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getDept() {
        return dept;
    }

    public void setDept(String dept) {
        this.dept = dept;
    }

    public String getPost() {
        return post;
    }

    public void setPost(String post) {
        this.post = post;
    }
}

