package com.rutong.platform.sys.entity;

import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import org.hibernate.annotations.DynamicUpdate;

@Entity
@Table(name = "sys_user")
@DynamicUpdate
public class SysUser extends BaseEntity{
	
	public static String USERNAME = "username";
	@Column(nullable = false,unique = true,length = 40)
	private String username;
	// 用户类型 00 管理员 01 普通用户
	@Column(nullable = false,length = 2)
	private String usertype;
	@Column(nullable = false,length = 100)
	private String nickname;
	/** 密码 */
	@Column(nullable = false,length = 200)
	private String password;
	
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "sys_user_role",
            joinColumns = {@JoinColumn(name = "user_id",referencedColumnName = "id")},
            inverseJoinColumns = {@JoinColumn(name = "role_id",referencedColumnName = "id")})
	private Set<SysRole> roles;
	
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dept_id",nullable = false)
	private SysDept dept;
	
	public SysDept getDept() {
		return dept;
	}

	public void setDept(SysDept dept) {
		this.dept = dept;
	}

	public String getUsertype() {
		return usertype;
	}

	public void setUsertype(String usertype) {
		this.usertype = usertype;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public Set<SysRole> getRoles() {
		return roles;
	}

	public void setRoles(Set<SysRole> roles) {
		this.roles = roles;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getNickname() {
		return nickname;
	}

	public void setNickname(String nickname) {
		this.nickname = nickname;
	}

}
