package com.rutong.platform.wms.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.rutong.platform.common.entity.BaseEntity;

/**
 * 检品异常上报
 */
@Entity
@Table(name = "wms_exception")
public class WmsException extends BaseEntity {

    private String cloType; // 品类
    private String cloModel; // 款号
    private String workWearConfigurationCategory; // 款号
    private String cloColor; // 颜色
    private String cloLogo; // logo位置
    private String cloSpec; // 规格
    private String cloRfid; // rfid号
    private String cloNumber; // 序列号
    private String etype; //异常信息
    private String status; //状态 //未维修 ，已处理
    private String cloWorkType; //工作服品类
    private String employeeName; //员工姓名
    private String department; //部门

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getWorkWearConfigurationCategory() {
        return workWearConfigurationCategory;
    }

    public void setWorkWearConfigurationCategory(String workWearConfigurationCategory) {
        this.workWearConfigurationCategory = workWearConfigurationCategory;
    }

    public String getCloWorkType() {
        return cloWorkType;
    }

    public void setCloWorkType(String cloWorkType) {
        this.cloWorkType = cloWorkType;
    }

    public String getCloType() {
        return cloType;
    }

    public void setCloType(String cloType) {
        this.cloType = cloType;
    }

    public String getCloModel() {
        return cloModel;
    }

    public void setCloModel(String cloModel) {
        this.cloModel = cloModel;
    }

    public String getCloColor() {
        return cloColor;
    }

    public void setCloColor(String cloColor) {
        this.cloColor = cloColor;
    }

    public String getCloLogo() {
        return cloLogo;
    }

    public void setCloLogo(String cloLogo) {
        this.cloLogo = cloLogo;
    }

    public String getCloSpec() {
        return cloSpec;
    }

    public void setCloSpec(String cloSpec) {
        this.cloSpec = cloSpec;
    }

    public String getCloRfid() {
        return cloRfid;
    }

    public void setCloRfid(String cloRfid) {
        this.cloRfid = cloRfid;
    }

    public String getCloNumber() {
        return cloNumber;
    }

    public void setCloNumber(String cloNumber) {
        this.cloNumber = cloNumber;
    }

    public String getEtype() {
        return etype;
    }

    public void setEtype(String etype) {
        this.etype = etype;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

}
