package com.rutong.platform.device.controller;

import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.device.entity.DevRecordSorting;
import com.rutong.platform.device.entity.DevSorting;
import com.rutong.platform.device.service.DevRecordSortingService;
import com.rutong.platform.device.service.SortingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.alibaba.excel.EasyExcel;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;
import com.rutong.platform.device.excel.DevRecordSortingExcel;
import org.apache.commons.lang3.StringUtils;

@RestController
@RequestMapping("/device/recordSorting")
@LogDesc(title = "分拣记录")
public class DevRecordSortingController extends CrudController<DevRecordSorting> {

    @Autowired
    private DevRecordSortingService devRecordSortingService;

    @Override
    public BaseService<DevRecordSorting> getService() {
        return devRecordSortingService;
    }

    /**
     * 导出分拣记录为Excel
     * @param sortingMan 分拣人
     * @param cabNo 格口
     * @param sortingno 分拣编号
     * @param ids 勾选的记录ID列表，多个ID用逗号分隔
     */
    @GetMapping("/export.do")
    public void exportRecordSorting(HttpServletResponse response, 
            String sortingMan, 
            String cabNo, 
            String sortingno,
            String ids) throws Exception {
        
        List<DevRecordSorting> list;
        
        // 优先根据勾选的ID导出
        if (StringUtils.isNotBlank(ids)) {
            String[] idArray = ids.split(",");
            list = new java.util.ArrayList<>();
            for (String id : idArray) {
                DevRecordSorting record = devRecordSortingService.findById(id);
                if (record != null) {
                    list.add(record);
                }
            }
        } else {
            // 如果没有勾选，则根据搜索条件导出
            DevRecordSorting queryParams = new DevRecordSorting();
            if (StringUtils.isNotBlank(sortingMan)) {
                queryParams.setSortingMan(sortingMan);
            }
            if (StringUtils.isNotBlank(cabNo)) {
                queryParams.setCabNo(cabNo);
            }
            if (StringUtils.isNotBlank(sortingno)) {
                queryParams.setSortingno(sortingno);
            }

            // 根据条件查询数据
            if (StringUtils.isBlank(sortingMan) && StringUtils.isBlank(cabNo) && StringUtils.isBlank(sortingno)) {
                list = devRecordSortingService.findAll();
            } else {
                list = devRecordSortingService.findAllByObject(queryParams, null);
            }
        }

        // 转换为Excel导出结构体
        List<DevRecordSortingExcel> excelList = new java.util.ArrayList<>();
        int i = 1;
        for (DevRecordSorting record : list) {
            DevRecordSortingExcel excel = new DevRecordSortingExcel();
            excel.setIndex(i++);
            excel.setSortingMan(record.getSortingMan());
            excel.setCabNo(record.getCabNo());
            excel.setSortingno(record.getSortingno());
            excel.setEmployeeName(record.getEmployeeName());
            excel.setEmployeePhone(record.getEmployeePhone());
            excel.setDepartment(record.getDepartment());
            excel.setSubDepartment(record.getSubDepartment());
            excel.setConfigurationCategory(record.getConfigurationCategory());
            excel.setProductModelNumber(record.getProductModelNumber());
            excel.setProductColor(record.getProductColor());
            excel.setLogoPosition(record.getLogoPosition());
            excel.setProductSpecification(record.getProductSpecification());
            excel.setEmployeeId(record.getEmployeeId());
            excel.setRfidTagNumber(record.getRfidTagNumber());
            excel.setProductSerialNumber(record.getProductSerialNumber());
            excelList.add(excel);
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("分拣记录导出", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), DevRecordSortingExcel.class)
                .sheet("分拣记录")
                .doWrite(excelList);
    }

}
