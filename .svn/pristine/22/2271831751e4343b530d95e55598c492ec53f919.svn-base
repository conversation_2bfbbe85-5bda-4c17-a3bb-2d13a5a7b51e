package com.rutong.platform.wms.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.rutong.platform.common.entity.BaseEntity;

/**
 * 服装报废记录
 */
@Entity
@Table(name = "wms_clothing_outbound")
public class WmsClothingOutbound extends BaseEntity{
	
	private String cloType;  //品类
	private String cloModel;  //款号
	private String cloColor;  //颜色
	private String cloLogo;  //logo位置
	private String cloSpec;  //规格
	@Column(nullable = false,unique = true,length = 96)
	private String cloRfid;  //rfid号
	@Column(nullable = false,unique = true,length = 96)
	private String cloNumber;  //序列号
	private Date leaseTime;  //最后租赁时间	
	private Date outboundTime; //报废时间
	private String outboundResult;  //报废原因
	
	public String getCloType() {
		return cloType;
	}
	public void setCloType(String cloType) {
		this.cloType = cloType;
	}
	public String getCloModel() {
		return cloModel;
	}
	public void setCloModel(String cloModel) {
		this.cloModel = cloModel;
	}
	public String getCloColor() {
		return cloColor;
	}
	public void setCloColor(String cloColor) {
		this.cloColor = cloColor;
	}
	public String getCloLogo() {
		return cloLogo;
	}
	public void setCloLogo(String cloLogo) {
		this.cloLogo = cloLogo;
	}
	public String getCloSpec() {
		return cloSpec;
	}
	public void setCloSpec(String cloSpec) {
		this.cloSpec = cloSpec;
	}
	public String getCloRfid() {
		return cloRfid;
	}
	public void setCloRfid(String cloRfid) {
		this.cloRfid = cloRfid;
	}
	public String getCloNumber() {
		return cloNumber;
	}
	public void setCloNumber(String cloNumber) {
		this.cloNumber = cloNumber;
	}
	public Date getLeaseTime() {
		return leaseTime;
	}
	public void setLeaseTime(Date leaseTime) {
		this.leaseTime = leaseTime;
	}
	public Date getOutboundTime() {
		return outboundTime;
	}
	public void setOutboundTime(Date outboundTime) {
		this.outboundTime = outboundTime;
	}
	public String getOutboundResult() {
		return outboundResult;
	}
	public void setOutboundResult(String outboundResult) {
		this.outboundResult = outboundResult;
	}
	
}
