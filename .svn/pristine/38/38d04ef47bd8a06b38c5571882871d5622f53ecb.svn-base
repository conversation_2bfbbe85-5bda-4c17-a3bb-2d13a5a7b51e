package com.rutong.platform.wms.service;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.rutong.framework.utils.BeanUtils;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsClothing;
import com.rutong.platform.wms.entity.WmsClothingOutbound;

@Service
public class WmsClothingService extends BaseService<WmsClothing> {
	
	@Autowired
	private WmsClothingOutboundService outboundService;

	public WmsClothing findByCloRfid(String cloRfid) {
		return dao.findByPropertyFirst(WmsClothing.class, WmsClothing.FIELD_COL_RFID, cloRfid);
	}

	public WmsClothing findByCloNumber(String cloNumber) {
		return dao.findByPropertyFirst(WmsClothing.class, WmsClothing.FIELD_COL_NUMBER, cloNumber);
	}

	public void updateOutbound(WmsClothing wmsClothing,String result) {
		WmsClothingOutbound wmsClothingOutbound = new WmsClothingOutbound();
		BeanUtils.copyProperties(wmsClothingOutbound, wmsClothing);
		wmsClothingOutbound.setId(null);
		wmsClothingOutbound.setOutboundTime(new Date());
		wmsClothingOutbound.setOutboundResult(result);
		outboundService.save(wmsClothingOutbound);
		
		this.delete(wmsClothing);
	}

}
