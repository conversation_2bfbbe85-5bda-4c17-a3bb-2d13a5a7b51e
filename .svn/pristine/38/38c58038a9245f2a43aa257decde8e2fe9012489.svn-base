package com.rutong.platform.washing.service;

import com.rutong.framework.constant.GlobalContsant;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.inventory.entity.InventoryInformation;
import com.rutong.platform.washing.entity.WashingTask;
import com.rutong.platform.wms.entity.WmsDelivery;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class WashingTaskService extends BaseService<WashingTask> {

    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;

    public List<WashingTask> findNotStatusAndDriverEmployeeId(Long status, String driverEmployeeId) {
        String sql = "FROM WashingTask WHERE status != ?0 and driver_employee_id =?1 ORDER BY taskOrderNumber DESC";
        return dao.executeQuery(sql, WashingTask.class, status, driverEmployeeId);
    }
    public List<WashingTask> findStatusAndDriverEmployeeId(Long status, String driverEmployeeId) {
        String sql = "FROM WashingTask WHERE status = ?0 and driver_employee_id =?1 ORDER BY taskOrderNumber DESC";
        return dao.executeQuery(sql, WashingTask.class, status, driverEmployeeId);
    }
    public List<WashingTask> findNotStatus(Long status) {
        String sql = "FROM WashingTask WHERE status != ?0 ORDER BY taskOrderNumber DESC";
        return dao.executeQuery(sql, WashingTask.class, status);
    }
    public List<WashingTask> findInStatus(List<Long> statusList) {
        String sql = "FROM WashingTask WHERE status IN  ?0 ORDER BY taskOrderNumber DESC";
        return dao.executeQuery(sql, WashingTask.class, statusList);
    }

    /**
     * 批量处理异常
     * @param byId
     * @return
     */
    public List<WmsDeliveryDetail> calculationAnomalies(WmsDelivery byId) {
        List<WmsDeliveryDetail> one = wmsDeliveryDetailService.findByOrderIdAndRemark(byId.getOrderId(), byId.getRemark());
        List<WmsDeliveryDetail> two = wmsDeliveryDetailService.findByOrderIdAndRemark(byId.getOrderId(), GlobalContsant.CLOTHING_WASH_TYPE_7);
        //List<WmsDeliveryDetail> two = wmsDeliveryDetailService.findByOrderIdAndRemark(byId.getOrderId(), GlobalContsant.CLOTHING_WASH_TYPE_7);

        // 提取ID集合用于快速查找
        Set<String> oneIds = one.stream().map(WmsDeliveryDetail::getRfidTagNumber).collect(Collectors.toSet());
        Set<String> twoIds = two.stream().map(WmsDeliveryDetail::getRfidTagNumber).collect(Collectors.toSet());

        // 处理two列表：标记missing
        List<WmsDeliveryDetail> configList = new ArrayList<>();

        //遍历two中的WmsDeliveryDetail,获取detail.getRfidTagNumber()
        for (WmsDeliveryDetail detail : two) {
            boolean contains = oneIds.contains(detail.getRfidTagNumber());
            if (!contains) {
                detail.setAnomalyType("missing");
                configList.add(detail);
            }
        }
        // 处理one列表：标记add
        for (WmsDeliveryDetail detail : one) {
            if (!twoIds.contains(detail.getRfidTagNumber())) {
                boolean contains = oneIds.contains(detail.getRfidTagNumber());
                if (contains) {
                    detail.setAnomalyType("add");
                    configList.add(detail);
                }
            }
        }
        return configList;
    }
}
