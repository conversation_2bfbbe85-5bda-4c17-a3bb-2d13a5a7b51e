server:
  port: 8080
  servlet:
    context-path: /
spring:
  profiles:
    active: dev
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  servlet:
    multipart:
      # 单个文件的最大值, bind到CommonsMultipartResolver，请用字节数，1048576=1M，1073741824=1G
      max-file-size: 1073741824
      max-request-size: 1073741824
  # redis 配置
  redis:
    # 配置地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码 - 您需要填写正确的Redis密码，请咨询系统管理员获取
    password:
    # 连接超时时间
    timeout: 10s
# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期 48小时
  expireTime: 2880