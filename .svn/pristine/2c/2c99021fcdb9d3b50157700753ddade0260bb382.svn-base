package com.rutong.framework.bean;

import java.util.List;

public class PageInfo<T> {

	private long total;

	private List<T> data;

	private Boolean success;

	public PageInfo(long total, List<T> data) {
		this(total, data, true);
	}

	public PageInfo(long total, List<T> data, Boolean success) {
		this.total = total;
		this.data = data;
		this.success = success;
	}

	public long getTotal() {
		return total;
	}

	public void setTotal(long total) {
		this.total = total;
	}

	public List<T> getData() {
		return data;
	}

	public void setData(List<T> data) {
		this.data = data;
	}

	public Boolean getSuccess() {
		return success;
	}

	public void setSuccess(Boolean success) {
		this.success = success;
	}

}
