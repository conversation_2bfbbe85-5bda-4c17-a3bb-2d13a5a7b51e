package com.rutong.configuration;

import org.springframework.format.Formatter;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class DateFormatter implements Formatter<Date> {

    // 定义日期格式数组
    private String[] datePatterns = {
            "yyyy-MM-dd HH:mm:ss",    // 示例格式 1
            "yyyy-MM-dd",             // 示例格式 2
            "yyyy/MM/dd HH:mm:ss",    // 示例格式 3
            "MM-dd-yyyy",             // 示例格式 4
            "MM/dd/yyyy",             // 示例格式 5
            "yyyy-MM-dd'T'HH:mm:ss'Z'" // ISO 8601 示例格式 6
    };

    // 解析字符串为日期对象
    @Override
    public Date parse(String text, Locale locale) throws ParseException {
        ParseException lastException = null;  // 定义一个变量来保存最后的异常
        for (String pattern : datePatterns) {  // 遍历日期格式
            try {
                SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);  // 创建日期格式对象
                return dateFormat.parse(text);  // 尝试解析
            } catch (ParseException e) {
                lastException = e;  // 捕获解析异常并继续尝试其他格式
            }
        }
        // 如果所有格式解析失败，抛出最后的异常
        throw lastException != null ? lastException : new ParseException("Unable to parse date", 0);
    }

    // 将日期对象格式化为字符串
    @Override
    public String print(Date object, Locale locale) {
        // 返回指定格式的日期字符串
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(object);
    }
}
