package com.rutong.platform.wms.service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.rutong.framework.constant.GlobalContsant;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.BeanUtils;
import com.rutong.framework.utils.ImageUtil;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.ClientInfo;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.wms.entity.WmsDelivery;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;

@Service
public class WmsDeliveryService extends BaseService<WmsDelivery> {

	private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	
	@Autowired
	private WmsDeliveryDetailService deliveryDetailService;

	public void generateBatch(List<EmployeeProductConfiguration> proConfigurations, String signImg, String type, String source) {
		// 查询客户信息，以第一个衣服收取计算
		EmployeeProductConfiguration employeeProductConfiguration = proConfigurations.get(0);
		ClientInfo clientInfo = dao.findByPropertyFirst(ClientInfo.class, "contractNumber",
				employeeProductConfiguration.getContractNumber());
		// 将base64保存为图片并返回地址
		String httpPath = ImageUtil.base64Save(signImg);
		LoginUser loginUser = SecurityUtils.getLoginUser();
		WmsDelivery wmsDelivery = new WmsDelivery();
		wmsDelivery.setClientInfo(clientInfo);
		wmsDelivery.setBatchNo(sdf.format(new Date()));
		wmsDelivery.setRemark(type);
		wmsDelivery.setAttachment(httpPath);
		wmsDelivery.setCreateBy(loginUser.getUsername());
		wmsDelivery.setCreateTime(new Date());
		wmsDelivery.setStatus("已完成");
		wmsDelivery.setSource(source);
		wmsDelivery.setContractNumber(clientInfo.getContractNumber());
		wmsDelivery.setDeliver(loginUser.getUser().getNickname());

		// 计算上次收取数量
		if (type.equals(GlobalContsant.CLOTHING_WASH_TYPE_0) || type.equals(GlobalContsant.CLOTHING_WASH_TYPE_2)
				|| type.equals(GlobalContsant.CLOTHING_WASH_TYPE_5)) {
			wmsDelivery.setLastNum(0);
		} else if (type.equals(GlobalContsant.CLOTHING_WASH_TYPE_1)) {
			WmsDelivery delivery = findLast(clientInfo.getContractNumber(), GlobalContsant.CLOTHING_WASH_TYPE_0);
			if (delivery != null) {
				wmsDelivery.setLastNum(delivery.getTotalNum());
			}
		} else if (type.equals(GlobalContsant.CLOTHING_WASH_TYPE_3)) {
			WmsDelivery delivery = findLast(clientInfo.getContractNumber(), GlobalContsant.CLOTHING_WASH_TYPE_2);
			if (delivery != null) {
				wmsDelivery.setLastNum(delivery.getTotalNum());
			}
		} else if (type.equals(GlobalContsant.CLOTHING_WASH_TYPE_4)) {
			WmsDelivery delivery = findLast(clientInfo.getContractNumber(), GlobalContsant.CLOTHING_WASH_TYPE_2);
			if (delivery != null) {
				wmsDelivery.setLastNum(delivery.getTotalNum());
			}
		} else if (type.equals(GlobalContsant.CLOTHING_WASH_TYPE_6)) {
			WmsDelivery delivery = findLast(clientInfo.getContractNumber(), GlobalContsant.CLOTHING_WASH_TYPE_5);
			if (delivery != null) {
				wmsDelivery.setLastNum(delivery.getTotalNum());
			}
		}
		wmsDelivery.setTotalNum(proConfigurations.size());
		this.save(wmsDelivery);

		for (EmployeeProductConfiguration data : proConfigurations) {
			WmsDeliveryDetail wmsDeliveryDetail = new WmsDeliveryDetail();
			BeanUtils.copyProperties(wmsDeliveryDetail, data);
			wmsDeliveryDetail.setBatchNo(wmsDelivery.getBatchNo());
			wmsDeliveryDetail.setType(type);
			wmsDeliveryDetail.setCreateTime(new Date());
			wmsDeliveryDetail.setDelivery(wmsDelivery);
			wmsDeliveryDetail.setCreateBy(loginUser.getUsername());
			deliveryDetailService.save(wmsDeliveryDetail);
		}
	}

	private WmsDelivery findLast(String contractNumber, String type) {
		String sql = "FROM WmsDelivery WHERE contractNumber = ?0 AND remark = ?1 ORDER BY createTime DESC";
		WmsDelivery wmsDelivery = dao.executeQueryFirst(sql, WmsDelivery.class, contractNumber, type);
		return wmsDelivery;
	}

	public List<WmsDelivery> findAllByOwnerUserIdAndSource(String userId, String source) {
		return dao.findByProperty(WmsDelivery.class, "ownerUserId", userId, "source", source);
	}

}
