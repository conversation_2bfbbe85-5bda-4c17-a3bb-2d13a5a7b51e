package com.rutong.platform.wechat.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import java.util.List;

import com.rutong.platform.client.entity.DemandResponse;
import com.rutong.platform.wechat.entity.SubmitChatInfo;
import lombok.Data;

/**
 * 服务信息DTO
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ServiceInfoDTO {
    
    @JsonProperty("id")
    private String id;
    
    /**
     * 姓名
     */
    @JsonProperty("userName")
    private String userName;

    /**
     * 企业名称
     */
    @JsonProperty("enterpriseName")
    private String enterpriseName;

    /**
     * 车间部门
     */
    @JsonProperty("department")
    private String department;

    /**
     * 员工工号
     */
    @JsonProperty("employeeId")
    private String employeeId;

    /**
     * 更衣柜/列号
     */
    @JsonProperty("lockerId")
    private String lockerId;
    
    /**
     * 配置品类
     */
    @JsonProperty("configurationCategory")
    private String configurationCategory;
    
    /**
     * 产品款号
     */
    @JsonProperty("productId")
    private String productId;
    
    /**
     * 收件时间
     */
    @JsonInclude(JsonInclude.Include.ALWAYS)
    @JsonProperty("receiveTime")
    private Date receiveTime;
    
    /**
     * 送件时间
     */
    @JsonInclude(JsonInclude.Include.ALWAYS)
    @JsonProperty("deliveryTime")
    private Date deliveryTime;
    
    /**
     * 提取问题
     */
    @JsonProperty("collectionIssue")
    private String collectionIssue;
    
    /**
     * 问题处理
     */
    @JsonProperty("issueSolution")
    private String issueSolution;

    private List<SubmitChatInfo> chatInfos;
    
    /**
     * 服装信息列表
     */
    @JsonProperty("clothesInfos")
    private List<DemandResponse> clothesInfos;
    
    /**
     * 单件服装的服务信息
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class ClothesServiceInfo {
        /**
         * 配置品类
         */
        @JsonProperty("configurationCategory")
        private String configurationCategory;
        
        /**
         * 产品款号
         */
        @JsonProperty("productId")
        private String productId;
        
        /**
         * 收件时间
         */
        @JsonInclude(JsonInclude.Include.ALWAYS)
        @JsonProperty("receiveTime")
        private Date receiveTime;
        
        /**
         * 送件时间
         */
        @JsonInclude(JsonInclude.Include.ALWAYS)
        @JsonProperty("deliveryTime")
        private Date deliveryTime;
        
        /**
         * 提取问题
         */
        @JsonProperty("collectionIssue")
        private String collectionIssue;
        
        /**
         * 问题处理
         */
        @JsonProperty("issueSolution")
        private String issueSolution;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public String getLockerId() {
        return lockerId;
    }

    public void setLockerId(String lockerId) {
        this.lockerId = lockerId;
    }

    public String getConfigurationCategory() {
        return configurationCategory;
    }

    public void setConfigurationCategory(String configurationCategory) {
        this.configurationCategory = configurationCategory;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public String getCollectionIssue() {
        return collectionIssue;
    }

    public void setCollectionIssue(String collectionIssue) {
        this.collectionIssue = collectionIssue;
    }

    public String getIssueSolution() {
        return issueSolution;
    }

    public void setIssueSolution(String issueSolution) {
        this.issueSolution = issueSolution;
    }

    public List<SubmitChatInfo> getChatInfos() {
        return chatInfos;
    }

    public void setChatInfos(List<SubmitChatInfo> chatInfos) {
        this.chatInfos = chatInfos;
    }

    public List<DemandResponse> getClothesInfos() {
        return clothesInfos;
    }

    public void setClothesInfos(List<DemandResponse> clothesInfos) {
        this.clothesInfos = clothesInfos;
    }
}