package com.rutong.platform.fms.service;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.ClauseInfo;
import com.rutong.platform.client.entity.ExpenseSettlement;
import com.rutong.platform.client.service.ExpenseSettlementService;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.fms.entity.ReceiptPayable;
import com.rutong.platform.fms.entity.ReceiptPayableItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

@Service
public class ReceiptPayableService extends BaseService<ReceiptPayable> {

    @Autowired
    private ReceiptPayableItemService receiptPayableItemService;

    @Autowired
    private ExpenseSettlementService expenseSettlementService;

    public ClauseInfo findByContractNumber(String contractNumber) {
        return dao.findByPropertyFirst(ClauseInfo.class, ClauseInfo.FIELD_COL_CONTRACT_NUMBER, contractNumber);
    }

    public ResultBean gathering(ReceiptPayableItem receiptPayableItem) {
        //对账单数据同步
        ExpenseSettlement bySettlementNumber = expenseSettlementService.findBySettlementNumber(receiptPayableItem.getSettlementNumber());
        bySettlementNumber.setMonthlyPaidAmount(bySettlementNumber.getMonthlyPaidAmount().add(receiptPayableItem.getReceivableAmount()));
        bySettlementNumber.setMonthlyOutstandingAmount(bySettlementNumber.getMonthlyOutstandingAmount().subtract(receiptPayableItem.getReceivableAmount()));
        bySettlementNumber.setActualPaymentDate(new Date());
        expenseSettlementService.update(bySettlementNumber);

        ReceiptPayable byId = this.findById(receiptPayableItem.getId());
        byId.setReceiveMoney(byId.getReceiveMoney().add(receiptPayableItem.getReceivableAmount()));
        byId.setRemainMoney(byId.getRemainMoney().subtract(receiptPayableItem.getReceivableAmount()));
        if (byId.getRemainMoney().compareTo(new BigDecimal(0)) == 0) {
            byId.setStatus("全部收款");
        } else {
            byId.setStatus("部分收款");
        }
        this.update(byId);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        receiptPayableItem.setFiller(loginUser.getUser().getNickname());
        receiptPayableItem.setId(null);
        receiptPayableItem.setCreateBy(null);
        receiptPayableItemService.save(receiptPayableItem);
        return ResultBean.success("收款成功");
    }
}
