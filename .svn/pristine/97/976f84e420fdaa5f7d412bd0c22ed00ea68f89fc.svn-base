package com.rutong.platform.app.service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSONArray;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.utils.BeanUtils;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.inventory.entity.*;
import com.rutong.platform.inventory.service.*;
import com.rutong.platform.washing.entity.WashingTask;
import com.rutong.platform.washing.service.WashingTaskService;
import com.rutong.platform.wms.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import com.rutong.framework.constant.Constants;
import com.rutong.framework.core.exception.ServiceException;
import com.rutong.framework.core.exception.user.UserPasswordNotMatchException;
import com.rutong.framework.manager.AsyncManager;
import com.rutong.framework.manager.factory.AsyncFactory;
import com.rutong.framework.security.auth.SmsAuthenticationToken;
import com.rutong.framework.security.context.AuthenticationContextHolder;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.security.service.TokenService;
import com.rutong.framework.utils.MessageUtils;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.app.model.Achievement;
import com.rutong.platform.client.entity.PositionGuide;
import com.rutong.platform.client.service.PositionGuideService;
import com.rutong.platform.wms.service.WmsCarDetailService;
import com.rutong.platform.wms.service.WmsCarService;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import com.rutong.platform.wms.service.WmsDeliveryService;
import com.rutong.platform.wms.service.WmsFeedbackService;
import org.springframework.transaction.annotation.Transactional;
import com.rutong.platform.washing.service.WashingRecordService;

@Service
public class AppService {
    @Autowired
    private TokenService tokenService;
    @Resource
    private AuthenticationManager authenticationManager;
    @Autowired
    private WmsCarDetailService carDetailService;
    @Autowired
    private WmsCarService carService;
    @Autowired
    private WmsFeedbackService feedbackService;
    @Autowired
    private WmsDeliveryService deliveryService;
    @Autowired
    private WmsDeliveryDetailService deliveryDetailService;
    @Autowired
    private PositionGuideService positionGuideService;
    @Autowired
    private GodownEntryService godownEntryService;
    @Autowired
    private DeliveryRecordService deliveryRecordService;
    @Autowired
    private GodownEntryItemService godownEntryItemService;
    @Autowired
    private DeliveryRecordItemService deliveryRecordItemService;
    @Autowired
    private InventoryInformationService inventoryInformationService;
    @Autowired
    private WashingTaskService washingTaskService;
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;
    @Autowired
    private WashingRecordService washingRecordService;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public String login(String username, String password) {
        Authentication authentication = null;
        Authentication authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
        try {
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL,
                        MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me()
                        .execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        return tokenService.createToken(loginUser);
    }

    public Map<String, Object> login(String phone) {
        Authentication authentication = null;
        Authentication authenticationToken = new SmsAuthenticationToken(phone);
        try {
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(phone, Constants.LOGIN_FAIL,
                        MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(phone, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        Map<String, Object> data = new HashMap<String, Object>();
        data.put("user", loginUser);
        data.put("token", tokenService.createToken(loginUser));
        return data;
    }

    public void saveCarinfo(String carno, String result) {
        WmsCarDetail wmsCarDetail = new WmsCarDetail();
        wmsCarDetail.setCarNo(carno);
        wmsCarDetail.setResult(result);
        carDetailService.save(wmsCarDetail);

        WmsCar wmsCar = carService.findByCarno(carno);
        if (wmsCar != null) {
            wmsCar.setStatus("异常");
            carService.update(wmsCar);
        }
    }

    public List<String> getCarList() {
        List<WmsCar> carList = carService.findAll();
        return carList.stream().map(c -> c.getCarNo()).collect(Collectors.toList());
    }

    public void saveFeedback(WmsFeedback feedback) {
        feedbackService.save(feedback);
    }

    public List<WmsDelivery> getWorkList() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return deliveryService.findAllByOwnerUserIdAndSource(loginUser.getUserId(), "pda");
    }

    public Achievement getAchievement() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Achievement achievement = new Achievement();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        achievement.setToday(deliveryDetailService.countByUserAndDate(loginUser, calendar.getTime()));
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        achievement.setPreday(deliveryDetailService.countByUserAndDate(loginUser, calendar.getTime()));
        calendar.add(Calendar.DAY_OF_YEAR, -6);
        achievement.setWeekday(deliveryDetailService.countByUserAndDate(loginUser, calendar.getTime()));
        calendar.add(Calendar.DAY_OF_YEAR, -8);
        achievement.setHalfMonth(deliveryDetailService.countByUserAndDate(loginUser, calendar.getTime()));
        calendar.add(Calendar.DAY_OF_YEAR, -15);
        achievement.setOneMonth(deliveryDetailService.countByUserAndDate(loginUser, calendar.getTime()));
        return achievement;
    }

    public PositionGuide getPositionGuide() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return positionGuideService.findByDeptId(loginUser.getDeptId());
    }

    public ResultBean getOrder(String type) {
        if ("入库".equals(type)) {
            GodownEntry godownEntry = new GodownEntry();
            godownEntry.setStatus(0L);
            return ResultBean.success(godownEntryService.findAllByObject(godownEntry, null));
        } else {
            DeliveryRecord deliveryRecord = new DeliveryRecord();
            deliveryRecord.setStatus(0L);
            return ResultBean.success(deliveryRecordService.findAllByObject(deliveryRecord, null));
        }
    }

    @Transactional
    public ResultBean uploadOrder(String id, String dataJson, String type) {
        if ("入库".equals(type)) {
            GodownEntry byId = godownEntryService.findById(id);
            List<GodownEntryItem> godownEntryItems = JSONArray.parseArray(dataJson, GodownEntryItem.class);
            for (GodownEntryItem godownEntryItem : godownEntryItems) {

                InventoryInformation inventoryInformation = inventoryInformationService.byRfidTagNumber(godownEntryItem.getRfidTagNumber());
                if (inventoryInformation != null) {
                    throw new ExcelAnalysisException("库存已经存在无法入库");
                }
                EmployeeProductConfiguration byRfidAndStatus = employeeProductConfigurationService.byRfid(godownEntryItem.getRfidTagNumber());
                if (byRfidAndStatus != null) {
                    InventoryInformation inventoryInformationNew = new InventoryInformation();
                    BeanUtils.copyProperties(inventoryInformationNew, byRfidAndStatus);
                    inventoryInformationNew.setId(null);
                    inventoryInformationService.save(inventoryInformationNew);
                    GodownEntryItem godownEntryItem1 = new GodownEntryItem();
                    BeanUtils.copyProperties(godownEntryItem1, byRfidAndStatus);
                    godownEntryItem1.setId(null);
                    godownEntryItem1.setParentId(id);
                    godownEntryItemService.save(godownEntryItem1);
                }


            }
            byId.setActualTime(new Date());
            byId.setQuantity(new BigDecimal(godownEntryItems.size()));
            byId.setStatus(1L);
            return ResultBean.success();
        } else {
            DeliveryRecord byId = deliveryRecordService.findById(id);
            List<DeliveryRecordItem> deliveryRecordItems = JSONArray.parseArray(dataJson, DeliveryRecordItem.class);
            for (DeliveryRecordItem deliveryRecordItem : deliveryRecordItems) {
                EmployeeProductConfiguration byRfidAndStatus = employeeProductConfigurationService.byRfid(deliveryRecordItem.getRfidTagNumber());

                InventoryInformation inventoryInformation = inventoryInformationService.byRfidTagNumber(deliveryRecordItem.getRfidTagNumber());
                if (inventoryInformation == null) {
                    throw new ExcelAnalysisException(byRfidAndStatus.getProductSerialNumber() + ":没有库存无法出库");
                }
                inventoryInformationService.delete(inventoryInformation);
                if (byRfidAndStatus != null) {
                    DeliveryRecordItem deliveryRecordItem1 = new DeliveryRecordItem();
                    BeanUtils.copyProperties(deliveryRecordItem1, byRfidAndStatus);
                    deliveryRecordItem1.setId(null);
                    deliveryRecordItem1.setParentId(id);
                    deliveryRecordItemService.save(deliveryRecordItem1);
                }

            }
            byId.setActualTime(new Date());
            byId.setQuantity(new BigDecimal(deliveryRecordItems.size()));
            byId.setStatus(1L);
            return ResultBean.success();
        }
    }

    public ResultBean getWashingTask(Long status) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(status==null){
            return ResultBean.success(washingTaskService.findNotStatusAndDriverEmployeeId(3L, loginUser.getUserId()));
        }
        if(status==3L){
//        washingTask.setStatus(0L);
            return ResultBean.success(washingTaskService.findNotStatusAndDriverEmployeeId(3L, loginUser.getUserId()));
        }else{
//            LoginUser loginUser = SecurityUtils.getLoginUser();
//        washingTask.setStatus(0L);
            return ResultBean.success(washingTaskService.findStatusAndDriverEmployeeId(status, loginUser.getUserId()));
        }
    }

    public ResultBean getWashingTaskItem(String id) {
        WmsDelivery wmsDelivery = new WmsDelivery();
        wmsDelivery.setOrderId(id);
        List<WmsDelivery> byContractNumberAndProductSerialNumber = deliveryService.findAllByObject(wmsDelivery, null);
        return ResultBean.success(byContractNumberAndProductSerialNumber);

    }

    public ResultBean getWashingTaskItemInfo(String id) {
        List<WmsDeliveryDetail> byContractNumberAndProductSerialNumber = deliveryDetailService.findByDeliveryId(id);
        return ResultBean.success(byContractNumberAndProductSerialNumber);
    }

    public ResultBean getWashingTaskPc() {
        List<Long> list = new ArrayList<>();
        list.add(1L);
        list.add(2L);
        return ResultBean.success(washingTaskService.findInStatus(list));
    }

    public ResultBean overWashingTask(String id) {
        WashingTask byId = washingTaskService.findById(id);
        byId.setStatus(3L);
        washingTaskService.update(byId);
        
        // 删除生成洗涤记录的代码，因为在客户端收取时已经生成过洗涤记录
        // washingRecordService.generateWashingRecords(id);
        
        return ResultBean.success("结束成功");
    }

    /**
     * 验证隧道机扫描到的衣服的客户是否与当前任务单客户名称一致
     *
     * @param rfidList rfid列表
     * @param orderId  任务单id
     * @return
     */
    public ResultBean validClothing(List<String> rfidList, String orderId) {
        if (rfidList == null || rfidList.size() == 0) {
            return ResultBean.error("未扫描到芯片，无法提交！", 500);
        }
        // 根据任务单id查询任务单信息
        WashingTask washingTask = washingTaskService.findById(orderId);
        if (washingTask == null) {
            return ResultBean.error("任务单不存在！", 500);
        }
        //String clientName = washingTask.getClientName();
        String clientId = washingTask.getClientId();
        for (String rfid : rfidList) {
            EmployeeProductConfiguration rfid1 = employeeProductConfigurationService.byRfid(rfid);
            if (rfid1 == null) {
                return ResultBean.error("芯片未录入系统，请检查！" + rfid, 500);
            }

            //检查获取到的芯片客户信息是否和任务单客户信息保持一致
            if (!Objects.equals(rfid1.getEnterpriseId(), clientId)) {
                return ResultBean.error("存在非当前客户服装，请检查！", 500);
            }
        }

        return ResultBean.success();
    }
}
