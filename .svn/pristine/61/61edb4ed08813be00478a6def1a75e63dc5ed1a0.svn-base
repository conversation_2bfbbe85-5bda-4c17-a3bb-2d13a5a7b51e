package com.rutong.platform.wechat.service.impl;

import com.rutong.platform.wechat.entity.SysFaq;
import com.rutong.platform.wechat.mapper.SysFaqMapper;
import com.rutong.platform.wechat.service.SysFaqService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class SysFaqServiceImpl implements SysFaqService {

    @Autowired
    private SysFaqMapper sysFaqMapper;

    @Override
    public List<SysFaq> list() {
        return sysFaqMapper.selectList(null);
    }

    @Override
    public void add(SysFaq sysFaq) {
        sysFaqMapper.insert(sysFaq);
    }

    @Override
    public void update(SysFaq sysFaq) {
        sysFaqMapper.updateById(sysFaq);
    }

    @Override
    public void delete(Long id) {
        sysFaqMapper.deleteById(id);
    }
} 