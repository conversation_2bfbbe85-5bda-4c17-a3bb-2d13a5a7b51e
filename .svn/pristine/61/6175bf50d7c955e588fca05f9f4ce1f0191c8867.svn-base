package com.rutong.platform.client.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 条款商品配置
 */
@Data
@Entity
@Table(name = "clause_product_config")
public class ClauseProductConfig extends BaseEntity {
    public static final String FIELD_COL_CONTRACT_NUMBER = "contractNumber";

    /**
     * 合同编号
     */
    private String contractNumber;

    @ExcelProperty(value = "部门", index = 0)
    private String department;

    @ExcelProperty(value = "配置品类", index = 1)
    private String category;

    @ExcelProperty(value = "产品款号", index = 2)
    private String productCode;

    @ExcelProperty(value = "产品颜色", index = 3)
    private String color;

    @ExcelProperty(value = "LOGO位置", index = 4)
    private String logoPosition;

    @ExcelProperty(value = "产品规格", index = 5)
    private String specification;

    @ExcelProperty(value = "协议各品类配置数量", index = 6)
    private Integer configQuantity;

    @ExcelProperty(value = "存衣柜协议配置型号", index = 7)
    private String storageCabinetModel;

    @ExcelProperty(value = "协议存衣柜配置数量", index = 8)
    private Integer storageCabinetQuantity;

    @ExcelProperty(value = "脏衣柜协议配置型号", index = 9)
    private String dirtyCabinetModel;

    @ExcelProperty(value = "协议脏衣柜配置数量", index = 10)
    private Integer dirtyCabinetQuantity;

    @ExcelProperty(value = "协议配置品类使用月份", index = 11)
    private Integer usageMonth;

    @ExcelProperty(value = "协议每周作息时间", index = 12)
    private String weeklySchedule;

    @ExcelProperty(value = "协议每周换洗日期", index = 13)
    private String weeklyChangeDate;

    @ExcelProperty(value = "协议每周换洗次数", index = 14)
    private Integer weeklyChangeCount;

    @ExcelProperty(value = "协议单次清洗租赁费", index = 15)
    private Double singleCleaningFee;

    @ExcelProperty(value = "协议每周清洗租赁费", index = 16)
    private Double weeklyCleaningFee;

    @ExcelProperty(value = "协议服装结算价", index = 17)
    private Double settlementPrice;

    @ExcelProperty(value = "备注", index = 18)
    private String remarks;

}
