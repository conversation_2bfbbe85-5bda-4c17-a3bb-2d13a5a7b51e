package com.rutong.platform.wms.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.utils.DataAuthUtil;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsException;
import com.rutong.platform.wms.service.WmsExceptionService;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/wms/exception")
@LogDesc(title = "检品异常")
public class WmsExceptionController extends CrudController<WmsException>{
	
	@Autowired
	private WmsExceptionService exceptionService;

	@Override
	public BaseService<WmsException> getService() {
		return exceptionService;
	}

	@Override
	public ResultBean update(WmsException entity) {
		entity.setStatus("已修复");
		return super.update(entity);
	}
	
	@Override
	public PageInfo<WmsException> findAllByPage(GridParams gridParams,
			@RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
			@RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
		if (sorts.size() == 0) {
			SortCondition sortCondition = new SortCondition();
			sortCondition.setDirection(SortCondition.SORT_DESC);
			sortCondition.setProperty(BaseEntity.FIELD_CREATE_TIME);
			sorts.add(sortCondition);
		}
		
		// 处理时间范围过滤
		for (int i = 0; i < filters.size(); i++) {
			FilterCondition filter = filters.get(i);
			// 如果是日期字符串，需要转换为Date对象
			if ((filter.getProperty().equals("createTime") || filter.getProperty().equals("updateTime")) 
					&& filter.getValue() instanceof String) {
				try {
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					String dateStr = (String) filter.getValue();
					Date date = sdf.parse(dateStr);
					filter.setValue(date);
				} catch (ParseException e) {
					e.printStackTrace();
				}
			}
		}
		
		//增加数据权限
		DataAuthUtil.setDataAuth(filters);
		return getService().findAllByPage(gridParams, filters, sorts);
	}
}
