package com.rutong.framework.security.utils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.alibaba.fastjson2.JSON;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.framework.utils.SpringUtils;
import com.rutong.framework.utils.StringUtils;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.sys.entity.SysDept;
import com.rutong.platform.sys.entity.SysRole;
import com.rutong.platform.sys.service.SysDeptService;
import com.rutong.platform.sys.service.SysRoleService;

public class DataAuthUtil {
	private static SysRoleService roleService = SpringUtils.getBean(SysRoleService.class);
	private static SysDeptService deptService = SpringUtils.getBean(SysDeptService.class);

	public static void setDataAuth(List<FilterCondition> filters) {
		LoginUser loginUser = SecurityUtils.getLoginUser();
		if(loginUser.getUser().getUsertype().equals("01")) {
			List<SysRole> roleList = roleService.getRolesByUserid(loginUser.getUserId());
			int datascope = 6;
			SysRole sysRole = null;
			//取权限最大的角色
			for (SysRole role : roleList) {
				int scope = role.getDatascope();
				if(scope < datascope) {
					datascope = scope;
					sysRole = role;
				}
			}
			if(sysRole == null) {
				//查询公共数据
				FilterCondition filterCondition = new FilterCondition();
				filterCondition.setProperty(BaseEntity.FIELD_OWNER_USER_ID);
				filterCondition.setOperator(FilterCondition.EQ);
				filterCondition.setValue(null);
				filters.add(filterCondition);
			}else {
				if(datascope == 2) {
					//自定义 获取自定义的部门id
					String[] deptIds = sysRole.getDeptids().split(",");
					FilterCondition filterCondition = new FilterCondition();
					filterCondition.setProperty(BaseEntity.FIELD_OWNER_DEPT_ID);
					filterCondition.setOperator(FilterCondition.IN);
					filterCondition.setValue(deptIds);
					filters.add(filterCondition);
				}else if(datascope == 3) {
					//本部门
					FilterCondition filterCondition = new FilterCondition();
					filterCondition.setProperty(BaseEntity.FIELD_OWNER_DEPT_ID);
					filterCondition.setOperator(FilterCondition.EQ);
					filterCondition.setValue(loginUser.getDeptId());
					filters.add(filterCondition);
				}else if(datascope == 4) {
					//本部门及以下
					Set<String> deptIds = new HashSet<String>();
					deptIds.add(loginUser.getDeptId());
					SysDept sysDept = deptService.findById(loginUser.getDeptId());
					if(StringUtils.isNotEmpty(sysDept.getAncestors())) {
						String[] ids = sysDept.getAncestors().split(",");
						deptIds.addAll(Arrays.asList(ids));
					}
					FilterCondition filterCondition = new FilterCondition();
					filterCondition.setProperty(BaseEntity.FIELD_OWNER_DEPT_ID);
					filterCondition.setOperator(FilterCondition.IN);
					filterCondition.setValue(deptIds);
					filters.add(filterCondition);
				}else if(datascope == 5) {
					//本人数据
					FilterCondition filterCondition = new FilterCondition();
					filterCondition.setProperty(BaseEntity.FIELD_OWNER_USER_ID);
					filterCondition.setOperator(FilterCondition.EQ);
					filterCondition.setValue(loginUser.getUserId());
					filters.add(filterCondition);
				}
			}
		}
	}

}
