package com.rutong.platform.client.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.rutong.platform.client.excel.UniversalDateConverter;
import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;
//import org.jdom2.DataConversionException;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 服务记录查询（产品配置信息）
 */
@MappedSuperclass
@Data
public abstract class EmployeeProductConfigurationDto extends BaseEntity {

    public static final String FIELD_COL_CONTRACT_NUMBER = "contractNumber";
    public static final String FIELD_COL_RFID_TAG_NUMBER = "rfidTagNumber";
    public static final String FIELD_COL_PRODUCT_SERIAL_NUMBER = "productSerialNumber";
    public static final String FIELD_RFID_TAG_NUMBER = "rfidTagNumber";

    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户名称", index = 0)
    private String enterpriseName;

    @ExcelProperty(value = "员工姓名", index = 1)
    private String employeeName;

    @ExcelProperty(value = "员工电话", index = 2)
    private String employeePhone;

    @ExcelProperty(value = "部门", index = 3)
    private String department;

    @ExcelProperty(value = "分部门", index = 4)
    private String subDepartment;

    @ExcelProperty(value = "配置品类", index = 5)
    private String configurationCategory;

    @ExcelProperty(value = "产品款号", index = 6)
    private String productModelNumber;

    @ExcelProperty(value = "产品颜色", index = 7)
    private String productColor;

    @ExcelProperty(value = "LOGO位置", index = 8)
    private String logoPosition;

    @ExcelProperty(value = "产品规格", index = 9)
    private String productSpecification;

    @ExcelProperty(value = "协议各品类配置数量", index = 10)
    private Integer protocolConfigurationQuantity;

    @ExcelProperty(value = "员工企业工号", index = 11)
    private String employeeId;

    @ExcelProperty(value = "工作服RFID标签编码", index = 12)
    private String rfidTagNumberWork;

    @ExcelProperty(value = "RFID标签号", index = 13)
    private String rfidTagNumber;

    @ExcelProperty(value = "产品序列号", index = 14)
    private String productSerialNumber;

    @ExcelProperty(value = "存衣柜协议配置型号", index = 15)
    private String lockerModel;

    @ExcelProperty(value = "协议存衣柜配置数量", index = 16)
    private Integer lockerQuantity;

    @ExcelProperty(value = "更衣柜序列号", index = 17)
    private String lockerSerialNumber;

    @ExcelProperty(value = "脏衣柜协议配置型号", index = 18)
    private String dirtyLockerModel;

    @ExcelProperty(value = "协议脏衣柜配置数量", index = 19)
    private Integer dirtyLockerQuantity;

    @ExcelProperty(value = "脏衣柜序列号", index = 20)
    private String dirtyLockerSerialNumber;

    @ExcelProperty(value = "协议配置品类使用月份", index = 21)
    private String protocolUsageMonths;

    @ExcelProperty(value = "协议每周作息时间", index = 22)
    private String weeklySchedule;

    @ExcelProperty(value = "协议每周换洗日期", index = 23)
    private String weeklyWashDate;

    @ExcelProperty(value = "协议每周换洗次数", index = 24)
    private Integer weeklyWashFrequency;

    @ExcelProperty(value = "配置产品洗涤信息", index = 25)
    private String productWashInfo;

    @ExcelProperty(value = "协议单次清洗租赁费", index = 26)
    private BigDecimal singleWashRentalFee;

    @ExcelProperty(value = "协议每周清洗租赁费", index = 27)
    private BigDecimal weeklyWashRentalFee;

    @ExcelProperty(value = "协议服装结算价", index = 28)
    private BigDecimal clothingSettlementPrice;

    @ExcelProperty(value = "本月服装余值", index = 29)
    private BigDecimal clothingResidualValue;

    @ExcelProperty(value = "客方本月服装丢失次数", index = 30)
    private Integer clothingLossCount;

    @ExcelProperty(value = "客方本月服装丢失费", index = 31)
    private BigDecimal clothingLossFee;

    @ExcelProperty(value = "月合计费用", index = 32)
    private BigDecimal monthlyTotalCost;

    @ExcelProperty(value = "协议启用日期", index = 33,converter = UniversalDateConverter.class)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss") // 这里定义了日期的格式
    private Date protocolStartDate;

    @ExcelProperty(value = "协议截止日期", index = 34,converter = UniversalDateConverter.class)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss") // 这里定义了日期的格式
    private Date protocolEndDate;

    @ExcelProperty(value = "离职日期", index = 35,converter = UniversalDateConverter.class)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss") // 这里定义了日期的格式
    private Date resignationDate;

    @ExcelProperty(value = "属性", index = 36)
    private String attributes;

    @ExcelProperty(value = "状态", index = 37)
    private String status;
    @ExcelProperty(value = "服务类型", index = 38)
    private String serviceType;

    @Transient
    private String countInfo;


}
