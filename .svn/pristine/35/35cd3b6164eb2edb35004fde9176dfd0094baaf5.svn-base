package com.rutong.platform.wms.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsExceptionType;
import com.rutong.platform.wms.service.WmsExceptionTypeService;

@RestController
@RequestMapping("/wms/exceptiontype")
@LogDesc(title = "检品异常类型")
public class WmsExceptionTypeController extends CrudController<WmsExceptionType>{
	
	@Autowired
	private WmsExceptionTypeService exceptionTypeService;

	@Override
	public BaseService<WmsExceptionType> getService() {
		return exceptionTypeService;
	}

}
