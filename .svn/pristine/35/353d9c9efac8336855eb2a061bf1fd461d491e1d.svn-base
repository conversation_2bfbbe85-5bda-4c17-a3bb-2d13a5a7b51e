package com.rutong.platform.sys.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.rutong.framework.utils.StringUtils;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysRole;
import com.rutong.platform.sys.entity.SysRoleMenu;
import com.rutong.platform.sys.entity.SysUserRole;

@Service
public class SysRoleService extends BaseService<SysRole> {

	public void authrole(String id, String[] menuIds) {
		if (StringUtils.isNotEmpty(menuIds) && menuIds.length > 0) {
			// 删除旧的关联表
			dao.deleteByProperty(SysRoleMenu.class, SysRoleMenu.ROLE_ID, id);
			for (String menuId : menuIds) {
				SysRoleMenu sysRoleMenu = new SysRoleMenu();
				sysRoleMenu.setRoleid(id);
				sysRoleMenu.setMenuid(menuId);
				dao.persist(sysRoleMenu);
			}
		}
	}

	public List<String> getRoleMenuIds(String roleid) {
		List<SysRoleMenu> smList = dao.findByProperty(SysRoleMenu.class, SysRoleMenu.ROLE_ID, roleid);
		return smList.stream().map(sm -> sm.getMenuid()).collect(Collectors.toList());
	}

	public boolean checkUser(SysRole entity) {
		return dao.countByProperty(SysUserRole.class, SysUserRole.ROLE_ID, entity.getId()) > 0;
	}

	public void deleteRoleMenu(SysRole entity) {
		dao.deleteByProperty(SysRoleMenu.class, SysRoleMenu.ROLE_ID, entity.getId());
	}

}
