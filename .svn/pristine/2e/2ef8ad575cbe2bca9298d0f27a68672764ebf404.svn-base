package com.rutong.platform.wms.entity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rutong.platform.common.entity.BaseEntity;
import org.springframework.data.annotation.Transient;

/**
 * 配送明细
 */
@Entity
@Table(name = "wms_delivery_detail")
@ExcelIgnoreUnannotated
public class WmsDeliveryDetail extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "delivery_id")
    private WmsDelivery delivery;


    /** 批次号 */
    @ExcelProperty(value = "批次号")
    private String batchNo;

    /** 合同编号 */
    @ExcelProperty(value = "合同编号")
    private String contractNumber;

    /**客户名称*/
    @ExcelProperty(value = "客户名称")
    private String enterpriseName;

    /** 员工姓名 */
    @ExcelProperty(value = "员工姓名")
    private String employeeName;

    /**员工电话*/
    @ExcelProperty(value = "员工电话")
    private String employeePhone;

    /** 部门 */
    @ExcelProperty(value = "部门")
    private String department;

    /** 子部门 */
    @ExcelProperty(value = "子部门")
    private String subDepartment;

    /** 配置品类 */
    @ExcelProperty(value = "配置品类")
    private String configurationCategory;

    /** 产品款号 */
    @ExcelProperty(value = "产品款号")
    private String productModelNumber;

    /** 产品颜色 */
    @ExcelProperty(value = "产品颜色")
    private String productColor;

    /** Logo位置 */
    private String logoPosition;

    /** 产品规格 */
    @ExcelProperty(value = "产品规格")
    private String productSpecification;

    /**员工工号*/
    @ExcelProperty(value = "员工工号")
    private String employeeId;

    /** 员工ID */
    //private String employeeID;

    /** RFID标签号 */
    @ExcelProperty(value = "RFID标签号")
    private String rfidTagNumber;

    /** 产品序列号 */
    @ExcelProperty(value = "产品序列号")
    private String productSerialNumber;

    /** 存衣柜序列号 */
    @ExcelProperty(value = "存衣柜序列号")
    private String lockerSerialNumber;

    /** 类型：仓储端接单,客户端送达,客户端收取,洗涤端送达,外洗端送达,外洗端收取,外洗回检端送达 */
    private String type;

    /** 状态 */
    @ExcelProperty(value = "状态")
    private Long status;

    /** 任务ID */
    private String orderId;

    /** 任务单号 */
    private String orderNumber;





    @Transient
    private String anomalyType; //异常类型

    public String getAnomalyType() {
        return anomalyType;
    }

    public void setAnomalyType(String anomalyType) {
        this.anomalyType = anomalyType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public WmsDelivery getDelivery() {
        return delivery;
    }

    public void setDelivery(WmsDelivery delivery) {
        this.delivery = delivery;
    }

    public String getContractNumber() {
        return contractNumber;
    }

    public void setContractNumber(String contractNumber) {
        this.contractNumber = contractNumber;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getSubDepartment() {
        return subDepartment;
    }

    public void setSubDepartment(String subDepartment) {
        this.subDepartment = subDepartment;
    }

    public String getConfigurationCategory() {
        return configurationCategory;
    }

    public void setConfigurationCategory(String configurationCategory) {
        this.configurationCategory = configurationCategory;
    }

    public String getProductModelNumber() {
        return productModelNumber;
    }

    public void setProductModelNumber(String productModelNumber) {
        this.productModelNumber = productModelNumber;
    }

    public String getProductColor() {
        return productColor;
    }

    public void setProductColor(String productColor) {
        this.productColor = productColor;
    }

    public String getLogoPosition() {
        return logoPosition;
    }

    public void setLogoPosition(String logoPosition) {
        this.logoPosition = logoPosition;
    }

    public String getProductSpecification() {
        return productSpecification;
    }

    public void setProductSpecification(String productSpecification) {
        this.productSpecification = productSpecification;
    }

    /*public String getEmployeeID() {
        return employeeID;
    }

    public void setEmployeeID(String employeeID) {
        this.employeeID = employeeID;
    }*/

    public String getRfidTagNumber() {
        return rfidTagNumber;
    }

    public void setRfidTagNumber(String rfidTagNumber) {
        this.rfidTagNumber = rfidTagNumber;
    }

    public String getProductSerialNumber() {
        return productSerialNumber;
    }

    public void setProductSerialNumber(String productSerialNumber) {
        this.productSerialNumber = productSerialNumber;
    }

    public String getLockerSerialNumber() {
        return lockerSerialNumber;
    }

    public void setLockerSerialNumber(String lockerSerialNumber) {
        this.lockerSerialNumber = lockerSerialNumber;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getEmployeePhone() {
        return employeePhone;
    }

    public void setEmployeePhone(String employeePhone) {
        this.employeePhone = employeePhone;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

}
