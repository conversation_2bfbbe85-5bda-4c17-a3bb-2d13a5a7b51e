package com.rutong.platform.sys.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.security.service.PermissionService;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.framework.utils.TreeBuilder;
import com.rutong.platform.sys.entity.SysConfig;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.sys.entity.SysUserSetting;
import com.rutong.platform.sys.pojo.LoginBody;
import com.rutong.platform.sys.pojo.MenuDataItem;
import com.rutong.platform.sys.service.SysConfigService;
import com.rutong.platform.sys.service.SysLoginService;
import com.rutong.platform.sys.service.SysMenuService;
import com.rutong.platform.sys.service.SysUserSettingService;

/**
 * 登录验证
 */
@RestController
public class SysLoginController {
	@Autowired
	private SysLoginService loginService;
	@Autowired
	private SysMenuService menuService;
	@Autowired
	private PermissionService permissionService;
	@Autowired
	private SysConfigService configService;
	@Autowired
	private SysUserSettingService settingService;
	/**
	 * 登录方法
	 * 
	 * @param loginBody 登录信息，包含用户名和密码等登录所需的数据
	 * @return 结果，返回一个ResultBean对象，其中包含登录成功后的token信息
	 */
	@PostMapping("/login.do") // 使用PostMapping注解指定该方法处理POST请求，URL路径为"/login.do"
	public ResultBean login(@RequestBody LoginBody loginBody) { // 方法参数loginBody通过@RequestBody注解从请求体中获取，类型为LoginBody
		String token = loginService.login(loginBody); // 调用loginService的login方法，传入loginBody，获取登录后的token
		return ResultBean.success(token); // 返回一个成功的ResultBean对象，其中包含token信息
	}

	/**
	 * 获取用户信息
	 * 
	 * @return 用户信息
	 */
	@GetMapping("/getInfo.do")
	public ResultBean getInfo() {
    // 获取当前登录的用户信息
		SysUser user = SecurityUtils.getLoginUser().getUser();
    // 获取当前用户的权限信息
		Set<String> permissions = permissionService.getMenuPermission(user);
    // 创建一个用于存储返回数据的Map
		Map<String, Object> data = new HashMap<String, Object>();
		
    // 创建一个用于存储系统设置的Map
		Map<String, Object> sysSetting = new HashMap<String, Object>();
    // 获取所有系统配置信息
		List<SysConfig> configs = configService.findAll();
    // 遍历系统配置信息，将其键值对存入sysSetting中
		for (SysConfig sysConfig : configs) {
			sysSetting.put(sysConfig.getConfigkey(), sysConfig.getConfigvalue());
		}
    // 根据用户ID获取用户设置信息
		SysUserSetting userSetting = settingService.findByUserid(user.getId());
    // 如果用户设置信息为空，则创建一个新的用户设置对象，并设置默认布局为"side"
		if(userSetting == null) {
			userSetting = new SysUserSetting();
			userSetting.setLayout("side");
		}
    // 将用户信息、权限信息、系统设置信息和用户设置信息存入data中
		data.put("user", user);
		data.put("permissions", permissions);
		data.put("sysSetting", sysSetting);
		data.put("userSetting", userSetting);
    // 返回成功结果，包含用户信息、权限信息、系统设置信息和用户设置信息
		return ResultBean.success(data);//登录token
	}

	/**
	 * 获取路由信息
	 * 
	 * @return 路由信息
	 */
	@GetMapping("/getRouters.do")
	public ResultBean getRouters() {
		LoginUser loginUser = SecurityUtils.getLoginUser();
		List<MenuDataItem> menus = menuService.selectMenuTreeByUserId(loginUser);
		return ResultBean.success(TreeBuilder.buildListToTree(menus));
	}
}
