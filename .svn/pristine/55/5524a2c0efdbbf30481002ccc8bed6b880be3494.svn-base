package com.rutong.platform.wms.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.excel.EasyExcel;
import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.utils.DataAuthUtil;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsException;
import com.rutong.platform.wms.excel.WmsExceptionExcel;
import com.rutong.platform.wms.service.WmsExceptionService;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/wms/exception")
@LogDesc(title = "检品异常")
public class WmsExceptionController extends CrudController<WmsException>{
	
	private static final Logger log = LoggerFactory.getLogger(WmsExceptionController.class);
	
	@Autowired
	private WmsExceptionService exceptionService;

	@Override
	public BaseService<WmsException> getService() {
		return exceptionService;
	}

	/*@Override
	public ResultBean save(WmsException entity) {
		entity.setStatus("未处理");
		return super.save(entity);
	}*/

	@Override
	public ResultBean update(WmsException entity) {
		entity.setStatus("已修复");
		return super.update(entity);
	}


    /**
     * 分页查询检品异常记录
     * @param gridParams
     * @param filters
     * @param sorts
     * @return
     */
	@Override
	public PageInfo<WmsException> findAllByPage(GridParams gridParams,
            //通过 @RequestList 注解将请求中的数据转换为 FilterCondition 和 SortCondition 对象列表
			@RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
			@RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
		if (sorts.size() == 0) {
			SortCondition sortCondition = new SortCondition();
			sortCondition.setDirection(SortCondition.SORT_DESC);
			sortCondition.setProperty(BaseEntity.FIELD_CREATE_TIME);
			sorts.add(sortCondition);
		}
		
		// 处理时间范围过滤
		processDateFilters(filters);
		
		// 处理客户名称筛选
		processClientNameFilter(filters);
		
		//增加数据权限
		DataAuthUtil.setDataAuth(filters);
		return getService().findAllByPage(gridParams, filters, sorts);
	}
	
	/**
     * 导出异常记录为Excel
     *
     * @param response HTTP响应
     * @param filters  筛选条件列表（与查询列表使用相同参数）
     * @param ids      勾选的记录ID列表，多个ID用逗号分隔
     */
    @GetMapping("/export.do")
    public void exportExceptions(
            HttpServletResponse response,
            @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
            @RequestParam(required = false) String ids) throws Exception {

        log.info("开始导出异常记录, 筛选条件数量: {}, 勾选ID: {}", 
                filters != null ? filters.size() : 0, 
                StringUtils.isNotBlank(ids) ? ids : "无");
        
        // 记录筛选条件用于调试
        if (filters != null && !filters.isEmpty()) {
            for (FilterCondition filter : filters) {
                log.info("筛选条件: {}={}{}", 
                        filter.getProperty(), filter.getOperator(), filter.getValue());
            }
        }
        
        // 处理客户名称筛选条件
        processClientNameFilter(filters);

        List<WmsException> list;

        // 优先根据勾选的ID导出
        if (StringUtils.isNotBlank(ids)) {
            log.info("根据勾选ID导出数据");
            String[] idArray = ids.split(",");
            list = new ArrayList<>();
            for (String id : idArray) {
                WmsException exception = exceptionService.findById(id);
                if (exception != null) {
                    list.add(exception);
                }
            }
        }
        // 根据筛选条件导出
        else {
            log.info("根据筛选条件导出数据");
            
            // 处理时间范围过滤
            processDateFilters(filters);

            // 增加数据权限
            DataAuthUtil.setDataAuth(filters);

            // 根据条件查询数据
            list = exceptionService.findAll(filters, null);
            log.info("查询到 {} 条符合条件的记录", list.size());
        }
        
        // 转换为Excel导出结构
        List<WmsExceptionExcel> excelList = new ArrayList<>();
        for (WmsException exception : list) {
            WmsExceptionExcel excel = new WmsExceptionExcel();
            excel.setCloNumber(exception.getCloNumber());
            excel.setCloType(exception.getCloType());
            excel.setCloModel(exception.getCloModel());
            excel.setCloColor(exception.getCloColor());
            excel.setCloLogo(exception.getCloLogo());
            excel.setCloSpec(exception.getCloSpec());
            excel.setCloRfid(exception.getCloRfid());
            excel.setEtype(exception.getEtype());
            excel.setStatus(exception.getStatus());
            excel.setCreateTime(exception.getCreateTime());
            excel.setUpdateTime(exception.getUpdateTime());
            excel.setEmployeeName(exception.getEmployeeName());//设置客户用户姓名
            excel.setDepartment(exception.getDepartment());//设置部门名称
            excel.setClientName(exception.getClientName());//客户名称
            
            excelList.add(excel);
        }

        log.info("导出Excel记录数: {}", excelList.size());
        
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("检品异常导出", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), WmsExceptionExcel.class)
                .sheet("检品异常")
                .doWrite(excelList);
    }
    
    /**
     * 处理客户名称筛选条件
     * 
     * 
     * @param filters 筛选条件列表
     */
    private void processClientNameFilter(List<FilterCondition> filters) {
        if (filters == null || filters.isEmpty()) {
            return;
        }
        
        // 查找clientName过滤条件，确保使用LIKE操作符进行模糊查询
        for (FilterCondition filter : filters) {
            if ("clientName".equals(filter.getProperty())) {
                log.info("处理客户名称筛选: {}", filter.getValue());
                
                // 如果操作符不是LIKE，则修改为LIKE以支持模糊查询
                if (!FilterCondition.LIKE.equals(filter.getOperator())) {
                    filter.setOperator(FilterCondition.LIKE);
                    String clientName = filter.getValue() != null ? filter.getValue().toString() : "";
                    if (StringUtils.isNotBlank(clientName) && !clientName.startsWith("%")) {
                        filter.setValue("%" + clientName + "%");
                    }
                }
                break; // 找到后退出循环
            }
        }
    }
    
    /**
     * 处理日期类型的筛选条件
     * 
     * @param filters 筛选条件列表
     */
    private void processDateFilters(List<FilterCondition> filters) {
        if (filters == null) {
            return;
        }
        
        for (int i = 0; i < filters.size(); i++) {
            FilterCondition filter = filters.get(i);
            // 如果是日期字符串，需要转换为Date对象
            if ((filter.getProperty().equals("createTime") || filter.getProperty().equals("updateTime")) 
                    && filter.getValue() instanceof String) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String dateStr = (String) filter.getValue();
                    Date date = sdf.parse(dateStr);
                    filter.setValue(date);
                } catch (ParseException e) {
                    log.error("日期格式转换错误: {}", e.getMessage());
                }
            }
        }
    }
}
