package com.rutong.platform.common.controller;

import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.common.entity.TreeEntity;
import com.rutong.platform.common.service.BaseService;

public abstract class CrudController<T extends BaseEntity> extends BaseController {

	public abstract BaseService<T> getService();

	@PostMapping(value = "/fetchdata.do")
	public PageInfo<T> findAllByPage(GridParams gridParams,
			@RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
			@RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
		if (sorts.size() == 0) {
			SortCondition sortCondition = new SortCondition();
			sortCondition.setDirection(SortCondition.SORT_DESC);
			sortCondition.setProperty(BaseEntity.CREATE_TIME);
			sorts.add(sortCondition);
		}
		return getService().findAllByPage(gridParams, filters, sorts);
	}

	@PostMapping(value = "/fetchtree.do")
	public PageInfo<T> findAllByTree(GridParams gridParams,
			@RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
			@RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
		if (filters.size() == 0) {
			FilterCondition filterCondition = new FilterCondition();
			filterCondition.setProperty(TreeEntity.PARENTID);
			filterCondition.setOperator(FilterCondition.EQ);
			filterCondition.setValue("00");
			filters.add(filterCondition);
		}
		if (sorts.size() == 0) {
			SortCondition sortCondition = new SortCondition();
			sortCondition.setDirection(SortCondition.SORT_DESC);
			sortCondition.setProperty(BaseEntity.CREATE_TIME);
			sorts.add(sortCondition);
		}
		return getService().findAllByTree(gridParams, filters, sorts);
	}

	@PostMapping(value = "/save.do")
	public ResultBean save(T entity) {
		getService().save(entity);
		return ResultBean.success(entity);
	}

	@PostMapping(value = "/update.do")
	public ResultBean update(T entity) {
		getService().update(entity);
		return ResultBean.success(entity);
	}

	@PostMapping(value = "/delete.do")
	public ResultBean delete(@RequestBody T entity) {
		getService().delete(entity);
		return ResultBean.success();
	}

	@PostMapping(value = "/deleteBatch.do")
	public ResultBean deleteBatch(@RequestBody List<T> entitys) {
		for (T t : entitys) {
			getService().delete(t);
		}
		return ResultBean.success();
	}
}
