package com.rutong.platform.app.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.ResultBean;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.wms.service.WmsDeliveryService;

@RestController
@RequestMapping(value = "/pda")
public class PdaController {

	@Autowired
	private EmployeeProductConfigurationService employeeProductConfigurationService;

	@Autowired
	private WmsDeliveryService wmsDeliveryService;
	/**
	 * 根据rifd查询服装
	 * @param rfid
	 * @return
	 */
	@GetMapping("/getClothingByRfid.do")
	public ResultBean getClothingByRfid(String rfid) {
		EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfid(rfid);
		return ResultBean.success(employeeProductConfiguration);
	}

	/**
	 * 提交盘点到的服装
	 */
	@PostMapping("/postClothingList/{type}/a.do")
	public ResultBean postClothingList(@PathVariable("type") int type,@RequestBody List<EmployeeProductConfiguration> proConfigurations) {
		//生成一次交接记录
		wmsDeliveryService.generateBatch(proConfigurations,null);
		return ResultBean.success();
		
	}
	
	
}
