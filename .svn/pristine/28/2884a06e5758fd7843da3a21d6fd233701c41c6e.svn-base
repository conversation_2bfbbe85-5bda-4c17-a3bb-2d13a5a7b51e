package com.rutong.platform.sys.entity;

import java.util.Set;

import javax.persistence.Entity;
import javax.persistence.ManyToMany;
import javax.persistence.Table;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rutong.platform.common.entity.BaseEntity;

@Entity
@Table(name = "sys_role")
public class SysRole extends BaseEntity {
	/** 角色名称 */
	private String rolename;

	/** 角色排序 */
	private Integer orderno;

	/** 数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限） */
	private String datascope;
	
	@JsonIgnore
    @ManyToMany(mappedBy = "roles")
    private Set<SysUser> users;

	public String getRolename() {
		return rolename;
	}

	public void setRolename(String rolename) {
		this.rolename = rolename;
	}

	public Integer getOrderno() {
		return orderno;
	}

	public void setOrderno(Integer orderno) {
		this.orderno = orderno;
	}

	public String getDatascope() {
		return datascope;
	}

	public void setDatascope(String datascope) {
		this.datascope = datascope;
	}

	public Set<SysUser> getUsers() {
		return users;
	}

	public void setUsers(Set<SysUser> users) {
		this.users = users;
	}
	
}
