package com.rutong.platform.wechat.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 洗涤项目DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.ALWAYS)  // 确保所有字段都被序列化，即使为null
public class WashingItemDTO {
    
    /**
     * 配置品类
     */
    @JsonProperty("configurationCategory")
    private String configurationCategory;
    
    /**
     * 产品款号
     */
    @JsonProperty("productModelNumber")
    private String productModelNumber;
    
    /**
     * 协议每周换洗日期
     */
    @JsonProperty("weeklyWashDate")
    private String weeklyWashDate;
    
    /**
     * 协议每周换洗次数
     */
    @JsonProperty("weeklyWashFrequency")
    private Integer weeklyWashFrequency;
    
    /**
     * 协议洗涤次数
     */
    @JsonProperty("totalWashCount")
    private Integer totalWashCount;
    
    /**
     * 已洗次数
     */
    @JsonProperty("washedCount")
    private Integer washedCount;

    /**
     * 产品序列号
     */
    @JsonProperty("productSerialNumber")
    private String productSerialNumber;
}