package com.rutong.platform.client.excel;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.rutong.platform.client.service.EmployeeProductConfigurationService.createError;

public class EmployeeProductConfigurationListener implements ReadListener<EmployeeProductConfiguration> {
    private final List<EmployeeProductConfiguration> dataList = new ArrayList<>();
    private final List<String> errorList = new ArrayList<>();

    List<Map<String, String>> errors = new ArrayList<>();
    private final Map<Integer, String> columnIndexToName = new HashMap<>(); // 列索引 -> 列名
    // 1. 定义正确的模板列名列表（按顺序）
    private static final List<String> EXPECTED_COLUMNS = Arrays.asList(
            "序号", "员工姓名", "员工电话", "部门", "分部门", "配置品类", "产品款号", "产品颜色",
            "LOGO位置", "产品规格", "协议各品类配置数量", "员工企业工号", "工作服RFID标签编码", "RFID标签号",
            "产品序列号", "存衣柜协议配置型号", "协议存衣柜配置数量", "更衣柜序列号", "脏衣柜协议配置型号",
            "协议脏衣柜配置数量", "脏衣柜序列号", "协议配置品类使用月份", "协议每周作息时间", "协议每周换洗日期",
            "协议每周换洗次数", "配置产品洗涤信息", "协议单次清洗租赁费", "协议每周清洗租赁费", "协议服装结算价",
            "本月服装余值", "客方本月服装丢失次数", "客方本月服装丢失费", "月合计费用", "协议启用日期",
            "协议截止日期", "离职日期", "属性", "状态", "服务类型"
    );
    private boolean headerValidated = false; // 表头是否验证通过


    private String contractNumber;

    public Map<String, Object> backData;

    public EmployeeProductConfigurationListener(String contractNumber) {
        this.contractNumber = contractNumber;
    }
    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        // 2. 将实际列名按顺序存入列表
        List<String> actualColumns = headMap.values().stream()
                .map(ReadCellData::getStringValue)
                .collect(Collectors.toList());
        System.out.println(actualColumns.toString());
        // 3. 验证列数和列名
        if (actualColumns.size() != EXPECTED_COLUMNS.size()) {
            String errorMsg = String.format("模板列数不正确，应为%d列，实际%d列",
                    EXPECTED_COLUMNS.size(), actualColumns.size());
            errors.add(createError(0, "模板错误", errorMsg));
            headerValidated = false;
            return;
        }

        // 4. 逐个对比列名和顺序
        for (int i = 0; i < EXPECTED_COLUMNS.size(); i++) {
            String expected = EXPECTED_COLUMNS.get(i);
            String actual = actualColumns.get(i);
            if (!expected.equals(actual)) {
                String errorMsg = String.format("第%d列应为【%s】，实际为【%s】",
                        i + 1, expected, actual);
                errors.add(createError(0, "模板错误", errorMsg));
                headerValidated = false;
                return;
            }
        }

        // 5. 验证通过后缓存列名映射
        headMap.forEach((index, cellData) -> {
            String columnName = cellData.getStringValue();
            columnIndexToName.put(index, columnName);
        });
        headerValidated = true;
    }

    @Override
    public void invoke(EmployeeProductConfiguration data, AnalysisContext context) {
        // 6. 如果表头未通过验证，直接跳过数据处理
        if (!headerValidated) {
            return;
        }
        dataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 移除service调用，现在在controller中直接调用service
        // 避免NullPointerException
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) {
        if (exception instanceof ExcelDataConvertException) {
            ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
            // 获取异常的行号和列号
            Integer rowIndex = excelDataConvertException.getRowIndex();
            Integer columnIndex = excelDataConvertException.getColumnIndex();
            // 从缓存中获取列名，如果不存在则用列号代替
            String columnName = columnIndexToName.getOrDefault(
                    excelDataConvertException.getColumnIndex(),
                    "第" + columnIndex + "列"
            );
            errors.add(createError(rowIndex, "转换错误", String.format("【%s】数据格式错误: %s", columnName, excelDataConvertException.getMessage())));

            errorList.add(String.format("第%s行第%s列数据格式错误: %s",
                    rowIndex + 1, columnIndex + 1, excelDataConvertException.getMessage()));
        } else {
            errorList.add("导入异常: " + exception.getMessage());
        }
    }

    public List<EmployeeProductConfiguration> getDataList() {
        return dataList;
    }

    public  List<Map<String, String>> getErrorMessages() {
        return errors;
    }

    public Map<String, Object> getBackData() {
        return backData;
    }
}