package com.rutong.framework.security.auth;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;

import com.rutong.framework.core.exception.ServiceException;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.security.service.PermissionService;
import com.rutong.framework.utils.MessageUtils;
import com.rutong.framework.utils.StringUtils;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.sys.service.SysUserService;

public class SmsAuthenticationProvider implements AuthenticationProvider {
	private static final Logger log = LoggerFactory.getLogger(SmsAuthenticationProvider.class);
	@Autowired
	private SysUserService userService;
	@Autowired
	private PermissionService permissionService;

	@Override
	public Authentication authenticate(Authentication authentication) throws AuthenticationException {
		String phoneNumber = authentication.getPrincipal().toString();

		SysUser user = userService.selectUserByPhone(phoneNumber);
		if (StringUtils.isNull(user)) {
			log.info("登录用户：{} 不存在.", phoneNumber);
			throw new ServiceException("手机号不存在！");
		}
		// 根据手机号查找用户
		UserDetails userDetails = createLoginUser(user);
		return new SmsAuthenticationToken(userDetails, userDetails.getAuthorities());
	}

	@Override
	public boolean supports(Class<?> authentication) {
		return SmsAuthenticationToken.class.isAssignableFrom(authentication);
	}

	public UserDetails createLoginUser(SysUser user) {
		return new LoginUser(user.getId(), user.getDeptid(), user, permissionService.getMenuPermission(user));
	}
}
