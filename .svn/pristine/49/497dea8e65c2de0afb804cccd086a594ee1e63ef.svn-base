package com.rutong.framework.security.auth;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

public class SmsAuthenticationTokenWeChat extends AbstractAuthenticationToken {
	private static final long serialVersionUID = -2653037429032735782L;

	private final Object principal;

    // 未认证时的构造函数，保存手机号和短信验证码
    public SmsAuthenticationTokenWeChat(Object principal) {
        super(null);
        this.principal = principal;
        setAuthenticated(false);
    }

    // 认证成功后，保存用户信息及权限
    public SmsAuthenticationTokenWeChat(Object principal, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        super.setAuthenticated(true);  // 必须使用父类的方法，否则会抛出异常
    }

    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getPrincipal() {
        return this.principal;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        if (isAuthenticated) {
            throw new IllegalArgumentException("Cannot set this token to trusted - use constructor which takes a GrantedAuthority list instead");
        }
        super.setAuthenticated(false);
    }

    @Override
    public void eraseCredentials() {
        super.eraseCredentials();
    }
}
