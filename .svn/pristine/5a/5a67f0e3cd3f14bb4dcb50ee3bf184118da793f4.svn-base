package com.rutong.platform.client.controller;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.Log;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.framework.utils.ServerUtil;
import com.rutong.framework.utils.StringUtils;
import com.rutong.framework.utils.file.FileTypeUtils;
import com.rutong.framework.utils.file.MimeTypeUtils;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.entity.PositionGuide;
import com.rutong.platform.client.service.PositionGuideService;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysDept;
import com.rutong.platform.sys.entity.SysFile;
import com.rutong.platform.sys.service.SysDeptService;
import com.rutong.platform.sys.service.SysFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/client/positionGuide")
public class PositionGuideController extends CrudController<PositionGuide> {

    @Autowired
    private PositionGuideService positionGuideService;
    @Autowired
    private SysFileService sysFileService;
    @Autowired
    private SysDeptService sysDeptService;

    @Override
    public BaseService<PositionGuide> getService() {
        return positionGuideService;
    }

    @Override
    public ResultBean save(PositionGuide entity) {
        SysDept byId = sysDeptService.findById(entity.getDeptId());
        entity.setDeptName(byId.getDeptname());
        return super.save(entity);
    }


    @Override
    public ResultBean update(PositionGuide entity) {
        SysDept byId = sysDeptService.findById(entity.getDeptId());
        entity.setDeptName(byId.getDeptname());
        return super.update(entity);
    }


    @PostMapping(value = "/getPositionGuideByUser.do")
    public ResultBean getPositionGuideByUser() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        PositionGuide byDeptId = positionGuideService.findByDeptId(loginUser.getDeptId());
        return ResultBean.success(byDeptId);
    }


    /**
     * 头像上传
     */
    @Log(desc = "上传条款文件")
    @PostMapping("/profile/uploadFile.do")
    public ResultBean avatar(@RequestParam("file") MultipartFile file) {
        if (!file.isEmpty()) {
//            LoginUser loginUser = SecurityUtils.getLoginUser();
            String extension = FileTypeUtils.getExtension(file);
            if (!StringUtils.equalsAnyIgnoreCase(extension, MimeTypeUtils.IMAGE_EXTENSION)) {
                return ResultBean.error("文件格式不正确，请上传" + Arrays.toString(MimeTypeUtils.IMAGE_EXTENSION) + "格式", 500);
            }
            SysFile fileResult = sysFileService.uploadFile(file);
            if (StringUtils.isNull(fileResult)) {
                return ResultBean.error("文件服务异常，请联系管理员", 500);
            }
            String url = ServerUtil.getDomain() + fileResult.getUrl();

            return ResultBean.success(url);
        }
        return ResultBean.error("上传图片异常，请联系管理员", 500);
    }


}
