package com.rutong.platform.client.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

@Entity
@Table(name = "client_info")
@Data
public class ClientInfo extends BaseEntity {
    public static final String FIELD_COL_CONTRACT_NUMBER = "contractNumber";

    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 客户名称
     */
    private String enterpriseName;

    /**
     * 简称
     */
    private String abbreviation;
    /**
     * 主要联系人
     */
    private String primaryContactPerson;
    /**
     * 联系电话
     */
    private String phone;

    /**
     * 状态
     */
    private Long status;

    /**
     * 合作人数
     */
    private Long countData;

    /**
     * 状态
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 状态
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;


    @Transient
    private String clauseInfoJson;

    @Transient
    private String productDataJson;

}
