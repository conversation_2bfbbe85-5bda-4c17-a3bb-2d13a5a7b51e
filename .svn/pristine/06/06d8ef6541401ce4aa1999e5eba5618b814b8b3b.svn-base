package com.rutong.platform.inventory.entity;

import com.rutong.platform.client.dto.EmployeeProductConfigurationDto;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @ClassName DeliveryRecord * @Description TODO
 * <AUTHOR>
 * @Date 10:47 2025/4/1
 * @Version 1.0
 **/
@Data
@Entity
@Table(name = "inventory_delivery_record_item")
public class DeliveryRecordItem extends EmployeeProductConfigurationDto {

    private String parentId;
}
