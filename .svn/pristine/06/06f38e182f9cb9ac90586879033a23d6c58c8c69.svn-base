package com.rutong.platform.wechat.service;

import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.wechat.dto.ConfigInfoDTO;
import com.rutong.platform.wechat.dto.ConfigItemDTO;
import com.rutong.platform.wechat.dto.UserInfoDTO;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.wechat.dto.ChatInfoDTO;
import com.rutong.platform.wechat.dto.ChatItemDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 配置信息服务类
 */
@Service
public class ConfigInfoService {
    
    private static final Logger log = LoggerFactory.getLogger(ConfigInfoService.class);
    
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;

    @Autowired
    private UserInfoService userInfoService;
    
    @Autowired
    private ChatInfoService chatInfoService;
    
    /**
     * 获取当前登录用户的配置信息
     * 
     * @return 配置信息
     */
    public ConfigInfoDTO getConfigInfoByLoginUser() {
        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null || loginUser.getUser() == null) {
            log.warn("用户未登录或登录信息不完整");
            return null;
        }
        
        SysUser user = loginUser.getUser();
        String phone = user.getPhone();
        
        // 通过UserInfoService获取完整的用户信息
        UserInfoDTO userInfo = userInfoService.getUserInfoByPhone(phone);
        if (userInfo == null) {
            log.warn("未找到用户信息，phone: {}", phone);
            return null;
        }
        
        log.info("查询用户配置信息，用户ID: {}, 员工工号: {}", userInfo.getUserId(), userInfo.getEmployeeId());
        
        return getConfigInfoByEmployeeId(userInfo.getEmployeeId(), userInfo.getName());
    }
    
    /**
     * 根据员工工号获取配置信息
     *
     * @param employeeId 员工工号
     * @param userName 用户姓名
     * @return 配置信息
     */
    public ConfigInfoDTO getConfigInfoByEmployeeId(String employeeId, String userName) {
        if (StringUtils.isBlank(employeeId)) {
            log.warn("员工工号为空，无法查询配置信息");
            return null;
        }
        
        log.info("开始查询员工[{}]的配置信息", employeeId);
        
        ChatInfoDTO chatInfo = chatInfoService.getChatInfoByLoginUser();
        if (chatInfo == null || chatInfo.getChatItems() == null || chatInfo.getChatItems().isEmpty()) {
            log.warn("未找到当前用户的聊天信息，无法匹配服装信息");
            ConfigInfoDTO configInfoDTO = new ConfigInfoDTO();
            configInfoDTO.setEmployeeId(employeeId);
            configInfoDTO.setUserName(userName);
            configInfoDTO.setConfigItems(new ArrayList<>());
            return configInfoDTO;
        }
        
        List<FilterCondition> filters = new ArrayList<>();
        
        FilterCondition employeeFilter = new FilterCondition();
        employeeFilter.setProperty("employeeId");
        employeeFilter.setOperator(FilterCondition.EQ);
        employeeFilter.setValue(employeeId);
        filters.add(employeeFilter);
        
        FilterCondition statusFilter = new FilterCondition();
        statusFilter.setProperty("status");
        statusFilter.setOperator(FilterCondition.EQ);
        statusFilter.setValue("合作中");
        filters.add(statusFilter);
        
        List<EmployeeProductConfiguration> allConfigs = employeeProductConfigurationService.findAll(filters, null);
        if (allConfigs == null || allConfigs.isEmpty()) {
            log.info("未找到员工[{}]的配置信息", employeeId);
            ConfigInfoDTO configInfoDTO = new ConfigInfoDTO();
            configInfoDTO.setEmployeeId(employeeId);
            configInfoDTO.setUserName(userName);
            configInfoDTO.setConfigItems(new ArrayList<>());
            return configInfoDTO;
        }
        
        Map<String, List<EmployeeProductConfiguration>> configMap = new HashMap<>();
        for (EmployeeProductConfiguration config : allConfigs) {
            String key = config.getConfigurationCategory() + "_" + config.getProductModelNumber();
            configMap.computeIfAbsent(key, k -> new ArrayList<>()).add(config);
        }
        
        List<ConfigItemDTO> configItems = new ArrayList<>();
        
        for (ChatItemDTO chatItem : chatInfo.getChatItems()) {
            String configKey = chatItem.getConfigurationCategory() + "_" + chatItem.getProductModelNumber();
            List<EmployeeProductConfiguration> matchingConfigs = configMap.get(configKey);
            
            if (matchingConfigs != null && !matchingConfigs.isEmpty()) {
                EmployeeProductConfiguration config = matchingConfigs.get(0);
                
                ConfigItemDTO configItem = new ConfigItemDTO();
                configItem.setConfigurationCategory(config.getConfigurationCategory());
                configItem.setProductModelNumber(config.getProductModelNumber());
                configItem.setProductSpecification(config.getProductSpecification());
                configItem.setProductSerialNumber(config.getProductSerialNumber());
                configItem.setProtocolStartDate(config.getProtocolStartDate());
                configItem.setProtocolEndDate(config.getProtocolEndDate());
                
                configItems.add(configItem);
            }
        }
        
        log.info("成功构建配置项列表，共{}项，与聊天信息匹配", configItems.size());
        
        ConfigInfoDTO configInfoDTO = new ConfigInfoDTO();
        configInfoDTO.setEmployeeId(employeeId);
        configInfoDTO.setUserName(userName);
        configInfoDTO.setConfigItems(configItems);
        return configInfoDTO;
    }
} 