package com.rutong.platform.app.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.ResultBean;
import com.rutong.platform.app.utils.AppConstan;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.device.entity.DevInCabinet;
import com.rutong.platform.device.entity.DevSorting;
import com.rutong.platform.device.service.InCabinetService;
import com.rutong.platform.device.service.SortingService;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;

@RestController
@RequestMapping(value = "/unauth")
public class UnauthController {
    @Autowired
    private InCabinetService inCabinetService;
    @Autowired
    private SortingService devSortingService;
    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;

    @GetMapping("/getShelfByHostSid.do")
    public ResultBean getShelfByHostSid(String host, Integer sid) {
        DevInCabinet cabinet = inCabinetService.findByHostAndSid(host, sid);
        if (cabinet == null) {
            return ResultBean.error();
        }
        if (cabinet.getType().equals(AppConstan.DEVICE_TYPE_NO)) {
            // 按列号查询
            List<DevInCabinet> caList = inCabinetService.findByCol(cabinet.getCol());
            return ResultBean.success(caList);
        }
        return ResultBean.success();
    }

    /**
     * 产线分拣格口
     */
    @GetMapping("/productionLineSorting.do")
    public ResultBean productionLineSorting(String ip, String rfid) {
        List<DevSorting> devSorting = devSortingService.findByIpInfo(ip);
        EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfid(rfid);
        String lockerSerialNumber = employeeProductConfiguration.getLockerSerialNumber();// 更衣柜序列号
        DevInCabinet byCabNo = inCabinetService.findByCabNo(lockerSerialNumber);
        if (StringUtils.isEmpty(byCabNo)) {
            return ResultBean.error("没有查询到格口" + lockerSerialNumber, -1);
        }
        for (DevSorting sorting : devSorting) {
            if (sorting.getDevno().equals(byCabNo.getSortingno())) {
                return ResultBean.success(byCabNo);
            }
        }
        return ResultBean.error("不是当前口分拣", -1);
    }

    @PostMapping("/changeLightRfid.do")
    public ResultBean changeLightRifd(String ip, Integer sid, String rfid) {
        DevInCabinet cabinet = inCabinetService.findByHostAndSid(ip, sid);
        if (cabinet == null) {
            return ResultBean.error();
        }
        cabinet.setRfid(rfid);
        cabinet.setStatus("占用");
        inCabinetService.updateInfo(cabinet);

        return ResultBean.success();
    }

    @PostMapping("/changeLight.do")
    public ResultBean changeLight(String ip, Integer sid) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        Map<String, Object> map = new HashMap<>();
        DevInCabinet cabinet = inCabinetService.findByHostAndSid(ip, sid);
        if (cabinet == null) {
            return ResultBean.error();
        }
        if ("可分配".equals(cabinet.getType())) {
            cabinet.setStatus("占用");
        } else {
            List<DevInCabinet> listOff = new ArrayList<>();
            List<DevInCabinet> list = inCabinetService.findBySortingnoAndCol(cabinet.getSortingno(), cabinet.getCol());
            for (DevInCabinet devInCabinet : list) {
                if ("占用".equals(devInCabinet.getStatus()) || "不可分配".equals(devInCabinet.getType())) {
                    listOff.add(devInCabinet);
                }
                List<WmsDeliveryDetail> byRfid = wmsDeliveryDetailService.findByRfid(devInCabinet.getRfid(),
                        simpleDateFormat.format(new Date()));
                for (WmsDeliveryDetail wmsDeliveryDetail : byRfid) {
                    wmsDeliveryDetail.setStatus(1L);
                    wmsDeliveryDetailService.updateInfo(wmsDeliveryDetail);
                }
                devInCabinet.setStatus("空闲");
                devInCabinet.setRfid(null);
                inCabinetService.updateInfo(devInCabinet);
            }
            map.put("type", "off");
            map.put("data", listOff);
            return ResultBean.success(map);
        }
        try {
            EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService
                    .byRfid(cabinet.getRfid());
            String lockerSerialNumber = employeeProductConfiguration.getLockerSerialNumber();
            String[] sss = lockerSerialNumber.split("-");
            String client = sss[0];
            String col = sss[1];
            List<String> list = new ArrayList<>();
            List<DevInCabinet> bySortingnoAndCol = inCabinetService.findBySortingnoAndCol(client,
                    Integer.parseInt(col));
            for (DevInCabinet devInCabinet : bySortingnoAndCol) {
                list.add(devInCabinet.getCabNo());
            }
            List<String> listOK = new ArrayList<>();

            List<WmsDeliveryDetail> wmsDeliveryDetails = wmsDeliveryDetailService.findByContractNumber(
                    employeeProductConfiguration.getContractNumber(), "(客户端)收取", simpleDateFormat.format(new Date()));
            for (WmsDeliveryDetail wmsDeliveryDetail : wmsDeliveryDetails) {
                String lockerSerialNumber1 = wmsDeliveryDetail.getLockerSerialNumber();
                if (list.contains(lockerSerialNumber1)) {
                    listOK.add(lockerSerialNumber1);
                }
            }
            boolean status = true;
            for (String s : listOK) {
                DevInCabinet byCabNo = inCabinetService.findByCabNo(s);
                if (byCabNo.getStatus().equals("空闲")) {
                    status = false;
                }
            }
            if (status) {
                List<DevInCabinet> list1 = inCabinetService.findByHostAndColAndType(ip, col, "不可分配");
                map.put("type", "on");
                map.put("data", list1);
                return ResultBean.success(map);
            }
        } catch (NullPointerException e) {
            return ResultBean.error();
        }
        return ResultBean.error();
    }


}
