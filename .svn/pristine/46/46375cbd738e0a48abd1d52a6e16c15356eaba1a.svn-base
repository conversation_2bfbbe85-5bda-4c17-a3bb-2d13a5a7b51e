package com.rutong.platform.common.controller;

import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.Log;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.security.utils.DataAuthUtil;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.common.entity.TreeEntity;
import com.rutong.platform.common.service.BaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class CrudController<T extends BaseEntity> extends BaseController {

	public abstract BaseService<T> getService();

	@PostMapping(value = "/fetchdata.do")
	public PageInfo<T> findAllByPage(GridParams gridParams,
			@RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
			@RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
		if (sorts.size() == 0) {
			SortCondition sortCondition = new SortCondition();
			sortCondition.setDirection(SortCondition.SORT_DESC);
			sortCondition.setProperty(BaseEntity.FIELD_CREATE_TIME);
			sorts.add(sortCondition);
		}
		//增加数据权限
		DataAuthUtil.setDataAuth(filters);
		return getService().findAllByPage(gridParams, filters, sorts);
	}

	@PostMapping(value = "/fetchtree.do")
	public PageInfo<T> findAllByTree(GridParams gridParams,
			@RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
			@RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
		if (filters.size() == 0) {
			FilterCondition filterCondition = new FilterCondition();
			filterCondition.setProperty(TreeEntity.PARENTID);
			filterCondition.setOperator(FilterCondition.EQ);
			filterCondition.setValue("00");
			filters.add(filterCondition);
		}
		if (sorts.size() == 0) {
			SortCondition sortCondition = new SortCondition();
			sortCondition.setDirection(SortCondition.SORT_DESC);
			sortCondition.setProperty(BaseEntity.FIELD_CREATE_TIME);
			sorts.add(sortCondition);
		}
		DataAuthUtil.setDataAuth(filters);
		return getService().findAllByTree(gridParams, filters, sorts);
	}

	@Log(title = "基础操作", desc = "新增数据")
	@PostMapping(value = "/save.do")
	public ResultBean save(T entity) {
		getService().save(entity);
		return ResultBean.success(entity);
	}

	@Log(title = "基础操作", desc = "修改数据")
	@PostMapping(value = "/update.do")
	public ResultBean update(T entity) {
		getService().update(entity);
		return ResultBean.success(entity);
	}

	@Log(title = "基础操作", desc = "删除数据")
	@PostMapping(value = "/delete.do")
	public ResultBean delete(@RequestBody T entity) {
		getService().delete(entity);
		return ResultBean.success();
	}

	@Log(title = "基础操作", desc = "批量删除数据")
	@PostMapping(value = "/deleteBatch.do")
	public ResultBean deleteBatch(@RequestBody List<T> entitys) {
		for (T t : entitys) {
			getService().delete(t);
		}
		return ResultBean.success();
	}
}
