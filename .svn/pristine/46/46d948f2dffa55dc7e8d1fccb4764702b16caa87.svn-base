package com.rutong.platform.client.controller;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.Global;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.*;
import com.rutong.platform.client.excel.EmployeeProductConfigurationExcel;
import com.rutong.platform.client.excel.EmployeeProductConfigurationListener;
import com.rutong.platform.client.excel.ExpenseSettlementItemExcel;
import com.rutong.platform.client.service.ClientInfoService;
import com.rutong.platform.client.service.EmployeeProductConfigurationHistoryService;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.client.util.ExportUtil;
import com.rutong.platform.client.util.GenerateXML;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/client/employeeProductConfiguration")
public class EmployeeProductConfigurationController extends CrudController<EmployeeProductConfiguration> {

    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;


    @Autowired
    private EmployeeProductConfigurationHistoryService employeeProductConfigurationHistoryService;

    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;


    @Autowired
    private ClientInfoService clientInfoService;


    @Override
    public BaseService<EmployeeProductConfiguration> getService() {
        return employeeProductConfigurationService;
    }

    @Override
    public ResultBean save(EmployeeProductConfiguration entity) {
        ClientInfo byContractNumber = clientInfoService.findByContractNumber(entity.getContractNumber());
        EmployeeProductConfiguration allByObject = employeeProductConfigurationService.byRfid(entity.getRfidTagNumber());
        if (allByObject != null) {
            throw new ExcelAnalysisException("RFID标签信息：" + entity.getRfidTagNumber() + "，已存在无法重复添加");
        }
        EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byProductSerialNumber(entity.getProductSerialNumber());
        if (employeeProductConfiguration != null) {
            throw new ExcelAnalysisException("产品序列号：" + entity.getProductSerialNumber() + "，已存在无法重复添加");
        }

        entity.setEnterpriseName(byContractNumber.getEnterpriseName());
//        employeeProductConfiguration.setContractNumber(contractNumber);
        employeeProductConfigurationService.save(entity);
        return ResultBean.success(entity);
    }

    @PostMapping(value = "/fetchdataGroup.do")
    public PageInfo<EmployeeProductConfiguration> fetchdataGroup(GridParams gridParams,
                                                                 @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
                                                                 @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        if (sorts.size() == 0) {
            SortCondition sortCondition = new SortCondition();
            sortCondition.setDirection(SortCondition.SORT_DESC);
            sortCondition.setProperty(BaseEntity.FIELD_CREATE_TIME);
            sorts.add(sortCondition);
        }
        return employeeProductConfigurationService.fetchdataGroup(gridParams, filters, sorts);
    }

    @Override
    public ResultBean update(EmployeeProductConfiguration entity) {
        EmployeeProductConfiguration byId = employeeProductConfigurationService.findById(entity.getId());
        EmployeeProductConfiguration allByObject = employeeProductConfigurationService.byRfid(entity.getRfidTagNumber());
        
        // 调试日志
        System.out.println("update方法被调用，参数entity: " + (entity != null ? entity.toString() : "null"));
        System.out.println("当前实体byId: " + (byId != null ? byId.toString() : "null"));
        if (entity != null && byId != null) {
            System.out.println("新RFID标签号: " + entity.getRfidTagNumber());
            System.out.println("旧RFID标签号: " + byId.getRfidTagNumber());
            System.out.println("RFID标签号是否变化: " + !entity.getRfidTagNumber().equals(byId.getRfidTagNumber()));
        }
        
        if (allByObject != null && !entity.getRfidTagNumber().equals(byId.getRfidTagNumber())) {
            throw new ExcelAnalysisException("RFID标签信息：" + entity.getRfidTagNumber() + "，已存在无法重复添加");
        }
        EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byProductSerialNumber(entity.getProductSerialNumber());
        if (employeeProductConfiguration != null && !entity.getProductSerialNumber().equals(byId.getProductSerialNumber())) {
            throw new ExcelAnalysisException("产品序列号：" + entity.getProductSerialNumber() + "，已存在无法重复添加");
        }

        // 只有当RFID标签号发生变化时才创建历史记录
        if (!entity.getRfidTagNumber().equals(byId.getRfidTagNumber())) {
            EmployeeProductConfigurationHistory employeeProductConfigurationHistory = new EmployeeProductConfigurationHistory();
            BeanUtils.copyProperties(byId, employeeProductConfigurationHistory);
            employeeProductConfigurationHistory.setParentId(byId.getId());
            employeeProductConfigurationHistory.setId(null);
            
            // 记录变更类型和新旧值
            employeeProductConfigurationHistory.setChangeType("RFID标签变更");
            employeeProductConfigurationHistory.setOldValue(byId.getRfidTagNumber());
            employeeProductConfigurationHistory.setNewValue(entity.getRfidTagNumber());
            
            // 设置操作人
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null) {
                employeeProductConfigurationHistory.setOperator(loginUser.getUsername());
            }
            
            employeeProductConfigurationHistoryService.save(employeeProductConfigurationHistory);
            System.out.println("已创建历史记录: " + employeeProductConfigurationHistory.toString());
        } else {
            System.out.println("RFID标签号未变化，不创建历史记录");
        }
        
        employeeProductConfigurationService.update(entity);
        return ResultBean.success(entity);
    }

    @PostMapping(value = "/getHistory.do")
    public ResultBean getHistory(EmployeeProductConfiguration entity) {
        List<SortCondition> sorts = new ArrayList<>();
        SortCondition sortCondition = new SortCondition();
        sortCondition.setDirection(SortCondition.SORT_DESC);
        sortCondition.setProperty(BaseEntity.FIELD_CREATE_TIME);
        sorts.add(sortCondition);
        
        // 调试日志
        System.out.println("getHistory方法被调用，参数entity: " + (entity != null ? entity.toString() : "null"));
        if (entity != null) {
            System.out.println("RFID标签号: " + entity.getRfidTagNumber());
            System.out.println("ID: " + entity.getId());
        }
        
        // 查询与当前RFID标签号相关的历史记录
        List<EmployeeProductConfigurationHistory> historyRecords = new ArrayList<>();
        
        if (entity != null && entity.getRfidTagNumber() != null) {
            // 使用服务方法查询历史记录
            List<Map<String, Object>> results = employeeProductConfigurationHistoryService.findAllRelatedHistoryByRfidTagNumber(entity.getRfidTagNumber());
            
            System.out.println("SQL查询结果数量: " + results.size());
            for (Map<String, Object> result : results) {
                String id = (String) result.get("id");
                EmployeeProductConfigurationHistory history = employeeProductConfigurationHistoryService.findById(id);
                if (history != null) {
                    historyRecords.add(history);
                }
            }
        } else if (entity != null && entity.getId() != null) {
            // 如果没有RFID标签号但有ID，则先查询当前实体
            EmployeeProductConfiguration currentEntity = employeeProductConfigurationService.findById(entity.getId());
            System.out.println("通过ID查询当前实体: " + (currentEntity != null ? currentEntity.toString() : "null"));
            if (currentEntity != null && currentEntity.getRfidTagNumber() != null) {
                // 使用服务方法查询历史记录
                List<Map<String, Object>> results = employeeProductConfigurationHistoryService.findAllRelatedHistoryByRfidTagNumber(currentEntity.getRfidTagNumber());
                
                System.out.println("SQL查询结果数量: " + results.size());
                for (Map<String, Object> result : results) {
                    String id = (String) result.get("id");
                    EmployeeProductConfigurationHistory history = employeeProductConfigurationHistoryService.findById(id);
                    if (history != null) {
                        historyRecords.add(history);
                    }
                }
            }
        }
        
        return ResultBean.success(historyRecords);
    }

    @PostMapping(value = "/getxdjl.do")
    public ResultBean getxdjl(EmployeeProductConfiguration entity) {
        List<WmsDeliveryDetail> byContractNumberAndProductSerialNumber = wmsDeliveryDetailService.findByContractNumberAndProductSerialNumber(entity.getContractNumber(), entity.getProductSerialNumber(), "(客户端)收取");
        return ResultBean.success(byContractNumberAndProductSerialNumber);
    }

    /**
     * 条款服装导入
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importExcel.do")
    public ResultBean importExcel(@RequestParam("file") MultipartFile file, String contractNumber) {
        try {
            // 创建自定义监听器
            EmployeeProductConfigurationListener listener = new EmployeeProductConfigurationListener(contractNumber);
            Map<String, Object> result = new HashMap<>();
            // 使用自定义监听器读取Excel
            EasyExcel.read(file.getInputStream(), EmployeeProductConfiguration.class, listener)
                    .sheet()
                    .doRead();

            // 检查是否有转换异常
            if (!listener.getErrorMessages().isEmpty()) {
                // 如果有数据转换异常，直接返回错误信息
                result.put("errorList", listener.getErrorMessages());
                result.put("successCount", 0);

                return ResultBean.success(result);
            }

            // 没有转换异常，则处理导入数据
            result = employeeProductConfigurationService.importData(listener.getDataList(), contractNumber);

            return ResultBean.success(result);
        } catch (IOException e) {
            e.printStackTrace();
            return ResultBean.error(e.getMessage(), 500);
        }
    }

    @PostMapping("/export.do")
    public ResultBean export(@RequestList(clazz = FilterCondition.class) List<FilterCondition> filters) throws IOException {

        String fileName = "服务记录与查询";
        fileName = ExportUtil.encodingExcelFilename(fileName);
        String folder = Global.getTempPath() + File.separator + "pio" + File.separator;
        FileUtil.touch(folder + fileName);
        // 模拟数据，实际需要从数据库中获取
        List<EmployeeProductConfigurationExcel> list = new ArrayList<>();


        List<EmployeeProductConfiguration> allByObject = employeeProductConfigurationService.findAll(filters, null);

        for (EmployeeProductConfiguration employeeProductConfiguration : allByObject) {
            EmployeeProductConfigurationExcel employeeProductConfigurationExcel = new EmployeeProductConfigurationExcel();
            BeanUtils.copyProperties(employeeProductConfiguration, employeeProductConfigurationExcel);
            list.add(employeeProductConfigurationExcel);
        }
        EasyExcel.write(folder + fileName, EmployeeProductConfigurationExcel.class).sheet("服务记录与查询").doWrite(list);

        return ResultBean.success(fileName);
    }

    @RequestMapping(value = "/getPrintData", produces = {"application/xml;charset=utf-8"})
    @ResponseBody
    public String getData(String ids) {
// 将字符串 ids 转换为 List<Long>
        List<String> idList = Arrays.stream(ids.split(","))
                .map(String::valueOf)  // 将每个部分转换为 Long 类型
                .collect(Collectors.toList());
        List<EmployeeProductConfiguration> employeeProductConfigurationList = new ArrayList<>();
        for (String aLong : idList) {
            EmployeeProductConfiguration byId = employeeProductConfigurationService.findById(aLong);
            employeeProductConfigurationList.add(byId);
        }
        String string = GenerateXML.generateXMLString(employeeProductConfigurationList);
        return string;
    }

    @PostMapping(value = "/checkHistoryTable.do")
    public ResultBean checkHistoryTable(String rfidTagNumber) {
        System.out.println("检查历史记录表，RFID标签号: " + rfidTagNumber);
        
        // 通过服务方法查询历史记录
        List<Map<String, Object>> results = employeeProductConfigurationHistoryService.findHistoryByRfidTagNumberSQL(rfidTagNumber);
        
        System.out.println("SQL查询结果数量: " + results.size());
        for (Map<String, Object> result : results) {
            System.out.println("历史记录: " + result);
        }
        
        // 检查表结构
        List<Map<String, Object>> tableStructure = employeeProductConfigurationHistoryService.getTableStructure();
        
        System.out.println("表结构字段数量: " + tableStructure.size());
        for (Map<String, Object> column : tableStructure) {
            System.out.println("字段: " + column);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", results);
        result.put("tableStructure", tableStructure);
        
        return ResultBean.success(result);
    }

}
