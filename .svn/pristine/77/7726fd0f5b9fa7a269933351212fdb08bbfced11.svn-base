package com.rutong.platform.wms.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.rutong.platform.common.entity.BaseEntity;

/**
 * 服装出库记录
 */
@Entity
@Table(name = "wms_clothing_outbound")
public class WmsClothingOutbound extends BaseEntity{

	private String cloType;  //品类
	private String cloModel;  //款号
	private String cloColor;  //颜色
	private String cloLogo;  //logo位置
	private String cloSpec;  //规格
	private String cloRfid;  //rfid号
	@Column(nullable = false,unique = true,length = 96)
	private String cloNumber;  //序列号
	private Date leaseTime;  //最后租赁时间
	private Date outboundTime; //操作时间
	private String outboundUser;//操作人

	private String outboundResult;  //出库原因
	private String operationType;  //操作类型
    private Long cloCount; // 数量
	private String enterpriseName; //客户名称

	public String getEnterpriseName() {
		return enterpriseName;
	}

	public void setEnterpriseName(String enterpriseName) {
		this.enterpriseName = enterpriseName;
	}
    public Long getCloCount() {
        return cloCount;
    }

    public void setCloCount(Long cloCount) {
        this.cloCount = cloCount;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getCloType() {
		return cloType;
	}
	public void setCloType(String cloType) {
		this.cloType = cloType;
	}
	public String getCloModel() {
		return cloModel;
	}
	public void setCloModel(String cloModel) {
		this.cloModel = cloModel;
	}
	public String getCloColor() {
		return cloColor;
	}
	public void setCloColor(String cloColor) {
		this.cloColor = cloColor;
	}
	public String getCloLogo() {
		return cloLogo;
	}
	public void setCloLogo(String cloLogo) {
		this.cloLogo = cloLogo;
	}
	public String getCloSpec() {
		return cloSpec;
	}
	public void setCloSpec(String cloSpec) {
		this.cloSpec = cloSpec;
	}
	public String getCloRfid() {
		return cloRfid;
	}
	public void setCloRfid(String cloRfid) {
		this.cloRfid = cloRfid;
	}
	public String getCloNumber() {
		return cloNumber;
	}
	public void setCloNumber(String cloNumber) {
		this.cloNumber = cloNumber;
	}
	public Date getLeaseTime() {
		return leaseTime;
	}
	public void setLeaseTime(Date leaseTime) {
		this.leaseTime = leaseTime;
	}
	public Date getOutboundTime() {
		return outboundTime;
	}
	public void setOutboundTime(Date outboundTime) {
		this.outboundTime = outboundTime;
	}
	public String getOutboundResult() {
		return outboundResult;
	}
	public void setOutboundResult(String outboundResult) {
		this.outboundResult = outboundResult;
	}
	public String getOutboundUser() {
		return outboundUser;
	}

	public void setOutboundUser(String outboundUser) {
		this.outboundUser = outboundUser;
	}
}
