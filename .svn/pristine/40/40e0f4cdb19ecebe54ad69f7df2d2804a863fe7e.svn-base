package com.rutong.platform.wechat.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import java.util.List;
import java.util.Date;

@Data
@JsonInclude(JsonInclude.Include.ALWAYS)
public class WorkClothesProfileDTO {
    // 基本信息
    private String type; // 类型
    private String clothesCode; // 工装编码
    private String enterpriseName; // 所属客户
    private String productSize; // 产品款号
    private String productSerialNumber; // 产品编号

    // 所属员工
    private String employeeName;// 员工姓名
    private String employeeId;//员工编码
    private String lockerSerialNumber;//编号

    // 其他信息
    private String status; // 当前状态
    private Integer washCount; // 洗涤次数
    private Date protocolStartDate; // 启用日期
    private Date protocolEndDate; // 截止日期

    // 当前员工所有服装下拉列表
    private List<ClothesSimpleInfo> allClothes;

    @Data
    @JsonInclude(JsonInclude.Include.ALWAYS)
    public static class ClothesSimpleInfo {
        private String clothesCode;// 工装编码
        private String productSize;// 产品款号
        private String productSerialNumber;// 产品编号
        private String enterpriseName; //  所属客户
        private String type; // 类型
        private String status; // 当前状态
        private Integer washCount; // 洗涤次数
    }
} 