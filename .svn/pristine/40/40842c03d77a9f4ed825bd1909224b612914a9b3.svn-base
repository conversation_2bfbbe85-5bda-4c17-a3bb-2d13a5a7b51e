package com.rutong.platform.client.service;

import com.rutong.platform.client.entity.ClauseInfo;
import com.rutong.platform.client.entity.ClauseProductConfig;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsClothing;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ClauseInfoService extends BaseService<ClauseInfo> {

    @Autowired
    private ClauseProductConfigService clauseProductConfigService;


    public ClauseInfo findByContractNumber(String contractNumber) {
        return dao.findByPropertyFirst(ClauseInfo.class, ClauseInfo.FIELD_COL_CONTRACT_NUMBER, contractNumber);
    }


    public ClauseInfo getClauseInfoListByContractNumber(String contractNumber) {
        List<ClauseProductConfig> byProperty = dao.findByProperty(ClauseProductConfig.class, ClauseProductConfig.FIELD_COL_CONTRACT_NUMBER, contractNumber);
        ClauseInfo byContractNumber = findByContractNumber(contractNumber);
        byContractNumber.setClauseProductConfigList(byProperty);
        return byContractNumber;
    }
}
