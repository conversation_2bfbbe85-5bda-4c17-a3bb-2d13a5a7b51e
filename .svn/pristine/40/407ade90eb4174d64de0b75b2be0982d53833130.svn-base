package com.rutong.framework.core.dao;

import java.io.Serializable;
import java.math.BigInteger;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaDelete;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.CriteriaUpdate;
import javax.persistence.criteria.Order;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.poi.ss.formula.functions.T;
import org.hibernate.Session;
import org.hibernate.internal.SessionImpl;
import org.hibernate.query.criteria.internal.OrderImpl;
import org.hibernate.query.internal.NativeQueryImpl;
import org.hibernate.transform.Transformers;
import org.springframework.stereotype.Repository;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.core.exception.DaoException;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.QueryHelp;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.utils.StringUtils;

@Repository
public class DaoImpl implements Dao {

	private Session session;

	@PersistenceContext
	private EntityManager em;

	public DaoImpl() {
	}


	@Override
	public Session getCurrentSession() {
		return session == null ? em.unwrap(SessionImpl.class) : session;
	}

	@Override
	public Connection getConnection() {
		SessionImpl session = (SessionImpl) getCurrentSession();
		return session.getJdbcCoordinator().getLogicalConnection().getPhysicalConnection();
	}

	@Override
	public <T> void persist(T t) {
		em.persist(t);
	}

	@Override
	public <T> T merge(T t) {
		return em.merge(t);
	}

	@Override
	public void flush() {
		em.flush();
	}

	@Override
	public void clear() {
		em.clear();
	}


	@Override
	public <T, ID extends Serializable> T findById(Class<T> clazz, ID id) {
		return em.find(clazz, id);
	}

	@Override
	public <T> List<T> findAll(Class<T> clazz) {
		CriteriaBuilder criteriaBuilder = em.getCriteriaBuilder();
		CriteriaQuery<T> query = criteriaBuilder.createQuery(clazz);
		query.from(clazz);
		return em.createQuery(query).getResultList();
	}

	@Override
	public <T> T findByPropertyFirst(Class<T> clazz, Object... obj) {
		List<T> list = findByProperty(clazz, obj);
		return list.size() == 0 ? null : list.get(0);
	}

	@Override
	public <T> List<T> findByProperty(Class<T> clazz, Object... obj) {
		if (obj != null && obj.length % 2 != 0) {
			throw new DaoException("findByPropertyFirst方法中参数错误，obj必须是键值对存在，所以参数为双数。");
		}
		CriteriaBuilder criteriaBuilder = em.getCriteriaBuilder();
		CriteriaQuery<T> query = criteriaBuilder.createQuery(clazz);
		Root<T> root = query.from(clazz);
		if (StringUtils.isNotEmpty(obj)) {
			List<Predicate> pList = new ArrayList<Predicate>();
			for (int i = 0; i < obj.length; i += 2) {
				String key = String.valueOf(obj[i]);
				Object value = obj[i + 1];
				if (value == null) {
					Predicate predicate = criteriaBuilder.isNull(root.get(key));
					pList.add(predicate);
				} else {
					Predicate predicate = criteriaBuilder.equal(root.get(key), value);
					pList.add(predicate);
				}
			}
			query.where(pList.toArray(new Predicate[pList.size()]));
		}
		return em.createQuery(query).getResultList();
	}

	/**
	 * 基础查询
	 * 
	 * @param sql
	 * @param params 数组参数 例如： user.name=?
	 * @return List<Map>: 每一元素Map中应一行, Map中的键对应数据库中的字段名
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<Map<String, Object>> executeSQLQuery(String sql, Object... params) {
		Query query = em.createNativeQuery(sql);
		NativeQueryImpl<Map<String, Object>> nativeQuery = query.unwrap(NativeQueryImpl.class);
		if (params != null) {
			for (int i = 0; i < params.length; i++) {
				nativeQuery.setParameter(i, params[i]);
			}
		}
		nativeQuery.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
		List<Map<String, Object>> results = nativeQuery.getResultList();
		return results;
	}

	@SuppressWarnings("unchecked")
	@Override
	public <T> List<T> executeSQLQuery(String sql, Class<T> clazz, Object... params) {
		Query query = em.createNativeQuery(sql);
		NativeQueryImpl<T> nativeQuery = query.unwrap(NativeQueryImpl.class);
		if (params != null) {
			for (int i = 0; i < params.length; i++) {
				nativeQuery.setParameter(i, params[i]);
			}
		}
		nativeQuery.setResultTransformer(Transformers.aliasToBean(clazz));
		List<T> results = nativeQuery.getResultList();
		return results;
	}
	
	/**
	 * FROM ENTITY e WHERE e.field1 = ?0 AND E.field2 = ?1
	 */
	@Override
	public <T> List<T> executeQuery(String hql, Class<T> clazz, Object... params) {
		TypedQuery<T> query = em.createQuery(hql,clazz);
		if (params != null) {
			for (int i = 0; i < params.length; i++) {
				query.setParameter(i, params[i]);
			}
		}
		return query.getResultList();
	}
	
	@Override
	public <T> T executeQueryFirst(String hql, Class<T> clazz, Object... params) {
		TypedQuery<T> query = em.createQuery(hql,clazz);
		if (params != null) {
			for (int i = 0; i < params.length; i++) {
				query.setParameter(i, params[i]);
			}
		}
		query.setFirstResult(0);
		query.setMaxResults(1);
		List<T> resultList = query.getResultList();
		if(resultList.size()>0) {
			return resultList.get(0);
		}
		return null;
	}

	@Override
	public <T> Long count(Class<T> clazz, List<FilterCondition> filters) {
		CriteriaBuilder criteriaBuilder = em.getCriteriaBuilder();
		CriteriaQuery<Long> query = criteriaBuilder.createQuery(Long.class);
		Root<T> root = query.from(clazz);
		query.select(criteriaBuilder.count(root));
		if (StringUtils.isNotEmpty(filters)) {
			query.where(QueryHelp.getPredicate(root, filters, criteriaBuilder));
		}
		return em.createQuery(query).getSingleResult();
	}

	@Override
	public <T> List<T> findAll(Class<T> clazz, GridParams gridParams, List<FilterCondition> filters,
			List<SortCondition> sorts) {
		CriteriaBuilder criteriaBuilder = em.getCriteriaBuilder();
		CriteriaQuery<T> query = criteriaBuilder.createQuery(clazz);
		Root<T> root = query.from(clazz);
		if (StringUtils.isNotEmpty(filters)) {
			query.where(QueryHelp.getPredicate(root, filters, criteriaBuilder));
		}
		if (StringUtils.isNotEmpty(sorts)) {
			List<Order> orderList = new ArrayList<Order>();
			for (SortCondition sortCondition : sorts) {
				if (sortCondition.getDirection().equals(SortCondition.SORT_ASC)) {
					orderList.add(criteriaBuilder.asc(root.get(sortCondition.getProperty())));
				} else {
					orderList.add(criteriaBuilder.desc(root.get(sortCondition.getProperty())));
				}
			}
			if (orderList.size() > 0) {
				query.orderBy(orderList);
			}
		}
		TypedQuery<T> typedQuery = em.createQuery(query);
		if (StringUtils.isNotNull(gridParams)) {
			typedQuery.setFirstResult(gridParams.getStart());
			typedQuery.setMaxResults(gridParams.getPageSize());
		}
		return typedQuery.getResultList();
	}

	@Override
	public <T> void delete(T t) {
		em.remove(t);
	}

	@Override
	public <T> List<T> findAll(Class<T> clazz, List<FilterCondition> filters) {
		CriteriaBuilder criteriaBuilder = em.getCriteriaBuilder();
		CriteriaQuery<T> query = criteriaBuilder.createQuery(clazz);
		Root<T> root = query.from(clazz);
		if (StringUtils.isNotEmpty(filters)) {
			query.where(QueryHelp.getPredicate(root, filters, criteriaBuilder));
		}
		TypedQuery<T> typedQuery = em.createQuery(query);
		return typedQuery.getResultList();
	}

	@Override
	public <T> Long countByProperty(Class<T> clazz, Object... obj) {
		if (obj != null && obj.length % 2 != 0) {
			throw new DaoException("countByProperty方法中参数错误，obj必须是键值对存在，所以参数为双数。");
		}
		CriteriaBuilder criteriaBuilder = em.getCriteriaBuilder();
		CriteriaQuery<Long> query = criteriaBuilder.createQuery(Long.class);
		Root<T> root = query.from(clazz);
		query.select(criteriaBuilder.count(root));

		if (StringUtils.isNotEmpty(obj)) {
			List<Predicate> pList = new ArrayList<Predicate>();
			for (int i = 0; i < obj.length; i += 2) {
				String key = String.valueOf(obj[i]);
				Object value = obj[i + 1];
				if (value == null) {
					Predicate predicate = criteriaBuilder.isNull(root.get(key));
					pList.add(predicate);
				} else {
					Predicate predicate = criteriaBuilder.equal(root.get(key), value);
					pList.add(predicate);
				}
			}
			query.where(pList.toArray(new Predicate[pList.size()]));
		}
		return em.createQuery(query).getSingleResult();
	}

	@SuppressWarnings("unchecked")
	@Override
	public <T> int executeSQLUpdate(String sql, Object... params) {
		Query query = em.createNativeQuery(sql);
		NativeQueryImpl<T> nativeQuery = query.unwrap(NativeQueryImpl.class);
		if (params != null) {
			for (int i = 0; i < params.length; i++) {
				nativeQuery.setParameter(i, params[i]);
			}
		}
		return nativeQuery.executeUpdate();
	}
	
	
	@Override
	public <T> int deleteByProperty(Class<T> t, Object... obj) {
		CriteriaBuilder criteriaBuilder = em.getCriteriaBuilder();
		CriteriaDelete<T> criteriaDelete = criteriaBuilder.createCriteriaDelete(t);
		Root<T> root = criteriaDelete.from(t);
		if (StringUtils.isNotEmpty(obj)) {
			List<Predicate> pList = new ArrayList<Predicate>();
			for (int i = 0; i < obj.length; i += 2) {
				String key = String.valueOf(obj[i]);
				Object value = obj[i + 1];
				if (value == null) {
					Predicate predicate = criteriaBuilder.isNull(root.get(key));
					pList.add(predicate);
				} else {
					Predicate predicate = criteriaBuilder.equal(root.get(key), value);
					pList.add(predicate);
				}
			}
			criteriaDelete.where(pList.toArray(new Predicate[pList.size()]));
		}
		return em.createQuery(criteriaDelete).executeUpdate();
	}

	@SuppressWarnings("unchecked")
	@Override
	public BigInteger countSQLQuery(String sql, Object... params) {
		Query query = em.createNativeQuery(sql);
		NativeQueryImpl<BigInteger> nativeQuery = query.unwrap(NativeQueryImpl.class);
		if (params != null) {
			for (int i = 0; i < params.length; i++) {
				nativeQuery.setParameter(i, params[i]);
			}
		}
		return nativeQuery.getSingleResult();
	}

}
