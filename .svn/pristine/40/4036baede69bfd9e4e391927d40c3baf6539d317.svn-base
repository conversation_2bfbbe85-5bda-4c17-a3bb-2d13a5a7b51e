package com.rutong.platform.wms.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsCar;
import com.rutong.platform.wms.service.WmsCarService;

@RestController
@RequestMapping("/wms/car")
@LogDesc(title = "车辆管理")
public class WmsCarController extends CrudController<WmsCar>{
	
	@Autowired
	private WmsCarService carService;

	@Override
	public BaseService<WmsCar> getService() {
		return carService;
	}

	@Override
	public ResultBean save(WmsCar entity) {
		entity.setStatus("正常");
		return super.save(entity);
	}
}
