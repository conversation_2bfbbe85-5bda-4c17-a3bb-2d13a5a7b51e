package com.rutong.platform.wms.service;

import java.util.Date;

import org.springframework.stereotype.Service;

import com.rutong.framework.core.exception.ServiceException;
import com.rutong.framework.utils.BeanUtils;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsException;

@Service
public class WmsExceptionService extends BaseService<WmsException>{

	@Override
	public void save(WmsException data) {
		EmployeeProductConfiguration configuration = dao.findByPropertyFirst(EmployeeProductConfiguration.class, EmployeeProductConfiguration.FIELD_RFID_TAG_NUMBER,data.getCloRfid());
		if(configuration == null) {
			throw new ServiceException("无效标签号");
		}
		WmsException exception = new WmsException();
		exception.setCloColor(configuration.getProductColor());
		exception.setCloLogo(configuration.getLogoPosition());
		exception.setCloModel(configuration.getProductModelNumber());
		exception.setCloNumber(configuration.getProductSerialNumber());
		exception.setCloRfid(configuration.getRfidTagNumber());
		exception.setCloSpec(configuration.getProductSpecification());
		exception.setCloType(configuration.getConfigurationCategory());
		exception.setCreateBy("分拣工作台");
		exception.setId(null);
		exception.setEtype(data.getEtype());
		exception.setCreateTime(new Date());
		dao.persist(exception);
	}
}
