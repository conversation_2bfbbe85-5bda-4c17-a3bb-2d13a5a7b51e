package com.rutong.platform.client.util;

import com.alibaba.fastjson.JSONObject;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.StringWriter;
import java.util.List;

public class GenerateXML {

    public static void main(String[] args) {

        String s = "10只/999";
        if (s.indexOf("/") != -1) {
            System.out.println(s.substring(0, s.indexOf("/")));
        }
//        String xmlString = generateXMLString();
//        System.out.println(xmlString);
    }

    public static String generateXMLString(List<EmployeeProductConfiguration> falTagEntities) {
        try {
            // 创建DocumentBuilderFactory
            DocumentBuilderFactory documentFactory = DocumentBuilderFactory.newInstance();

            // 创建DocumentBuilder
            DocumentBuilder documentBuilder = documentFactory.newDocumentBuilder();

            // 创建Document
            Document document = documentBuilder.newDocument();

            // 创建根元素
            Element root = document.createElement("NewDataSet");
            document.appendChild(root);

            // 添加schema定义
            Element schema = document.createElement("xs:schema");
            schema.setAttribute("id", "NewDataSet");
            schema.setAttribute("xmlns:xs", "http://www.w3.org/2001/XMLSchema");
            schema.setAttribute("xmlns:msdata", "urn:schemas-microsoft-com:xml-msdata");
            root.appendChild(schema);

            Element element = document.createElement("xs:element");
            element.setAttribute("name", "NewDataSet");
            element.setAttribute("msdata:IsDataSet", "true");
            element.setAttribute("msdata:UseCurrentLocale", "true");
            schema.appendChild(element);

            Element complexType = document.createElement("xs:complexType");
            element.appendChild(complexType);

            Element choice = document.createElement("xs:choice");
            choice.setAttribute("minOccurs", "0");
            choice.setAttribute("maxOccurs", "unbounded");
            complexType.appendChild(choice);

            Element head = document.createElement("xs:element");
            head.setAttribute("name", "Head");
            Element headComplexType = document.createElement("xs:complexType");
            head.appendChild(headComplexType);
            choice.appendChild(head);

            Element detail = document.createElement("xs:element");
            detail.setAttribute("name", "Detail");
            Element detailComplexType = document.createElement("xs:complexType");
            detail.appendChild(detailComplexType);
            choice.appendChild(detail);

            Element sequence = document.createElement("xs:sequence");
            detailComplexType.appendChild(sequence);

            addElementWithAttributes(sequence, "BarCode", "xs:string");
            addElementWithAttributes(sequence, "SerialNumber", "xs:string");
            addElementWithAttributes(sequence, "Count", "xs:string");
            addElementWithAttributes(sequence, "Unit", "xs:string");
            addElementWithAttributes(sequence, "Specification", "xs:string");
            addElementWithAttributes(sequence, "Weight", "xs:string");
            addElementWithAttributes(sequence, "Name", "xs:string");
            for (EmployeeProductConfiguration falTagEntity : falTagEntities) {
                createDetail(document, root, falTagEntity);
            }
            // 创建Detail元素

            // 创建TransformerFactory
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");

            // 创建DOMSource
            DOMSource domSource = new DOMSource(document);

            // 创建StreamResult
            StringWriter writer = new StringWriter();
            StreamResult result = new StreamResult(writer);

            // 将DOMSource写入StreamResult
            transformer.transform(domSource, result);

            return writer.toString();

        } catch (ParserConfigurationException | TransformerException e) {
            e.printStackTrace();
            return null;
        }
    }

    private static void createChildElement(Document document, Element parent, String tagName, String textContent) {
        Element child = document.createElement(tagName);
        child.appendChild(document.createTextNode(textContent));
        parent.appendChild(child);
    }

    private static void createDetail(Document document, Element root, EmployeeProductConfiguration falTagEntity) {
        Element detail = document.createElement("Detail");

        String string = JSONObject.toJSONString(falTagEntity);
        createChildElement(document, detail, "BarCode", "falTagEntity.getTagcode()");
        createChildElement(document, detail, "employeeName", falTagEntity.getEmployeeName() + "");
        createChildElement(document, detail, "department", falTagEntity.getDepartment() + "");
        createChildElement(document, detail, "productSpecification", falTagEntity.getProductSpecification() + "");
        createChildElement(document, detail, "productSpecification", falTagEntity.getProductSpecification() + "");

        root.appendChild(detail);
    }

    private static void addElementWithAttributes(Element parent, String name, String type) {
        Document document = parent.getOwnerDocument();
        Element element = document.createElement("xs:element");
        element.setAttribute("name", name);
        element.setAttribute("type", type);
        element.setAttribute("minOccurs", "0");
        parent.appendChild(element);
    }
}
