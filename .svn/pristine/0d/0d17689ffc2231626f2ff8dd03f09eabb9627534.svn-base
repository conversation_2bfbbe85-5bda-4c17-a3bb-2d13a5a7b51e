package com.rutong.platform.device.controller;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.utils.DataAuthUtil;
import com.rutong.platform.client.entity.ClientInfo;
import com.rutong.platform.client.service.ClientInfoService;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.device.entity.DevRecordSorting;
import com.rutong.platform.device.entity.DevSorting;
import com.rutong.platform.device.service.DevRecordSortingService;
import com.rutong.platform.device.service.SortingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.alibaba.excel.EasyExcel;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import com.rutong.platform.device.excel.DevRecordSortingExcel;
import org.apache.commons.lang3.StringUtils;

@RestController
@RequestMapping("/device/recordSorting")
@LogDesc(title = "分拣记录")
public class DevRecordSortingController extends CrudController<DevRecordSorting> {

    @Autowired
    private DevRecordSortingService devRecordSortingService;
    
    @Autowired
    private ClientInfoService clientInfoService;

    @Override
    public BaseService<DevRecordSorting> getService() {
        return devRecordSortingService;
    }
    
    @Override
    public PageInfo<DevRecordSorting> findAllByPage(GridParams gridParams,
            @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
            @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        if (sorts.size() == 0) {
            SortCondition sortCondition = new SortCondition();
            sortCondition.setDirection(SortCondition.SORT_DESC);
            sortCondition.setProperty(BaseEntity.FIELD_CREATE_TIME);
            sorts.add(sortCondition);
        }
        
        // 处理时间范围过滤
        for (int i = 0; i < filters.size(); i++) {
            FilterCondition filter = filters.get(i);
            // 如果是日期字符串，需要转换为Date对象
            if ((filter.getProperty().equals("sortingTime") || filter.getProperty().equals("createTime")) 
                    && filter.getValue() instanceof String) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String dateStr = (String) filter.getValue();
                    Date date = sdf.parse(dateStr);
                    filter.setValue(date);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
            
            // 处理客户名称查询，将clientId映射到enterpriseName字段
            if (filter.getProperty().equals("clientId")) {
                filter.setProperty("enterpriseName");
                // 如果是UUID格式，需要通过clientId查询对应的客户名称
                if (filter.getValue() instanceof String) {
                    String clientId = (String) filter.getValue();
                    // 通过ID查询客户信息
                    ClientInfo clientInfo = clientInfoService.findById(clientId);
                    if (clientInfo != null && clientInfo.getEnterpriseName() != null) {
                        // 使用客户名称作为查询条件
                        filter.setValue(clientInfo.getEnterpriseName());
                    } else {
                        // 如果找不到客户信息，移除这个过滤条件，避免查询不到数据
                        filters.remove(i);
                        i--; // 因为移除了一个元素，索引需要减1
                    }
                }
            }
        }
        
        //增加数据权限
        DataAuthUtil.setDataAuth(filters);
        return getService().findAllByPage(gridParams, filters, sorts);
    }

    /**
     * 导出分拣记录为Excel
     * @param sortingMan 分拣人
     * @param cabNo 格口
     * @param sortingno 分拣编号
     * @param ids 勾选的记录ID列表，多个ID用逗号分隔
     */
    @GetMapping("/export.do")
    public void exportRecordSorting(HttpServletResponse response, 
            String sortingMan, 
            String cabNo, 
            String sortingno,
            String ids) throws Exception {
        
        List<DevRecordSorting> list;
        
        // 优先根据勾选的ID导出
        if (StringUtils.isNotBlank(ids)) {
            String[] idArray = ids.split(",");
            list = new java.util.ArrayList<>();
            for (String id : idArray) {
                DevRecordSorting record = devRecordSortingService.findById(id);
                if (record != null) {
                    list.add(record);
                }
            }
        } else {
            // 如果没有勾选，则根据搜索条件导出
            DevRecordSorting queryParams = new DevRecordSorting();
            if (StringUtils.isNotBlank(sortingMan)) {
                queryParams.setSortingMan(sortingMan);
            }
            if (StringUtils.isNotBlank(cabNo)) {
                queryParams.setCabNo(cabNo);
            }
            if (StringUtils.isNotBlank(sortingno)) {
                queryParams.setSortingno(sortingno);
            }

            // 根据条件查询数据
            if (StringUtils.isBlank(sortingMan) && StringUtils.isBlank(cabNo) && StringUtils.isBlank(sortingno)) {
                list = devRecordSortingService.findAll();
            } else {
                list = devRecordSortingService.findAllByObject(queryParams, null);
            }
        }

        // 转换为Excel导出结构体
        List<DevRecordSortingExcel> excelList = new java.util.ArrayList<>();
        int i = 1;
        for (DevRecordSorting record : list) {
            DevRecordSortingExcel excel = new DevRecordSortingExcel();
            excel.setIndex(i++);
            excel.setSortingMan(record.getSortingMan());
            excel.setCabNo(record.getCabNo());
            excel.setSortingno(record.getSortingno());
            excel.setClientName(record.getClientName());
            excel.setEmployeeName(record.getEmployeeName());
            excel.setEmployeePhone(record.getEmployeePhone());
            excel.setDepartment(record.getDepartment());
            excel.setSubDepartment(record.getSubDepartment());
            excel.setConfigurationCategory(record.getConfigurationCategory());
            excel.setProductModelNumber(record.getProductModelNumber());
            excel.setProductColor(record.getProductColor());
            excel.setLogoPosition(record.getLogoPosition());
            excel.setProductSpecification(record.getProductSpecification());
            excel.setEmployeeId(record.getEmployeeId());
            excel.setRfidTagNumber(record.getRfidTagNumber());
            excel.setProductSerialNumber(record.getProductSerialNumber());
            excel.setSortingTime(record.getSortingTime());
            excelList.add(excel);
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("分拣记录导出", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), DevRecordSortingExcel.class)
                .sheet("分拣记录")
                .doWrite(excelList);
    }

}
