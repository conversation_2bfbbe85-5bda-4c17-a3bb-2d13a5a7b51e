package com.rutong.platform.sys.entity;

import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;

import com.rutong.platform.common.entity.BaseEntity;

/**
 * 菜单权限表 sys_menu
 */
@Entity
@Table(name = "sys_menu")
public class SysMenu extends BaseEntity{
	/** 菜单名称 */
	@Column(nullable = false,length = 100)
	private String menuname;

	/** 显示顺序 */
	@Column(nullable = false)
	private Integer orderno;
	
	/** 路由地址 */
	@Column(nullable = false)
	private String path;

	/** 路由参数 */
	private String query;
	
	/** 类型（0菜单 1功能） */
	private String menutype;
	
	/** 权限字符串 */
	private String perms;

	/** 菜单图标 */
	private String icon;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "parentid")
	private SysMenu menu;
	
	@OneToMany(mappedBy = "menu")
	@OrderBy(value = "orderno ASC")
	private Set<SysMenu> menus = new HashSet<SysMenu>(0);
	
	public String getMenuname() {
		return menuname;
	}

	public void setMenuname(String menuname) {
		this.menuname = menuname;
	}

	public Integer getOrderno() {
		return orderno;
	}

	public void setOrderno(Integer orderno) {
		this.orderno = orderno;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getQuery() {
		return query;
	}

	public void setQuery(String query) {
		this.query = query;
	}

	public String getMenutype() {
		return menutype;
	}

	public void setMenutype(String menutype) {
		this.menutype = menutype;
	}

	public String getPerms() {
		return perms;
	}

	public void setPerms(String perms) {
		this.perms = perms;
	}

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public SysMenu getMenu() {
		return menu;
	}

	public void setMenu(SysMenu menu) {
		this.menu = menu;
	}

	public Set<SysMenu> getMenus() {
		return menus;
	}

	public void setMenus(Set<SysMenu> menus) {
		this.menus = menus;
	}

}
