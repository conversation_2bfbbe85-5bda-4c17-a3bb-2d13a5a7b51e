package com.rutong.platform.wechat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.wechat.dto.ChatInfoDTO;
import com.rutong.platform.wechat.entity.SubmitChatInfo;
import com.rutong.platform.wechat.mapper.SubmitChatInfoMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 提交的聊天信息服务类
 */
@Service
public class SubmitChatInfoService extends ServiceImpl<SubmitChatInfoMapper, SubmitChatInfo> implements IService<SubmitChatInfo> {
    
    private static final Logger log = LoggerFactory.getLogger(SubmitChatInfoService.class);

    /**
     * 保存提交的聊天信息
     * 
     * @param chatInfoDTO 聊天信息DTO
     * @param userName 用户名
     * @param employeeId 员工工号
     * @param userIp 用户IP
     * @return 是否保存成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveSubmitChatInfo(ChatInfoDTO chatInfoDTO, String userName, String employeeId, String userIp) {
        try {
            log.debug("开始保存聊天信息: category={}, model={}, status={}, user={}, employeeId={}", 
                chatInfoDTO.getConfigurationCategory(),
                chatInfoDTO.getProductModelNumber(),
                String.join(",", chatInfoDTO.getStatus()),
                userName,
                employeeId);
            
            // 获取当前登录用户信息，用于填充审计字段
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null) {
                log.error("无法获取登录用户信息，无法设置审计字段");
                return false;
            }
            
            Date now = new Date();
            
            // 创建实体
            SubmitChatInfo submitChatInfo = SubmitChatInfo.builder()
                .configurationCategory(chatInfoDTO.getConfigurationCategory())
                .productModelNumber(chatInfoDTO.getProductModelNumber())
                .userName(userName)
                .employeeId(employeeId)
                .submitTime(now)
                .userIp(userIp)
                .processStatus(0) // 初始状态：未处理
                .build();
            
            // 设置状态列表
            submitChatInfo.setStatusList(chatInfoDTO.getStatus());
            
            // 设置审计字段
            submitChatInfo.setCreateBy(loginUser.getUsername());
            submitChatInfo.setCreateTime(now);
            submitChatInfo.setOwnerUserId(loginUser.getUserId());
            // 设置默认部门ID
            submitChatInfo.setOwnerDeptId(loginUser.getDeptId() != null ? loginUser.getDeptId() : "0");
            
            // 保存到数据库
            boolean result = this.save(submitChatInfo);
            
            if (result) {
                log.debug("聊天信息保存成功，ID: {}", submitChatInfo.getId());
            } else {
                log.warn("聊天信息保存失败");
            }
            
            return result;
        } catch (Exception e) {
            log.error("保存聊天信息异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 按员工工号、配置品类、产品款号查询沟通事项
     */
    public List<SubmitChatInfo> getByEmployeeIdAndConfigAndProduct(String employeeId, String configurationCategory, String productModelNumber) {
        log.info("开始查询沟通事项: employeeId={}, category={}, model={}", 
            employeeId, configurationCategory, productModelNumber);
            
        List<SubmitChatInfo> result = this.baseMapper.findByEmployeeIdAndConfigurationCategoryAndProductModelNumber(
            employeeId, configurationCategory, productModelNumber);
            
        log.info("查询沟通事项结果: 找到{}条记录", result != null ? result.size() : 0);
        
        return result;
    }
    
    /**
     * 根据员工ID查询所有沟通事项
     */
    public List<SubmitChatInfo> getByEmployeeId(String employeeId) {
        log.info("查询员工所有沟通事项: employeeId={}", employeeId);
        
        List<SubmitChatInfo> result = this.baseMapper.findByEmployeeId(employeeId);
        
        log.info("员工沟通事项查询结果: 找到{}条记录", result != null ? result.size() : 0);
        if (result != null && !result.isEmpty()) {
            SubmitChatInfo first = result.get(0);
            log.info("最新一条沟通事项: id={}, category={}, model={}, status={}, submitTime={}", 
                first.getId(), first.getConfigurationCategory(), first.getProductModelNumber(), 
                first.getStatus(), first.getSubmitTime());
        }
        
        return result;
    }
} 