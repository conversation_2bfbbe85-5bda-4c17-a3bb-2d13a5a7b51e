package com.rutong.platform.sys.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysDict;
import com.rutong.platform.sys.service.SysDictService;

@RestController
@RequestMapping("/system/dict")
@LogDesc(title = "字典管理")
public class SysDictController extends CrudController<SysDict>{
	
	@Autowired
	private SysDictService dictService;

	@Override
	public BaseService<SysDict> getService() {
		return dictService;
	}

	@GetMapping(value = "/getDictTypeOptionSelect.do")
	public ResultBean getDictTypeOptionSelect() {
		List<SysDict> all = dictService.findAll();
		return ResultBean.success(all);
	}
}
