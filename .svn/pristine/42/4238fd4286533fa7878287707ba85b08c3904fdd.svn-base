package com.rutong.platform.app.controller;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.app.service.AppService;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.device.entity.DevInCabinet;
import com.rutong.platform.device.entity.DevSorting;
import com.rutong.platform.device.service.InCabinetService;
import com.rutong.platform.device.service.SortingService;
import com.rutong.platform.wms.entity.WmsFeedback;
import com.rutong.platform.wms.service.WmsDeliveryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/app")
public class PdaController {

    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;

    @Autowired
    private WmsDeliveryService wmsDeliveryService;

    @Autowired
    private AppService appService;




    /**
     * 提交盘点到的服装
     */
    @PostMapping("/login.do")
    public ResultBean login(String username, String password) {
        String token = appService.login(username, password);
        return ResultBean.success(token);

    }

    @GetMapping("/getUserinfo.do")
    public ResultBean getUserinfo() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return ResultBean.success(loginUser.getUser());
    }

    /**
     * 根据rifd查询服装
     *
     * @param rfid
     * @return
     */
    @GetMapping("/getClothingByRfid.do")
    public ResultBean getClothingByRfid(String rfid) {
        EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfid(rfid);
        if (employeeProductConfiguration == null) {
            return ResultBean.error("没有查到有效数据", 500);
        }
        return ResultBean.success(employeeProductConfiguration);
    }

    /**
     * 提交盘点到的服装
     */
    @PostMapping("/postClothingList.do")
    public ResultBean postClothingList(
            @RequestList(clazz = EmployeeProductConfiguration.class) List<EmployeeProductConfiguration> proConfigurations,
            String signImg, String type) {
        // 生成一次交接记录
        if (proConfigurations.size() == 0) {
            return ResultBean.error("服装信息无效", 500);
        }
        wmsDeliveryService.generateBatch(proConfigurations, signImg, type);
        return ResultBean.success();
    }


    /**
     * 车辆列表
     *
     * @return
     */
    @GetMapping("/getCarList.do")
    public ResultBean getCarList() {
        List<String> list = appService.getCarList();
        return ResultBean.success(list);
    }

    /**
     * 车况报备
     */
    @PostMapping("/postCarinfo.do")
    public ResultBean postCarinfo(String carno, String result) {
        appService.saveCarinfo(carno, result);
        return ResultBean.success();
    }

    /**
     * 意见反馈
     */
    @PostMapping("/postFeedback.do")
    public ResultBean postFeedback(WmsFeedback feedback) {
        appService.saveFeedback(feedback);
        return ResultBean.success();
    }
}
