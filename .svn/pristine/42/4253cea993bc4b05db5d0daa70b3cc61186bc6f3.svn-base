package com.rutong.platform.sys.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.security.service.PermissionService;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.framework.utils.TreeBuilder;
import com.rutong.platform.sys.entity.SysConfig;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.sys.entity.SysUserSetting;
import com.rutong.platform.sys.pojo.LoginBody;
import com.rutong.platform.sys.pojo.MenuDataItem;
import com.rutong.platform.sys.service.SysConfigService;
import com.rutong.platform.sys.service.SysLoginService;
import com.rutong.platform.sys.service.SysMenuService;
import com.rutong.platform.sys.service.SysUserSettingService;

/**
 * 登录验证
 */
@RestController
public class SysLoginController {
	@Autowired
	private SysLoginService loginService;
	@Autowired
	private SysMenuService menuService;
	@Autowired
	private PermissionService permissionService;
	@Autowired
	private SysConfigService configService;
	@Autowired
	private SysUserSettingService settingService;
	/**
	 * 登录方法
	 * 
	 * @param loginBody 登录信息
	 * @return 结果
	 */
	@PostMapping("/login.do")
	public ResultBean login(@RequestBody LoginBody loginBody) {
		String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
				loginBody.getUuid());
		return ResultBean.success(token);
	}

	/**
	 * 获取用户信息
	 * 
	 * @return 用户信息
	 */
	@GetMapping("/getInfo.do")
	public ResultBean getInfo() {
		SysUser user = SecurityUtils.getLoginUser().getUser();
		Set<String> permissions = permissionService.getMenuPermission(user);
		Map<String, Object> data = new HashMap<String, Object>();
		
		Map<String, Object> sysSetting = new HashMap<String, Object>();
		List<SysConfig> configs = configService.findAll();
		for (SysConfig sysConfig : configs) {
			sysSetting.put(sysConfig.getConfigkey(), sysConfig.getConfigvalue());
		}
		SysUserSetting userSetting = settingService.findByUserid(user.getId());
		if(userSetting == null) {
			userSetting = new SysUserSetting();
			userSetting.setLayout("side");
		}
		data.put("user", user);
		data.put("permissions", permissions);
		data.put("sysSetting", sysSetting);
		data.put("userSetting", userSetting);
		return ResultBean.success(data);
	}

	/**
	 * 获取路由信息
	 * 
	 * @return 路由信息
	 */
	@GetMapping("/getRouters.do")
	public ResultBean getRouters() {
		LoginUser loginUser = SecurityUtils.getLoginUser();
		List<MenuDataItem> menus = menuService.selectMenuTreeByUserId(loginUser);
		return ResultBean.success(TreeBuilder.buildListToTree(menus));
	}
}
