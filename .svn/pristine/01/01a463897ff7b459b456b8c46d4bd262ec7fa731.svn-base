package com.rutong.platform.device.entity;

import com.rutong.platform.client.dto.EmployeeProductConfigurationDto;
import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * 分拣工作台
 */
@Entity
@Table(name = "dev_record_sorting")
@Data
public class DevRecordSorting extends EmployeeProductConfigurationDto {


	//分拣人
    private String sortingMan;

    //格口
    private String cabNo;

    //分拣编号
    private String sortingno;
    
    //客户名称
    private String clientName;
    
    //分拣时间
    private Date sortingTime;
}
