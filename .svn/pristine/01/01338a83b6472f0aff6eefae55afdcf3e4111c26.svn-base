package com.rutong.platform.wechat.controller;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.Log;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.wechat.dto.ChatInfoDTO;
import com.rutong.platform.wechat.dto.ChatItemDTO;
import com.rutong.platform.wechat.service.ChatInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 聊天信息控制器
 */
@RestController
@RequestMapping("/wechat/chatinfo")
@LogDesc(title = "聊天信息")
public class ChatInfoController {

    //记录日志
    private static final Logger log = LoggerFactory.getLogger(ChatInfoController.class);
    
    @Autowired
    private ChatInfoService chatInfoService;
    
    /**
     * 获取当前登录用户的沟通事项
     * 
     * @return 沟通事项信息
     */
    @Log(title = "查询沟通事项", desc = "查询当前登录用户的沟通事项")
    @GetMapping(value = "/getCurrentUserChatInfo.do")
    public ResultBean getCurrentUserChatInfo() {
        try {
            // 检查用户是否登录
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null || loginUser.getUser() == null) {
                log.warn("用户未登录，无法查询沟通事项");
                return ResultBean.error("用户未登录", 401);
            }
            
            // 查询沟通事项
            ChatInfoDTO chatInfo = chatInfoService.getChatInfoByLoginUser();
            
            // 返回结果
            return ResultBean.success(chatInfo);
        } catch (Exception e) {
            log.error("查询沟通事项失败", e);
            return ResultBean.error("查询沟通事项失败，请稍后重试", 500);
        }
    }
    
    /**
     * 提交聊天信息
     * 
     * @param chatInfoDTOList 包含configurationCategory、productModelNumber、status三个字段的请求数据列表
     *                        其中status字段是字符串列表，支持多选 (缺失、放错、破损、污渍、标签破损)
     * @return 操作结果
     */
    @PostMapping("/submitChatInfo.do")
    @Log(title = "提交聊天信息", desc = "用户提交聊天信息")
    public ResultBean submitChatInfo(@RequestBody List<ChatInfoDTO> chatInfoDTOList) {
        try {
            // 基本参数校验
            if (chatInfoDTOList == null || chatInfoDTOList.isEmpty()) {
                log.warn("接收到空的聊天信息请求");
                return ResultBean.error("提交的数据不能为空", 400);
            }
            
            // 检查用户是否登录
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null || loginUser.getUser() == null) {
                log.warn("用户未登录，无法提交聊天信息");
                return ResultBean.error("用户未登录", 401);
            }
            
            // 记录请求数据
            log.info("接收到提交聊天信息请求，数据条数: {}", chatInfoDTOList.size());
            
            // 检查状态是否在有效范围内
            for (ChatInfoDTO dto : chatInfoDTOList) {
                if (dto.getStatus() != null) {
                    for (String status : dto.getStatus()) {
                        if (!ChatItemDTO.ALL_STATUSES.contains(status)) {
                            log.warn("状态[{}]无效，有效状态为: {}", status, String.join(", ", ChatItemDTO.ALL_STATUSES));
                            return ResultBean.error("状态[" + status + "]无效", 400);
                        }
                    }
                }
            }
            
            // 批量保存所有聊天信息
            boolean allSuccess = true;
            for (ChatInfoDTO chatInfoDTO : chatInfoDTOList) {
                boolean success = chatInfoService.saveChatInfo(chatInfoDTO);
                if (!success) {
                    allSuccess = false;
                    log.warn("部分聊天信息保存失败: configurationCategory={}, productModelNumber={}, status={}",
                            chatInfoDTO.getConfigurationCategory(),
                            chatInfoDTO.getProductModelNumber(),
                            chatInfoDTO.getStatus() != null ? String.join(",", chatInfoDTO.getStatus()) : "");
                }
            }
            
            if (allSuccess) {
                return ResultBean.success("提交成功");
            } else {
                return ResultBean.error("部分数据提交失败，请检查数据后重试", 400);
            }
        } catch (Exception e) {
            log.error("提交聊天信息失败: {}", e.getMessage(), e);
            return ResultBean.error("系统异常，请稍后重试: " + e.getMessage(), 500);
        }
    }
} 