package com.rutong.platform.wechat.controller;

import com.rutong.framework.bean.ResultBean;
import com.rutong.platform.wechat.entity.WechatFaq;
import com.rutong.platform.wechat.service.WechatFaqService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: mkwang
 * @date: 2025年4月29日08:49:21
 * @description:常见问题查询接口
 */
@RestController
@RequestMapping("/wechat/wechat-faq") // 修改基础路径，避免与SysFaqController冲突
public class WechatFaqController {

    @Autowired // 自动注入WechatFaqService实例
    private WechatFaqService wechatFaqService;

    @GetMapping("/list") // 处理GET请求，URL为/wechat/wechat-faq/list
    public List<WechatFaq> list() {
        return wechatFaqService.list(); // 调用wechatFaqService的list方法，返回常见问题列表
    }

    @PostMapping("/create") // 修改添加接口的路径，使用更RESTful的命名
    public ResultBean add(@RequestBody WechatFaq wechatFaq) {
        wechatFaqService.add(wechatFaq);
        return ResultBean.success("添加成功");
    }

    @PutMapping("/update/{id}")
    // 更新接口，根据ID更新WechatFaq对象
    public ResultBean update(@PathVariable String id, @RequestBody WechatFaq wechatFaq) {
        try {
            // 判断参数是否为空
            if (wechatFaq == null) {
                return ResultBean.error("更新失败：参数不完整", 400);
            }
            wechatFaqService.update(wechatFaq);
            return ResultBean.success("更新成功");
        } catch (Exception e) {
            return ResultBean.error("更新失败: " + e.getMessage(), 500);
        }
    }

    @DeleteMapping("/delete/{id}") // 处理DELETE请求，URL为/wechat/wechat-faq/delete/{id}
    public ResultBean delete(@PathVariable String id) {
        try {
            wechatFaqService.delete(id);
            return ResultBean.success("删除成功");
        } catch (Exception e) {
            return ResultBean.error("删除失败: " + e.getMessage(), 500);
        }
    }
}