package com.rutong.platform.app.controller;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.framework.utils.StringUtils;
import com.rutong.platform.app.service.AppService;
import com.rutong.platform.client.entity.DemandResponse;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.DemandResponseService;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.device.entity.DevInCabinet;
import com.rutong.platform.device.entity.DevRecordSorting;
import com.rutong.platform.device.service.DevRecordSortingService;
import com.rutong.platform.device.service.InCabinetService;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.sys.service.SysUserService;
import com.rutong.platform.washing.entity.WashingTask;
import com.rutong.platform.washing.service.WashingTaskService;
import com.rutong.platform.wms.entity.WmsException;
import com.rutong.platform.wms.entity.WmsExceptionType;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import com.rutong.platform.wms.service.WmsDeliveryService;
import com.rutong.platform.wms.service.WmsExceptionService;
import com.rutong.platform.wms.service.WmsExceptionTypeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户端接口
 */
@RestController
@RequestMapping(value = "/client")
public class ClientController {

    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private WmsDeliveryService wmsDeliveryService;
    @Autowired
    private InCabinetService inCabinetService;
    @Autowired
    private WmsExceptionTypeService exceptionTypeService;
    @Autowired
    private WmsExceptionService exceptionService;
    @Autowired
    private DemandResponseService demandResponseService;
    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;

    @Autowired
    private WashingTaskService washingTaskService;

    @Autowired
    private DevRecordSortingService devRecordSortingService;
    @Autowired
    private AppService appService;

    /**
     * 根据工号登录
     *
     * @param cardno
     * @return
     */
    @PostMapping(value = "/loginByCardno.do")
    public ResultBean loginByName(String cardno) {
        // 根据工号查询用户信息
        SysUser user = sysUserService.findByCardno(cardno);
        // 如果用户信息为空，则返回错误信息
        if (StringUtils.isNull(user)) {
            return ResultBean.error("无效工号", 500);
        }
        // 返回登录结果
        return ResultBean.success(appService.login(user.getPhone()));
    }


    /**
     * 根据rifd查询服装，任务单隔离
     *
     * @param rfid
     * @return
     */
    @GetMapping("/getClothingByRfid.do")
    public ResultBean getClothingByRfid(String rfid) {
        EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfid(rfid);
        if (employeeProductConfiguration == null) {
            return ResultBean.error("无效标签", 500);
        }
        return ResultBean.success(employeeProductConfiguration);
    }

    /**
     * 根据rifd和任务单号查询服装
     *
     * @param rfid   rifd
     * @param taskId 任务id
     * @return
     */
    @GetMapping("/getClothingByRfidAndTaskId.do")
    public ResultBean getClothingByRfid(String rfid, String taskId) {
        WashingTask byId = washingTaskService.findById(taskId);
        EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfidAndClientId(rfid, byId.getClientId());
        if (employeeProductConfiguration == null) {
            return ResultBean.error("没有查到有效数据", 500);
        }
        return ResultBean.success(employeeProductConfiguration);
    }

    /**
     * 根据rifd查询服装及存衣柜信息
     *
     * @param rfid
     * @return
     */
    @GetMapping("/getClothingAndShelfByRfid.do")
    public ResultBean getClothingAndShelfByRfid(String rfid) {
        // 根据rfid查询员工产品配置信息
        EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfid(rfid);
        // 如果员工产品配置信息为空，则返回错误信息
        if (employeeProductConfiguration == null) {
            return ResultBean.error("无效标签", 500);
        }
        // 根据柜号查询存衣柜信息
        DevInCabinet cabinet = inCabinetService.findByCabNo(employeeProductConfiguration.getLockerSerialNumber());
        // 创建一个Map，用于存储查询结果
        Map<String, Object> map = new HashMap<String, Object>();
        // 将员工产品配置信息放入Map中
        map.put("employeeProduct", employeeProductConfiguration);
        // 根据rfid查询需求响应信息
        List<DemandResponse> demandResponses = demandResponseService
                .byRfid(employeeProductConfiguration.getRfidTagNumber());
        // 如果需求响应信息不为空，则将第一个需求响应信息放入Map中
        Map<String, Object> objectMap = new HashMap<>();
        if (demandResponses.size() > 0) {
            // 使用Stream拼接conditionInfo
            String conditionInfoStr = demandResponses.stream()
                    .map(DemandResponse::getConditionInfo)  // 假设有getConditionInfo()方法
                    .filter(Objects::nonNull)                // 过滤null值
                    .collect(Collectors.joining(", "));      // 用逗号拼接
            objectMap.put("conditionInfo", conditionInfoStr);
            map.put("error", objectMap);
            // 否则将null放入Map中
        } else {
            map.put("error", null);
        }
        // 将存衣柜信息放入Map中
        map.put("cabinet", cabinet);
        // 返回查询结果
        DevRecordSorting devRecordSorting = devRecordSortingService.byRfid(employeeProductConfiguration.getRfidTagNumber());
        Date sortingTime = null;
        if (devRecordSorting == null) {
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DATE, -1);
            sortingTime = cal.getTime();
        } else {
            sortingTime = devRecordSorting.getSortingTime();
        }
        if (sortingTime == null) {
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DATE, -1);
            sortingTime = cal.getTime();
        }
        // 转换为Java 8时间对象
        Instant sortingInstant = sortingTime.toInstant();
        Instant nowInstant = Instant.now();

        // 计算时间差
        Duration duration = Duration.between(sortingInstant, nowInstant);
        long hoursDiff = duration.toHours();

        //上次读取时间跟当前时间超过1小时 则增加分拣记录
        if (hoursDiff >= 1) {
            DevRecordSorting recordSorting = new DevRecordSorting();
            BeanUtils.copyProperties(employeeProductConfiguration, recordSorting);
            recordSorting.setSortingTime(new Date());
            // 设置基本分拣信息
            LoginUser loginUser = SecurityUtils.getLoginUser();
            recordSorting.setSortingMan(loginUser.getUser().getNickname());  // 分拣人
            recordSorting.setCabNo(employeeProductConfiguration.getLockerSerialNumber());  // 格口
            if (employeeProductConfiguration.getLockerSerialNumber() != null) {
                recordSorting.setSortingno(employeeProductConfiguration.getLockerSerialNumber().split("-")[0]);  // 分拣编号
            }
            recordSorting.setClientName(employeeProductConfiguration.getEnterpriseName());//客户名称
            recordSorting.setId(null);
            devRecordSortingService.save(recordSorting);
        }
        return ResultBean.success(map);
    }

    /**
     * 根据服装品类查询异常类型
     *
     * @param coltype 服装品类
     * @return 异常类型列表
     */
    @GetMapping("/getExceptionByClotype.do")
    public ResultBean getExceptionByClotype(String coltype) {
        // 根据服装品类查询异常类型
        List<WmsExceptionType> exceptionTypes = exceptionTypeService.finAllByColthingType(coltype);
        // 返回异常类型列表
        return ResultBean.success(exceptionTypes);
    }

    /**
     * 提交隧道机盘点到的服装
     */
    @PostMapping("/postClothingList.do")
    public ResultBean postClothingList(
            @RequestList(clazz = String.class) List<String> proConfigurations,
            String type, String orderId) {
        ResultBean validateResult = appService.validClothing(proConfigurations, orderId);
        if (!validateResult.isSuccess()) {
            return validateResult;
        }
        // 生成一次交接记录
        wmsDeliveryService.generateBatch(proConfigurations, null, type, "pc", orderId);
        return ResultBean.success();
    }


    /**
     * 提交检品异常信息
     */
    @PostMapping("/postCheckException.do")
    public ResultBean postCheckException(WmsException exception) {
        exception.setStatus("未处理");
        // 调用异常服务保存异常信息
        exceptionService.save(exception);
        // 返回成功结果
        return ResultBean.success();
    }

}
