package com.rutong.platform.wms.service;

import java.util.Date;

import com.rutong.platform.wms.entity.WmsClothingExamine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.rutong.framework.utils.BeanUtils;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsClothing;
import com.rutong.platform.wms.entity.WmsClothingOutbound;

@Service
public class WmsClothingService extends BaseService<WmsClothing> {

    @Autowired
    private WmsClothingOutboundService outboundService;

    public WmsClothing findByCloRfid(String cloRfid) {
        return dao.findByPropertyFirst(WmsClothing.class, WmsClothing.FIELD_COL_RFID, cloRfid);
    }

    public WmsClothing findByCloNumber(String cloNumber) {
        return dao.findByPropertyFirst(WmsClothing.class, WmsClothing.FIELD_COL_NUMBER, cloNumber);
    }
    public WmsClothing findByCloNumberAndEnterpriseName(String cloNumber, String enterpriseName) {
        return dao.findByPropertyFirst(WmsClothing.class, WmsClothing.FIELD_COL_NUMBER, cloNumber, "enterpriseName", enterpriseName);
    }

    public void updateOutbound(WmsClothing wmsClothing, String result, Long count, String type) {
        WmsClothingOutbound wmsClothingOutbound = new WmsClothingOutbound();
        BeanUtils.copyProperties(wmsClothingOutbound, wmsClothing);
        wmsClothingOutbound.setId(null);
        wmsClothingOutbound.setOutboundTime(new Date());
        wmsClothingOutbound.setOutboundResult(result);
        wmsClothingOutbound.setOperationType(type);
        wmsClothingOutbound.setCloCount(count);
        outboundService.save(wmsClothingOutbound);
    }



}
