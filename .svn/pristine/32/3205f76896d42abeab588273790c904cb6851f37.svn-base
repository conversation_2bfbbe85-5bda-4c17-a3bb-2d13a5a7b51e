package com.rutong.platform.client.entity;

import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @ClassName BusinessCommunication * @Description 业务沟通
 * <AUTHOR>
 * @Date 15:51 2024/8/22
 * @Version 1.0
 **/
@Data
@Entity
@Table(name = "cli_business_communication")
public class BusinessCommunication extends BaseEntity {
    /**
     * 合同编号
     */
    private String contractNumber;


    private String recipient;
    private String selectedType;
    private String content;
    private String timestamp;
    private String user;
    private String userId;


    private Long status;//状态

}
