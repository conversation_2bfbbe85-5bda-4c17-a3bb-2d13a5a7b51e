package com.rutong.platform.client.service;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.utils.BeanMapUtils;
import com.rutong.framework.utils.SqlQueryBuilder;
import com.rutong.platform.client.entity.*;
import com.rutong.platform.common.pojo.TreeData;
import com.rutong.platform.common.service.BaseService;
import javassist.expr.NewArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EmployeeProductConfigurationService extends BaseService<EmployeeProductConfiguration> {
    @Autowired
    private ClientInfoService clientInfoService;
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;

    public static final String EMPLOYEE_ID = "employeeId";

    public BigInteger countByContractNumber(String contractNumber) {
        String sql = "SELECT COUNT(*) FROM employee_product_configuration WHERE contract_number = ?0 ";
        return dao.countSQLQuery(sql, contractNumber);
    }

    public EmployeeProductConfiguration byRfid(String rfid) {
        return dao.findByPropertyFirst(EmployeeProductConfiguration.class,
                EmployeeProductConfiguration.FIELD_RFID_TAG_NUMBER, rfid, "status", "合作中");
    }

    public EmployeeProductConfiguration byProductSerialNumber(String productSerialNumber) {
        return dao.findByPropertyFirst(EmployeeProductConfiguration.class,
                EmployeeProductConfiguration.FIELD_COL_PRODUCT_SERIAL_NUMBER, productSerialNumber);
    }

    public List<EmployeeProductConfiguration> findByEmployeePhone(String employeePhone) {
        return dao.findByProperty(EmployeeProductConfiguration.class,
                "employeePhone", employeePhone);
    }

    public List<EmployeeProductConfiguration> findByEmployeeId(String employeeId) {
        if (StringUtils.isEmpty(employeeId)) {
            return new ArrayList<>();
        }
        return dao.findByProperty(EmployeeProductConfiguration.class, EMPLOYEE_ID, employeeId, "status", "合作中");
    }

    @Transactional
    public Map<String, Object> importData(List<EmployeeProductConfiguration> dataList, String contractNumber) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, String>> errors = new ArrayList<>();
        ClientInfo clientInfo = clientInfoService.findByContractNumber(contractNumber);

        if (clientInfo == null) {
            errors.add(createError(0, "全局错误", "找不到对应的合同信息"));
            result.put("errors", errors);
            return result;
        }

        // 预查询已存在的数据
        Map<String, Set<String>> existingRfids = employeeProductConfigurationService.getAllExisting();
        Set<String> productSerialNumberList = existingRfids.get("productSerialNumberList");
        Set<String> rfidNumberList = existingRfids.get("rfidNumberList");
        Set<String> rfidNumberWorkList = existingRfids.get("rfidNumberWorkList");
        List<EmployeeProductConfiguration> newList = new ArrayList<>();


        for (int i = 0; i < dataList.size(); i++) {
            int rowNumber = i + 2; // Excel 数据行从第2行开始
            EmployeeProductConfiguration config = dataList.get(i);
            StringBuilder errorMsg = new StringBuilder();
            List<String> errorDetails = new ArrayList<>();

            // 验证每周换洗日期
            if (!validateWeeklyWashDate(config.getWeeklyWashDate())) {
                errorDetails.add("每周换洗日期格式不正确，需为1,2,3,4,5,6,7格式");
            }
            if (StringUtils.isEmpty(config.getSingleWashRentalFee())) {
                errorDetails.add("单次洗涤租赁费用不能为空");
            }
            // 检查RFID重复
            if (rfidNumberWorkList.contains(config.getRfidTagNumberWork())) {
                errorDetails.add("工作服RFID标签编码已存在: " + config.getRfidTagNumberWork());
            }
            // 检查RFID重复
            if (rfidNumberList.contains(config.getRfidTagNumber())) {
                errorDetails.add("RFID标签已存在: " + config.getRfidTagNumber());
            }

            // 检查产品序列号重复
//            if (existingSerials.contains(config.getProductSerialNumber())) {
//                errorDetails.add("产品序列号已存在: " + config.getProductSerialNumber());
//            }

            // 如果有错误，拼接错误信息
            if (!errorDetails.isEmpty()) {
                errorMsg.append("本行包含").append(errorDetails.size()).append("个错误：");
                errorMsg.append(String.join("；", errorDetails));
                errors.add(createError(rowNumber, "多字段错误", errorMsg.toString()));

                // 标记为无效数据
                continue;
            }

            // 所有校验通过后的处理
            config.setContractNumber(contractNumber);
            config.setStatus("合作中");
            config.setEnterpriseName(clientInfo.getEnterpriseName());
            rfidNumberWorkList.add(config.getRfidTagNumber());
            rfidNumberList.add(config.getRfidTagNumber());
            productSerialNumberList.add(config.getProductSerialNumber());
            newList.add(config);
        }

        result.put("errorList", errors);
        result.put("successCount", newList.size());

        // 批量保存数据
        try {
            employeeProductConfigurationService.saveAll(newList);

            // 更新合同信息统计
            List<FilterCondition> filters = new ArrayList<>();
            FilterCondition filterCondition = new FilterCondition();
            filterCondition.setProperty("contractNumber");
            filterCondition.setOperator(FilterCondition.EQ);
            filterCondition.setValue(clientInfo.getContractNumber());
            filters.add(filterCondition);
            GridParams gridParams = new GridParams(1, 1);
            PageInfo<EmployeeProductConfiguration> employeeProductConfigurationPageInfo = employeeProductConfigurationService.fetchdataGroup(gridParams, filters, null);
            clientInfo.setCountData(employeeProductConfigurationPageInfo.getTotal());

            return result;
        } catch (Exception e) {
            throw new RuntimeException("数据保存失败: " + e.getMessage());
        }
    }

    public static Map<String, String> createError(int row, String type, String message) {
        Map<String, String> error = new HashMap<>();
        error.put("row", String.valueOf(row));
        error.put("type", type);
        error.put("message", message);
        return error;
    }

    private boolean validateWeeklyWashDate(String date) {
        return date != null && date.matches("^([1-7],)*[1-7]$");
    }

    // Service 层新增方法
    public Map<String, Set<String>> getAllExisting() {
        List<EmployeeProductConfiguration> all = employeeProductConfigurationService.findAll();
        Map<String, Set<String>> map = new HashMap<>();
        map.put("rfidNumberList", all.stream()
                .map(EmployeeProductConfiguration::getRfidTagNumber)
                .collect(Collectors.toSet()));
        map.put("productSerialNumberList", all.stream()
                .map(EmployeeProductConfiguration::getProductSerialNumber)
                .collect(Collectors.toSet()));
        map.put("rfidNumberWorkList", all.stream()
                .map(EmployeeProductConfiguration::getRfidTagNumberWork)
                .collect(Collectors.toSet()));


        return map;
    }

    public Set<String> getAllExistingSerials() {
        return employeeProductConfigurationService.findAll()
                .stream()
                .map(EmployeeProductConfiguration::getProductSerialNumber)
                .collect(Collectors.toSet());
    }

    public List<EmployeeProductConfiguration> groupDataInfo(String contractNumber) {

        String sql = "select * from employee_product_configuration where contract_number = '" + contractNumber + "' group by employee_name ,department,sub_department,configuration_category";

        List<EmployeeProductConfiguration> employeeProductConfigurationList = new ArrayList<>();
        List<Map<String, Object>> list = dao.executeSQLQuery(sql);
        for (Map<String, Object> map : list) {
            try {
                EmployeeProductConfiguration employeeProductConfiguration = BeanMapUtils.mapToBean(map, EmployeeProductConfiguration.class);
                employeeProductConfigurationList.add(employeeProductConfiguration);
            } catch (Exception e) {
                e.printStackTrace();
            }

        }


        return employeeProductConfigurationList;
    }

    public PageInfo<EmployeeProductConfiguration> fetchdataGroup(GridParams gridParams, List<FilterCondition> filters, List<SortCondition> sorts) {

        StringBuilder sqlBuilder = new StringBuilder("SELECT COUNT(*)  FROM (  SELECT    employee_name,     department,      sub_department,    configuration_category  FROM employee_product_configuration ");

        StringBuilder sqlBuilderQu = new StringBuilder("SELECT *,count(rfid_tag_number) as count_info FROM employee_product_configuration");

// 处理 WHERE 子句
        String whereClauseQu = SqlQueryBuilder.buildWhereClause(filters);
        if (!whereClauseQu.isEmpty()) {
            sqlBuilderQu.append(" WHERE ").append(whereClauseQu);
        }
        // 处理 WHERE 子句
        String whereClause = SqlQueryBuilder.buildWhereClause(filters);
        if (!whereClause.isEmpty()) {
            sqlBuilder.append(" WHERE ").append(whereClause);
        }
        sqlBuilder.append(" GROUP BY employee_id) AS grouped_results");
        BigInteger count = dao.countSQLQuery(sqlBuilder.toString());
        List<Map<String, Object>> dataList = new ArrayList();

        List<EmployeeProductConfiguration> employeeProductConfigurationList = new ArrayList<>();
        if (count.longValue() > 0L) {
            sqlBuilderQu.append(" group by employee_id limit " + gridParams.getStart() + "," + gridParams.getPageSize() + " ");
            dataList = dao.executeSQLQuery(sqlBuilderQu.toString());
            for (Map<String, Object> map : dataList) {
                try {
                    EmployeeProductConfiguration employeeProductConfiguration = BeanMapUtils.mapToBean(map, EmployeeProductConfiguration.class);
                    employeeProductConfigurationList.add(employeeProductConfiguration);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return new PageInfo<EmployeeProductConfiguration>(count.longValue(), employeeProductConfigurationList);
    }


    public EmployeeProductConfiguration selectUserByPhone(String phoneNumber) {
        return dao.findByPropertyFirst(EmployeeProductConfiguration.class, EmployeeProductConfigurationHistory.EMPLOYEE_PHONE, phoneNumber);
    }

}
