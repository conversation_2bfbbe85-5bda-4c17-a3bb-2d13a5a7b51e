package com.rutong.framework.bean;

import java.util.List;

public class TreeNode {
	protected String id;
	protected String parentid;
	/** 显示顺序 */
	protected Integer orderno;
	protected List<?> children;
	protected int level;
	protected boolean isLeaf;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getParentid() {
		return parentid;
	}

	public void setParentid(String parentid) {
		this.parentid = parentid;
	}

	public Integer getOrderno() {
		return orderno;
	}

	public void setOrderno(Integer orderno) {
		this.orderno = orderno;
	}

	public List<?> getChildren() {
		if (children.size() == 0) {
			return null;
		}
		return children;
	}

	public void setChildren(List<?> children) {
		this.children = children;
	}

	public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

	public boolean getIsLeaf() {
		return isLeaf;
	}

	public void setLeaf(boolean isLeaf) {
		this.isLeaf = isLeaf;
	}

	public String getKey() {
		return id;
	}
}
