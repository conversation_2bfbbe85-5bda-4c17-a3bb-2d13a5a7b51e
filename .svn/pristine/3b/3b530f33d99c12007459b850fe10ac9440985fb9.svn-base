package com.rutong.platform.device.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.rutong.platform.common.entity.BaseEntity;

/**
 * 分拣工作台
 */
@Entity
@Table(name = "dev_sorting")
public class DevSorting extends BaseEntity{
	
	@Column(nullable = false,unique = true)
	private String devno;
	
	@Column(nullable = false,unique = true)
	private String ipaddr;  //主机地址
	
	@Column(nullable = false,unique = true)
	private Integer orderno; //排列序号

	public String getDevno() {
		return devno;
	}

	public void setDevno(String devno) {
		this.devno = devno;
	}

	public String getIpaddr() {
		return ipaddr;
	}

	public void setIpaddr(String ipaddr) {
		this.ipaddr = ipaddr;
	}

	public Integer getOrderno() {
		return orderno;
	}

	public void setOrderno(Integer orderno) {
		this.orderno = orderno;
	}

}
