package com.rutong.platform.client.service;

import com.rutong.platform.client.entity.ExpenseSettlement;
import com.rutong.platform.client.entity.ExpenseSettlementItem;
import com.rutong.platform.common.service.BaseService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ExpenseSettlementItemService extends BaseService<ExpenseSettlementItem> {


    public List<ExpenseSettlementItem> selectByContractNumber(String contractNumber) {
        String sql = "from ExpenseSettlementItem where contractNumber = ?0 ";
        List<ExpenseSettlementItem> expenseSettlements = dao.executeQuery(sql, ExpenseSettlementItem.class, contractNumber);
        return expenseSettlements;
    }

}
