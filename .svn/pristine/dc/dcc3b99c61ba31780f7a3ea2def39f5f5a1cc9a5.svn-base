package com.rutong.platform.wms.excel;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.wms.entity.WmsDelivery;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;

import javax.persistence.*;
import java.util.Date;

/**
 * 配送明细导出
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ExcelIgnoreUnannotated
public class WmsDeliveryDetailNew extends BaseEntity {

    @ExcelProperty(value = "任务单号", index = 0)
    private String orderNumber;

    @ExcelProperty(value = "批次号", index = 1)
    private String batchNo;

    @ExcelProperty(value = "操作时间", index = 2)
    private Date createTime;

    @ExcelProperty(value = "合同编号", index = 3)
    private String contractNumber;

    @ExcelProperty(value = "客户名称", index = 4)
    private String enterpriseName;

    @ExcelProperty(value = "员工姓名", index = 5)
    private String employeeName;

    @ExcelProperty(value = "员工电话", index = 6)
    private String employeePhone;

    @ExcelProperty(value = "部门", index = 7)
    private String department;

    @ExcelProperty(value = "子部门", index = 8)
    private String subDepartment;

    @ExcelProperty(value = "配置品类", index = 9)
    private String configurationCategory;

    @ExcelProperty(value = "产品款号", index = 10)
    private String productModelNumber;

    @ExcelProperty(value = "产品颜色", index = 11)
    private String productColor;

    @ExcelProperty(value = "产品规格", index = 12)
    private String productSpecification;

    @ExcelProperty(value = "员工工号", index = 13)
    private String employeeId;

    @ExcelProperty(value = "工作服RFID标签编码", index = 14)
    private String rfidTagNumberWork;

    @ExcelProperty(value = "RFID标签号", index = 15)
    private String rfidTagNumber;

    @ExcelProperty(value = "产品序列号", index = 16)
    private String productSerialNumber;

    @ExcelProperty(value = "存衣柜序列号", index = 17)
    private String lockerSerialNumber;

    @ExcelProperty(value = "状态", index = 18)
    private Long status;

    @ExcelProperty(value = "工作服品类", index = 19)
    private String workWearCategory;
}
