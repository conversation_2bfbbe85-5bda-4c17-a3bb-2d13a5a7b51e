package com.rutong.platform.sys.service;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.rutong.framework.constant.UserConstants;
import com.rutong.framework.core.dao.Dao;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysMenu;
import com.rutong.platform.sys.pojo.MenuDataItem;

@Service
public class SysMenuService extends BaseService<SysMenu> {

	public Set<String> selectMenuPermsByRoleId(String id) {
		// TODO Auto-generated method stub
		return null;
	}

	public Collection<? extends String> selectMenuPermsByUserId(String id) {
		// TODO Auto-generated method stub
		return null;
	}

	public List<MenuDataItem> selectMenuTreeByUserId(LoginUser loginUser) {
		String sql = "select distinct m.id, m.parentid, m.menuname as name, m.path,"
				+ " ifnull(m.perms,'') as authority, m.icon, m.orderno " + "	from sys_menu m where m.menutype = 0 ";
		if (!loginUser.getUser().getUsertype().equals(UserConstants.SUPER_ADMIN)) {
			sql += "";
		}
		sql += "order by m.parentid, m.orderno";
		return dao.executeSQLQuery(sql, MenuDataItem.class);
	}

}
