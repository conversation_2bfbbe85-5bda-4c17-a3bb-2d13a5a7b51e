package com.rutong.platform.device.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.rutong.platform.common.entity.BaseEntity;

/**
 * 分拣存放柜 1000 个
 */

@Entity
@Table(name = "dev_in_cabinet")
public class DevInCabinet extends BaseEntity {

	public static final String FIELD_CABNO = "cabNo";

	@Column(nullable = false,unique = true)
	private Integer cabNo; // 衣柜编号

	private String status; // 状态： 占用/空闲
	
	@Transient
	private Integer startNo;
	@Transient
	private Integer endNo;

	public Integer getCabNo() {
		return cabNo;
	}

	public void setCabNo(Integer cabNo) {
		this.cabNo = cabNo;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getStartNo() {
		return startNo;
	}

	public void setStartNo(Integer startNo) {
		this.startNo = startNo;
	}

	public Integer getEndNo() {
		return endNo;
	}

	public void setEndNo(Integer endNo) {
		this.endNo = endNo;
	}

}
