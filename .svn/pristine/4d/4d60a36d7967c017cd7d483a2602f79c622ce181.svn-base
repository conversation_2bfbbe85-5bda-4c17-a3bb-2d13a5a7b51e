package com.rutong.platform.wechat.controller;

import com.rutong.framework.core.redis.RedisCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.Anonymous;
import com.rutong.platform.wechat.dto.PhoneLoginRequest;
import com.rutong.platform.wechat.dto.VerifyCodeRequest;
import com.rutong.platform.wechat.service.PhoneLoginService;

/**
 * 手机号登录控制器 - 微信小程序使用
 */
@RestController
@RequestMapping(value = "/wechat/phonelogin")
@Anonymous
public class PhoneLoginController {
    private static final Logger log = LoggerFactory.getLogger(PhoneLoginController.class);
    
    // 固定验证码
    private static final String FIXED_VERIFY_CODE = "123456";
    
    @Autowired
    private PhoneLoginService phoneLoginService;


    @Autowired
    private RedisCache redisCache;

    /**
     * 获取手机验证码
     * 
     * @param request 验证码请求对象
     * @return 是否成功
     */
    @PostMapping(value = "/getVerifyCode.do")
    public ResultBean getVerifyCode(@RequestBody VerifyCodeRequest request) {
        log.info("获取手机验证码, phone: {}", request.getPhone());
        try {
            phoneLoginService.sendVerifyCode(request.getPhone());
            return ResultBean.success("验证码已发送，测试环境固定验证码：" + FIXED_VERIFY_CODE);
        } catch (Exception e) {
            log.error("获取验证码失败", e);
            return ResultBean.error(e.getMessage(), 500);
        }
    }
    
    /**
     * 手机号登录
     * 
     * @param request 登录请求对象
     * @return token
     */
    @PostMapping(value = "/login.do")
    public ResultBean login(PhoneLoginRequest request) {
        log.info("手机号登录, phone: {}", request.getPhone());
        try {
            // 提示用户可以使用固定验证码
            if (request.getVerifyCode() == null || request.getVerifyCode().isEmpty()) {
                return ResultBean.error("验证码不能为空，测试环境可使用固定验证码：" + FIXED_VERIFY_CODE, 400);
            }
            
            String token = phoneLoginService.login(request.getPhone(), request.getVerifyCode());


            return ResultBean.success(token);
        } catch (Exception e) {
            log.error("手机号登录失败", e);
            return ResultBean.error(e.getMessage(), 500);
        }
    }
} 