package com.rutong.platform.wechat.controller;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.wechat.dto.UserInfoDTO;
import com.rutong.platform.wechat.dto.WorkClothesProfileDTO;
import com.rutong.platform.wechat.service.UserInfoService;
import com.rutong.platform.wechat.service.ChatInfoService;
import com.rutong.platform.wechat.dto.ChatInfoDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import com.rutong.framework.core.jpa.FilterCondition;

@RestController
@RequestMapping("/wechat/workClothesProfile")
public class WorkClothesProfileController {
    // 日志对象，用于记录日志信息
    private static final Logger log = LoggerFactory.getLogger(WorkClothesProfileController.class);
    
    // 自动注入UserInfoService，用于获取用户信息
    @Autowired
    private UserInfoService userInfoService;
    
    // 自动注入EmployeeProductConfigurationService，用于获取员工产品配置信息
    @Autowired
    private EmployeeProductConfigurationService configService;

    @Autowired
    private ChatInfoService chatInfoService;

    // 处理GET请求，获取工服档案详情
    @GetMapping("/detail")
    public ResultBean getWorkClothesProfile(@RequestParam(value = "clothesCode", required = false) String clothesCode) {
        try {
            // 获取当前登录用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null || loginUser.getUser() == null) {
                // 如果用户未登录，记录警告日志并返回错误信息
                log.warn("用户未登录，无法查询工服档案");
                return ResultBean.error("用户未登录", 401);
            }
            
            // 获取用户信息
            UserInfoDTO userInfo = userInfoService.getUserInfoByPhone(loginUser.getUser().getPhone());
            if (userInfo == null || userInfo.getEmployeeId() == null) {
                // 如果未找到用户信息或员工工号为空，记录警告日志并返回错误信息
                log.warn("未找到用户信息或员工工号为空");
                return ResultBean.error("未找到用户信息", 404);
            }
            
            String employeeId = userInfo.getEmployeeId();
            log.info("查询员工[{}]的工服档案", employeeId);

            // 直接从ChatInfoService获取聊天信息
            ChatInfoDTO chatInfo = chatInfoService.getChatInfoByLoginUser();
            if (chatInfo == null || chatInfo.getChatItems() == null || chatInfo.getChatItems().isEmpty()) {
                log.warn("未找到当前用户的聊天信息，无法匹配服装信息");
                return ResultBean.error("未找到服装信息", 404);
            }
            
            // 查询合作中的服装配置
            List<EmployeeProductConfiguration> configs = configService.findByEmployeeId(employeeId);
            if (configs == null || configs.isEmpty()) {
                log.warn("未找到员工[{}]的合作中服装信息", employeeId);
                return ResultBean.error("未找到服装信息", 404);
            }
            
            // 按照配置品类和产品款号分组
            Map<String, List<EmployeeProductConfiguration>> configMap = new HashMap<>();
            for (EmployeeProductConfiguration config : configs) {
                String key = config.getConfigurationCategory() + "_" + config.getProductModelNumber();
                configMap.computeIfAbsent(key, k -> new ArrayList<>()).add(config);
            }

            // 只保留与chatItems匹配的服装，且每个品类只取一个样本
            List<EmployeeProductConfiguration> relevantConfigs = new ArrayList<>();
            for (com.rutong.platform.wechat.dto.ChatItemDTO chatItem : chatInfo.getChatItems()) {
                String configKey = chatItem.getConfigurationCategory() + "_" + chatItem.getProductModelNumber();
                List<EmployeeProductConfiguration> matchingConfigs = configMap.get(configKey);
                if (matchingConfigs != null && !matchingConfigs.isEmpty()) {
                    // 对于每个ChatItem，只添加第一个匹配的配置
                    relevantConfigs.add(matchingConfigs.get(0));
                }
            }
            
            if (relevantConfigs.isEmpty()) {
                log.warn("未找到匹配的服装信息");
                return ResultBean.error("未找到服装信息", 404);
            }
            
            // 下拉列表 - 包含过滤后的服装
            List<WorkClothesProfileDTO.ClothesSimpleInfo> allClothes = relevantConfigs.stream().map(cfg -> {
                WorkClothesProfileDTO.ClothesSimpleInfo info = new WorkClothesProfileDTO.ClothesSimpleInfo();
                // 确保每个字段都不为null
                info.setClothesCode(cfg.getRfidTagNumber() != null ? cfg.getRfidTagNumber() : "");
                info.setProductSize(cfg.getProductModelNumber() != null ? cfg.getProductModelNumber() : "");
                info.setProductSerialNumber(cfg.getProductSerialNumber() != null ? cfg.getProductSerialNumber() : "");
                info.setEnterpriseName(cfg.getEnterpriseName() != null ? cfg.getEnterpriseName() : "");
                info.setType(cfg.getConfigurationCategory() != null ? cfg.getConfigurationCategory() : "");
                info.setStatus(cfg.getStatus() != null ? cfg.getStatus() : "");
                info.setWashCount(cfg.getWeeklyWashFrequency() != null ? cfg.getWeeklyWashFrequency() : 0);
                
                log.debug("服装信息: clothesCode={}, enterpriseName={}, productSize={}, type={}, status={}", 
                    info.getClothesCode(), info.getEnterpriseName(), info.getProductSize(),
                    info.getType(), info.getStatus());
                return info;
            }).collect(Collectors.toList());
            
            // 默认第一件或指定服装
            EmployeeProductConfiguration selected = relevantConfigs.get(0);
            if (clothesCode != null && !clothesCode.isEmpty()) {
                for (EmployeeProductConfiguration cfg : relevantConfigs) {
                    if (clothesCode.equals(cfg.getRfidTagNumber())) {
                        selected = cfg;
                        break;
                    }
                }
            }
            
            // 设置详细信息
            WorkClothesProfileDTO dto = new WorkClothesProfileDTO();
            dto.setType(selected.getConfigurationCategory()); // 类型
            dto.setClothesCode(selected.getRfidTagNumber()); // 工装编码
            dto.setEnterpriseName(selected.getEnterpriseName()); // 所属客户
            dto.setProductSize(selected.getProductModelNumber()); // 产品款号
            dto.setProductSerialNumber(selected.getProductSerialNumber()); // 产品编号
            
            // 所属员工信息
            dto.setEmployeeName(selected.getEmployeeName());
            dto.setEmployeeId(selected.getEmployeeId());
            dto.setLockerSerialNumber(selected.getLockerSerialNumber());
            
            // 其他信息
            dto.setStatus(selected.getStatus()); // 当前状态
            dto.setWashCount(selected.getWeeklyWashFrequency() != null ? selected.getWeeklyWashFrequency() : 0); // 洗涤次数
            dto.setProtocolStartDate(selected.getProtocolStartDate()); // 启用日期
            dto.setProtocolEndDate(selected.getProtocolEndDate()); // 截止日期
            
            // 设置下拉列表
            dto.setAllClothes(allClothes);
            
            // 确保enterpriseName在所有列表项中都有值
            for (WorkClothesProfileDTO.ClothesSimpleInfo info : allClothes) {
                if (info.getEnterpriseName() == null || info.getEnterpriseName().isEmpty()) {
                    // 如果为空，使用当前选中服装的企业名称
                    info.setEnterpriseName(selected.getEnterpriseName());
                    log.warn("服装编码[{}]的企业名称为空，已使用默认值[{}]", info.getClothesCode(), selected.getEnterpriseName());
                }
            }
            
            log.info("返回员工[{}]的工服档案信息，包含[{}]件服装", employeeId, allClothes.size());
            return ResultBean.success(dto);
            
        } catch (Exception e) {
            // 捕获异常，记录错误日志并返回系统异常信息
            log.error("查询工服档案失败: {}", e.getMessage(), e);
            return ResultBean.error("系统异常，请稍后重试", 500);
        }
    }
} 