package com.rutong.platform.client.service;

import com.rutong.platform.client.entity.DemandNotification;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.entity.ExpenseSettlement;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysUser;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ExpenseSettlementService extends BaseService<ExpenseSettlement> {
//    public List<ExpenseSettlement> selectByContractNumber(String contractNumber) {
//        String sql = "select * from expense_settlement where contract_number= '" + contractNumber + "' order by settlement_end ";
//        return dao.executeSQLQuery(sql, ExpenseSettlement.class);
//    }

    public ExpenseSettlement selectByContractNumber(String contractNumber) {
        dao.findAll(ExpenseSettlement.class,null,null,null);

        String sql = "select create_by from expense_settlement where contract_number = '" + contractNumber + "' order by settlement_end desc limit 1";
        List<ExpenseSettlement> expenseSettlements = dao.executeSQLQuery(sql, ExpenseSettlement.class);
        if(expenseSettlements.size()>0){
            return expenseSettlements.get(0);
        }
        return null;
    }
}
