package com.rutong.platform.sys.entity;

import javax.persistence.Entity;
import javax.persistence.Table;

import org.hibernate.annotations.Formula;

import com.rutong.platform.common.entity.TreeEntity;

@Entity
@Table(name = "sys_dept")
public class SysDept extends TreeEntity{
	/** 部门名称 */
	private String deptname;

	/** 显示顺序 */
	private Integer orderno;
	
	/**
	 * 用于减少数据库查询次数，当hasChildren>0时，才继续查询子数据
	 */
	@Formula("(select count(t.id) from sys_dept t where t.parentid = id)")
	private Integer hasChildren;
	
	public String getDeptname() {
		return deptname;
	}

	public void setDeptname(String deptname) {
		this.deptname = deptname;
	}

	public Integer getOrderno() {
		return orderno;
	}

	public void setOrderno(Integer orderno) {
		this.orderno = orderno;
	}

	@Override
	public Integer getHasChildren() {
		return hasChildren;
	}

}
