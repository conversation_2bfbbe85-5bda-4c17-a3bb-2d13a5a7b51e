package com.rutong.platform.wechat.controller;

import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.Anonymous;
import com.rutong.framework.core.annotation.Log;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.wechat.dto.FeedbackDTO;
import com.rutong.platform.wechat.dto.UserInfoDTO;
import com.rutong.platform.wechat.entity.Feedback;
import com.rutong.platform.wechat.service.FeedbackService;
import com.rutong.platform.wechat.service.UserInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 反馈控制器
 */
@RestController
@RequestMapping("/wechat/feedback")
@LogDesc(title = "意见反馈")
public class FeedbackController {

    //声明静态常量Logger对象
    private static final Logger log = LoggerFactory.getLogger(FeedbackController.class);

    @Autowired
    private FeedbackService feedbackService;
    
    @Autowired
    private UserInfoService userInfoService;

    /**
     * 提交反馈
     * 只需要两个必要字段：反馈内容(content)、联系方式(contactInfo)
     * 其他信息将从当前登录用户中自动获取
     *
     * @param feedbackDTO 反馈信息
     * @return 操作结果
     */
    @PostMapping("/submitFeedback.do")
    @Log(title = "小程序-提交反馈", desc = "小程序用户提交意见反馈")
    //@Anonymous 注解是一个自定义注解，通常用于标识某个方法或类是否允许匿名访问，表示允许没有登录或认证的用户访问
    @Anonymous
    public ResultBean submitFeedback(@RequestBody FeedbackDTO feedbackDTO) {
        try {
            log.info("接收到反馈提交请求: content={}, contactInfo={}", 
                    feedbackDTO.getContent(), 
                    StringUtils.isNotBlank(feedbackDTO.getContactInfo()) ? "已提供" : "未提供");
            
            // 获取当前登录用户信息
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null && loginUser.getUser() != null) {
                String phone = loginUser.getUser().getPhone();
                // 如果SysUser中没有手机号，尝试从LoginUser直接获取
                if (StringUtils.isEmpty(phone)) {
                    phone = loginUser.getPhone();
                }
                
                if (StringUtils.isNotEmpty(phone)) {
                    log.info("从当前登录用户获取到手机号: {}", phone);
                    
                    // 获取用户详细信息
                    UserInfoDTO userInfo = userInfoService.getUserInfoByPhone(phone);
                    if (userInfo != null) {
                        // 设置企业名称、部门、员工姓名和手机号
                        feedbackDTO.setEnterpriseName(userInfo.getEnterpriseName());
                        feedbackDTO.setDepartment(userInfo.getDepartment());
                        feedbackDTO.setEmployeeName(userInfo.getName());
                        feedbackDTO.setEmployeePhone(userInfo.getEmployeePhone());
                        
                        // 如果前端未提供openId，则使用手机号作为标识
                        if (StringUtils.isBlank(feedbackDTO.getOpenId())) {
                            feedbackDTO.setOpenId(phone);
                        }
                        
                        log.info("添加用户信息到反馈: 企业={}, 部门={}, 姓名={}, 电话={}",
                                userInfo.getEnterpriseName(), userInfo.getDepartment(),
                                userInfo.getName(), userInfo.getEmployeePhone());
                    } else {
                        log.warn("未找到手机号为[{}]的用户信息", phone);
                    }
                } else {
                    log.warn("当前登录用户没有手机号信息");
                }
            } else {
                log.warn("未获取到当前登录用户信息");
            }
            
            // 调用feedbackService的saveFeedback方法，保存反馈信息
            boolean success = feedbackService.saveFeedback(feedbackDTO);
            if (success) {
                // 如果保存成功，返回成功信息
                return ResultBean.success("反馈成功！");
            } else {
                // 如果保存失败，返回错误信息
                return ResultBean.error("反馈失败，请稍后再试", 500);
            }
        } catch (Exception e) {
            // 记录错误日志
            log.error("提交反馈失败：", e);
            // 返回错误信息
            return ResultBean.error("系统异常，请稍后再试", 500);
        }
    }

    /**
     * 获取反馈列表
     *
     * @return 所有反馈列表
     */
    @GetMapping("/getFeedbackList.do")
    @Anonymous
    public ResultBean getFeedbackList() {
        try {
            // 调用feedbackService的getAllFeedbacks方法，获取所有反馈列表
            List<Feedback> feedbackList = feedbackService.getAllFeedbacks();
            // 返回成功结果
            return ResultBean.success(feedbackList);
        } catch (Exception e) {
            // 记录错误日志
            log.error("获取反馈列表失败：", e);
            // 返回错误结果
            return ResultBean.error("获取反馈列表失败，请稍后再试", 500);
        }
    }

    /**
     * 获取反馈详情
     *
     * @param id 反馈ID
     * @return 反馈详情
     */
    @GetMapping("/getFeedbackDetail.do/{id}")
    @Anonymous
    public ResultBean getFeedbackDetail(@PathVariable String id) {
        try {
            // 根据反馈ID获取反馈详情
            Feedback feedback = feedbackService.getFeedbackDetail(id);
            // 如果反馈不存在，返回错误信息
            if (feedback == null) {
                return ResultBean.error("反馈不存在", 404);
            }
            
            // 返回反馈详情
            return ResultBean.success(feedback);
        } catch (Exception e) {
            // 记录错误日志
            log.error("获取反馈详情失败：", e);
            // 返回错误信息
            return ResultBean.error("获取反馈详情失败，请稍后再试", 500);
        }
    }

    /**
     * 处理反馈
     * 
     * @param feedbackDTO 处理参数，包含id(反馈ID)和processingResult(处理结果)
     * @return 操作结果
     */
    @PostMapping("/processFeedback.do")
    @Log(title = "处理反馈", desc = "管理员处理用户意见反馈")
    public ResultBean processFeedback(@RequestBody FeedbackDTO feedbackDTO) {
        try {
            // 记录接收到的参数
            log.info("处理反馈接收参数: {}", feedbackDTO);
            
            String id = feedbackDTO.getId();
            // 兼容两种参数名称：processingResult和result
            String result = feedbackDTO.getProcessingResult();
            if (StringUtils.isBlank(result)) {
                result = feedbackDTO.getResult();
            }
            String processor = feedbackDTO.getProcessor(); // 处理人，可选
            // 获取状态，默认为1（已处理）
            String status = feedbackDTO.getStatus();
            if (StringUtils.isBlank(status)) {
                status = "1";
            }
            // 获取处理时间，默认为当前时间
            Date processTime = feedbackDTO.getProcessTime();
            if (processTime == null) {
                processTime = new Date();
            }
            
            // 参数校验
            if (StringUtils.isBlank(id)) {
                log.warn("处理反馈失败: 反馈ID为空");
                return ResultBean.error("处理失败，反馈ID不能为空", 500);
            }
            
            if (StringUtils.isBlank(result)) {
                log.warn("处理反馈失败: 处理结果为空");
                return ResultBean.error("处理失败，处理结果不能为空", 500);
            }
            
            // 调用service处理反馈
            boolean success = feedbackService.processFeedback(id, result, processor, status, processTime);
            
            if (success) {
                log.info("处理反馈成功，ID: {}", id);
                return ResultBean.success("处理成功");
            } else {
                log.warn("处理反馈失败，ID: {}, 原因: 反馈不存在或参数错误", id);
                return ResultBean.error("处理失败，反馈不存在或参数错误", 500);
            }
        } catch (Exception e) {
            // 记录错误日志
            log.error("处理反馈失败：", e);
            // 返回错误信息
            return ResultBean.error("系统异常，请稍后再试", 500);
        }
    }

    /**
     * 按状态获取反馈列表
     *
     * @param status 状态：0-未处理，1-已处理
     * @return 反馈列表
     */
    @GetMapping("/getFeedbackListByStatus.do")
    public ResultBean getFeedbackListByStatus(@RequestParam(required = false) String status) {
        try {
            log.info("按状态查询反馈列表，状态: {}", status);
            
            List<Feedback> feedbackList;
            if (StringUtils.isNotBlank(status)) {
                // 调用feedbackService的方法，按状态获取反馈列表
                feedbackList = feedbackService.getFeedbacksByStatus(status);
            } else {
                // 如果状态为空，则获取所有反馈列表
                feedbackList = feedbackService.getAllFeedbacks();
            }
            
            // 返回成功结果
            return ResultBean.success(feedbackList);
        } catch (Exception e) {
            // 记录错误日志
            log.error("按状态获取反馈列表失败：", e);
            // 返回错误结果
            return ResultBean.error("获取反馈列表失败，请稍后再试", 500);
        }
    }
} 