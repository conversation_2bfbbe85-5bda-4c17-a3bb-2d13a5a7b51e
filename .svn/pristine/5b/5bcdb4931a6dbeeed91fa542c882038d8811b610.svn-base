package com.rutong.platform.wms.service;

import com.rutong.platform.device.entity.DevInCabinet;
import com.rutong.platform.device.entity.DevSorting;
import com.rutong.platform.sys.entity.SysUser;

import com.rutong.platform.washing.entity.WashingRecord;
import com.rutong.platform.wms.entity.WmsDelivery;
import org.springframework.stereotype.Service;

import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class WmsDeliveryDetailService extends BaseService<WmsDeliveryDetail> {

    public List<WmsDeliveryDetail> findByContractNumber(String contractNumber, String type, String date) {
        return dao.findByProperty(WmsDeliveryDetail.class, "contractNumber", contractNumber, "type", type, "batchNo", date, "status", 0);
    }

    public List<WmsDeliveryDetail> findByContractNumberAndProductSerialNumber(String contractNumber, String productSerialNumber, String type) {
        return dao.findByProperty(WmsDeliveryDetail.class, "contractNumber", contractNumber, "productSerialNumber", productSerialNumber, "type", type);
    }

    // 根据订单ID和备注查询
    public List<WmsDeliveryDetail> findByOrderIdAndRemark(String orderId, String type) {
        String sql = "FROM WmsDeliveryDetail WHERE orderId = ?0  and type = ?1";
        return dao.executeQuery(sql, WmsDeliveryDetail.class, orderId, type);
    }


    public List<WmsDeliveryDetail> findByDeliveryId(String delivery_id) {
        String sql = "FROM WmsDeliveryDetail WHERE delivery_id = ?0 ";
        return dao.executeQuery(sql, WmsDeliveryDetail.class, delivery_id);
    }

    public List<WmsDeliveryDetail> findByDeliveryIdAndRemark(String delivery_id, String type) {
        String sql = "FROM WmsDeliveryDetail WHERE delivery_id = ?0 and type = ?1 ";
        return dao.executeQuery(sql, WmsDeliveryDetail.class, delivery_id, type);
    }

    public List<WmsDeliveryDetail> findByRfid(String rfid, String date) {
        return dao.findByProperty(WmsDeliveryDetail.class, "rfidTagNumber", rfid, "batchNo", date);
    }

    public void updateInfo(WmsDeliveryDetail wmsDeliveryDetail) {
        //调用了dao.persist(wmsDeliveryDetail)，
        // 其功能是将传入的WmsDeliveryDetail实体对象持久化到数据库中。
        // persist方法会根据实体的状态决定是插入新记录还是更新已有记录。
        dao.persist(wmsDeliveryDetail);
    }

    public long countByUserAndDate(LoginUser loginUser, Date date) {
        List<FilterCondition> filterConditions = new ArrayList<FilterCondition>();
        FilterCondition filterCondition = new FilterCondition();
        filterCondition.setOperator(FilterCondition.EQ);
        filterCondition.setProperty(BaseEntity.FIELD_OWNER_USER_ID);
        filterCondition.setValue(loginUser.getUserId());
        filterConditions.add(filterCondition);
        FilterCondition filterCondition2 = new FilterCondition();
        filterCondition2.setOperator(FilterCondition.GT);
        filterCondition2.setProperty(BaseEntity.FIELD_CREATE_TIME);
        filterCondition2.setValue(date);
        filterConditions.add(filterCondition2);
        return dao.count(WmsDeliveryDetail.class, filterConditions);
    }

}
