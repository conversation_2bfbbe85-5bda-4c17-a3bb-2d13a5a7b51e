package com.rutong.platform.wms.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsClothingOutbound;
import com.rutong.platform.wms.service.WmsClothingOutboundService;

@RestController
@RequestMapping("/wms/clothingoutbound")
@LogDesc(title = "出库服装")
public class WmsClothingOutboundController extends CrudController<WmsClothingOutbound>{
	
	@Autowired
	private WmsClothingOutboundService clothingOutboundService;

	@Override
	public BaseService<WmsClothingOutbound> getService() {
		return clothingOutboundService;
	}

}
