### mysql的设置
spring:
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
    open-in-view: true
    hibernate:
      ddl-auto: update
    show-sql: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************** 
    username: admin
    password: db_admin@
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      read-only: false
      connection-timeout: 60000
      idle-timeout: 10000
      validation-timeout: 3000
      max-lifetime: 60000
      login-timeout: 5
      maximum-pool-size: 60
      minimum-idle: 10