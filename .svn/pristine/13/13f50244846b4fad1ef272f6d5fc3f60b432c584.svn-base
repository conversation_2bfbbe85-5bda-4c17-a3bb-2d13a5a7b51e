package com.rutong.platform.washing.controller;

import com.alibaba.excel.EasyExcel;
import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.utils.DataAuthUtil;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.washing.entity.WashingRecord;
import com.rutong.platform.washing.entity.WashingTask;
import com.rutong.platform.washing.excel.WashingRecordExcel;
import com.rutong.platform.washing.excel.WashingRecordExcelByDate;
import com.rutong.platform.washing.excel.WashingRecordExcelWithMultipleTimes;
import com.rutong.platform.washing.service.WashingRecordService;
import com.rutong.platform.washing.service.WashingTaskService;
import com.rutong.platform.wms.entity.WmsDelivery;
import com.rutong.platform.wms.service.WmsDeliveryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 洗涤记录控制器
 */
@RestController
@RequestMapping("/washing/record")
@LogDesc(title = "洗涤记录")
public class WashingRecordController extends CrudController<WashingRecord> {

    private static final Logger log = LoggerFactory.getLogger(WashingRecordController.class);

    @Autowired
    private WashingRecordService washingRecordService;

    @Autowired
    private WashingTaskService washingTaskService;

    @Autowired
    private WmsDeliveryService wmsDeliveryService;

    @Override
    public BaseService<WashingRecord> getService() {
        return washingRecordService;
    }

    /**
     * 自定义查询接口，支持按时间范围查询并按照洗涤时间倒序排序
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 查询结果
     */
    @PostMapping("/findByWashingTime.do")
    public ResultBean findByWashingTime(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {

        List<WashingRecord> result;

        if (startTime != null && endTime != null) {
            // 按时间范围查询
            result = washingRecordService.findByWashingTimeBetween(startTime, endTime);
        } else {
            // 查询所有并按时间倒序排序
            result = washingRecordService.findAllOrderByWashingTimeDesc();
        }

        return ResultBean.success(result);
    }

    /**
     * 高级条件查询接口
     * 根据多个条件查询洗涤记录，支持模糊查询和时间范围
     *
     * @param clientName    客户名称
     * @param employeeName  员工姓名
     * @param employeePhone 员工电话
     * @param department    部门
     * @param employeeId    员工工号
     * @param rfidTagNumber RFID标签号
     * @param rfidCode      工作服RFID标签编码
     * @param configType    配置品类
     * @param productModel  产品款号
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param ids           指定ID列表（优先级最高）
     * @return 查询结果
     */
    @PostMapping("/fetchCustomData.do")
    public ResultBean fetchCustomData(
            @RequestParam(required = false) String clientName,
            @RequestParam(required = false) String employeeName,
            @RequestParam(required = false) String employeePhone,
            @RequestParam(required = false) String department,
            @RequestParam(required = false) String employeeId,
            @RequestParam(required = false) String rfidTagNumber,
            @RequestParam(required = false) String rfidCode,
            @RequestParam(required = false) String configType,
            @RequestParam(required = false) String productModel,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam(required = false) String ids) {

        List<WashingRecord> result = new ArrayList<>();

        // 优先根据ID列表查询
        if (StringUtils.isNotBlank(ids)) {
            String[] idArray = ids.split(",");
            for (String id : idArray) {
                WashingRecord record = washingRecordService.findById(id);
                if (record != null) {
                    result.add(record);
                }
            }
        }
        // 条件查询
        else if (StringUtils.isNotBlank(clientName) ||
                StringUtils.isNotBlank(employeeName) ||
                StringUtils.isNotBlank(employeePhone) ||
                StringUtils.isNotBlank(department) ||
                StringUtils.isNotBlank(employeeId) ||
                StringUtils.isNotBlank(rfidTagNumber) ||
                StringUtils.isNotBlank(rfidCode) ||
                StringUtils.isNotBlank(configType) ||
                StringUtils.isNotBlank(productModel) ||
                startTime != null ||
                endTime != null) {

            // 扩展查询方法，添加新的查询条件
            result = washingRecordService.findByMultipleConditions(
                    clientName,
                    employeeName,
                    employeePhone,
                    department,
                    employeeId,
                    rfidTagNumber,
                    rfidCode,
                    configType,
                    productModel,
                    startTime,
                    endTime
            );
        }
        // 无条件，返回所有记录（按洗涤时间倒序）
        else {
            result = washingRecordService.findAllOrderByWashingTimeDesc();
        }

        return ResultBean.success(result);
    }

    /**
     * 重写基础查询方法，确保默认按照洗涤时间倒序排序，并处理查询条件
     */
    @Override
    public PageInfo<WashingRecord> findAllByPage(GridParams gridParams,
                                                 @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
                                                 @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        if (sorts.isEmpty()) {
            SortCondition sortCondition = new SortCondition();
            sortCondition.setDirection(SortCondition.SORT_DESC);
            sortCondition.setProperty("washingTime");
            sorts.add(sortCondition);
        }

        // 日志输出查询条件，便于调试
        log.info("查询条件数量: {}", filters.size());
        for (FilterCondition filter : filters) {
            log.info("查询条件: property={}, operator={}, value={}",
                    filter.getProperty(), filter.getOperator(), filter.getValue());
        }

        // 处理各种查询条件
        for (int i = 0; i < filters.size(); i++) {
            FilterCondition filter = filters.get(i);

            // 处理日期类型的查询条件
            if ((filter.getProperty().equals("washingTime") || filter.getProperty().equals("createTime"))
                    && filter.getValue() instanceof String) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String dateStr = (String) filter.getValue();
                    Date date = sdf.parse(dateStr);
                    filter.setValue(date);
                } catch (ParseException e) {
                    log.error("日期解析错误", e);
                }
            }

            // 确保以下字段可以正确查询
            if (filter.getProperty().equals("employeePhone") ||
                    filter.getProperty().equals("rfidCode") ||
                    filter.getProperty().equals("configType") ||
                    filter.getProperty().equals("productModel") ||
                    filter.getProperty().equals("employeeId")) {

                // 如果是模糊查询，确保值格式正确
                if (filter.getOperator().equals(FilterCondition.LIKE) && filter.getValue() instanceof String) {
                    String value = (String) filter.getValue();
                    if (!value.startsWith("%")) {
                        value = "%" + value;
                    }
                    if (!value.endsWith("%")) {
                        value = value + "%";
                    }
                    filter.setValue(value);
                }
            }

            // 客户名称使用精确匹配
            if (filter.getProperty().equals("clientName")) {
                // 如果是模糊查询操作符，改为精确匹配
                if (filter.getOperator().equals(FilterCondition.LIKE)) {
                    filter.setOperator(FilterCondition.EQ);
                    // 如果值包含了%字符(前端使用了模糊匹配格式)，去掉%
                    if (filter.getValue() instanceof String) {
                        String value = (String) filter.getValue();
                        if (value.startsWith("%") && value.endsWith("%")) {
                            value = value.substring(1, value.length() - 1);
                            filter.setValue(value);
                        }
                    }
                }
            }
        }

        // 增加数据权限
        DataAuthUtil.setDataAuth(filters);

        // 调用服务层查询
        PageInfo<WashingRecord> result = getService().findAllByPage(gridParams, filters, sorts);

        // 记录查询结果
        log.info("查询结果: 总记录数={}", result.getTotal());

        return result;
    }

    /**
     * 从历史客户端收取记录生成洗涤记录
     * 此方法用于补充已有的数据，生成洗涤记录
     *
     * @return 生成结果
     */
    @PostMapping("/generateFromHistory.do")
    public ResultBean generateFromHistory() {
        // 查找所有洗涤任务
        List<WashingTask> allTasks = washingTaskService.findAll();
        int count = 0;

        for (WashingTask task : allTasks) {
            // 查找该任务的所有交接记录
            WmsDelivery query = new WmsDelivery();
            query.setOrderId(task.getId());
            List<WmsDelivery> deliveries = wmsDeliveryService.findAllByObject(query, null);

            // 对每个交接记录生成洗涤记录
            for (WmsDelivery delivery : deliveries) {
                washingRecordService.generateWashingRecordsFromDelivery(delivery);
                count++;
            }
        }
        return ResultBean.success("成功从历史记录生成了" + count + "条洗涤记录");
    }

    /**
     * 更新现有洗涤记录中的员工电话、工作服RFID标签编码、配置品类、产品款号和员工企业工号等字段
     * 通过从employee_product_configuration表中查询这些信息并更新到washing_record表中
     *
     * @return 更新结果
     */
    @PostMapping("/updateMissingFields.do")
    public ResultBean updateMissingFields() {
        log.info("开始更新洗涤记录中缺失的字段信息");
        int count = washingRecordService.updateMissingFieldsFromEmployeeConfig();
        log.info("完成更新洗涤记录中缺失的字段信息，共更新{}条记录", count);
        return ResultBean.success("成功更新了" + count + "条洗涤记录的缺失字段");
    }

    /**
     * 导出洗涤记录为Excel
     * 按工作服RFID标签编码分组，每个RFID编码显示一条记录，包含多个洗涤时间列
     *
     * @param response HTTP响应
     * @param filters  筛选条件列表（与查询列表使用相同参数）
     * @param ids      勾选的记录ID列表，多个ID用逗号分隔
     */
    @GetMapping("/export.do")
    public void exportWashingRecords(
            HttpServletResponse response,
            @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
            @RequestParam(required = false) String ids) throws Exception {

        //声明了一个 List 类型的变量，用于存储 WashingRecord 类型的对象
        //List<WashingRecord> list = new ArrayList<>();
        List<WashingRecord> list;

        // 优先根据勾选的ID导出
        if (StringUtils.isNotBlank(ids)) {
            String[] idArray = ids.split(",");
            list = new ArrayList<>();
            for (String id : idArray) {
                WashingRecord record = washingRecordService.findById(id);
                if (record != null) {
                    list.add(record);
                }
            }
        }
        // 根据筛选条件导出
        else {
            // 处理各种查询条件
            for (int i = 0; i < filters.size(); i++) {
                FilterCondition filter = filters.get(i);

                // 处理日期类型的查询条件
                if ((filter.getProperty().equals("washingTime") || filter.getProperty().equals("createTime"))
                        && filter.getValue() instanceof String) {
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String dateStr = (String) filter.getValue();
                        Date date = sdf.parse(dateStr);
                        filter.setValue(date);
                    } catch (ParseException e) {
                        log.error("日期解析错误", e);
                    }
                }

                // 确保以下字段可以正确查询
                if (filter.getProperty().equals("employeePhone") ||
                        filter.getProperty().equals("rfidCode") ||
                        filter.getProperty().equals("configType") ||
                        filter.getProperty().equals("productModel") ||
                        filter.getProperty().equals("employeeId")) {

                    // 如果是模糊查询，确保值格式正确
                    if (filter.getOperator().equals(FilterCondition.LIKE) && filter.getValue() instanceof String) {
                        String value = (String) filter.getValue();
                        if (!value.startsWith("%")) {
                            value = "%" + value;
                        }
                        if (!value.endsWith("%")) {
                            value = value + "%";
                        }
                        filter.setValue(value);
                    }
                }

                // 客户名称使用精确匹配
                if (filter.getProperty().equals("clientName")) {
                    // 如果是模糊查询操作符，改为精确匹配
                    if (filter.getOperator().equals(FilterCondition.LIKE)) {
                        filter.setOperator(FilterCondition.EQ);
                        // 如果值包含了%字符(前端使用了模糊匹配格式)，去掉%
                        if (filter.getValue() instanceof String) {
                            String value = (String) filter.getValue();
                            if (value.startsWith("%") && value.endsWith("%")) {
                                value = value.substring(1, value.length() - 1);
                                filter.setValue(value);
                            }
                        }
                    }
                }
            }

            // 增加数据权限
            DataAuthUtil.setDataAuth(filters);

            // 根据条件查询数据
            if (filters.isEmpty()) {
                list = washingRecordService.findAllOrderByWashingTimeDesc();
            } else {
                list = washingRecordService.findAll(filters, null);
            }
        }



        //搜集所有洗涤信息，生成表头(使用HashSet不重复表头)，allDates是包含日期字符串的日期集合
        Set<String> allDates = new HashSet<>();

        //创建日期格式化对象,日期列显示格式为"MM-dd"
        //SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd");

        //遍历所有洗涤记录list中的WashingRecord对象，获取所有日期
        for (WashingRecord record : list) {

            //如果日期不为空，则添加到日期集合中
            if (record.getWashingTime() != null) {
                //将日期格式化为字符串
                String dateStr = dateFormat.format(record.getWashingTime());
                //将日期格式化后的字符串添加到日期集合中
                allDates.add(dateStr);
            }
        }
        //日期集合按照日期排序生成一个新的列表，按自然顺序排序的日期字符串
        List<String> sorteDates = allDates.stream().sorted().collect(Collectors.toList());

        //按照工作服rfid标签编码进行分组
        Map<String, List<WashingRecord>> groupsRecords = new HashMap<>();

        //遍历所有洗涤记录list，得到record
            for (WashingRecord record : list) {
                //获取工作服rfid标签编码
                String rfidCode = record.getRfidCode();
                if (rfidCode != null && !rfidCode.trim().isEmpty()) {

                    groupsRecords.computeIfAbsent(rfidCode,k -> new ArrayList<>()).add(record);
                }
            }

            //创建名为excellist列表，用于保存WashingRecordExcelByDate类型的对象
            List<WashingRecordExcelByDate> excellist = new ArrayList<>();

            //初始化了一个名为 index 的整数变量，并将其值设置为 1
            int index = 1;

            //遍历循环groupsRecords中所有的键值对，其中 entry.getKey() 是分组的键，entry.getValue() 是分组的值
            for (Map.Entry<String, List<WashingRecord>> entry : groupsRecords.entrySet()) {
                //获取键值对中的值
                List<WashingRecord> records = entry.getValue();
                if(!records.isEmpty()){
                    //使用第一条记录作为基础信息
                    WashingRecord baseRecord = records.get(0);
                    //创建一个新的excel对象
                    WashingRecordExcelByDate excel = new WashingRecordExcelByDate();

                    excel.setIndex(index++);
                    excel.setClientName(baseRecord.getClientName());
                    excel.setEmployeeName(baseRecord.getEmployeeName());
                    excel.setEmployeePhone(baseRecord.getEmployeePhone());
                    excel.setDepartment(baseRecord.getDepartment());
                    excel.setSubDepartment(baseRecord.getSubDepartment());
                    excel.setConfigType(baseRecord.getConfigType());
                    excel.setProductModel(baseRecord.getProductModel());
                    excel.setProductColor(baseRecord.getProductColor());
                    excel.setLogoPosition(baseRecord.getLogoPosition());
                    excel.setProductSpecification(baseRecord.getProductSpecification());
                    excel.setEmployeeId(baseRecord.getEmployeeId());
                    excel.setRfidCode(baseRecord.getRfidCode());
                    excel.setRfidTagNumber(baseRecord.getRfidTagNumber());
                    excel.setWashCount(baseRecord.getWashCount());

                    //创建洗涤时间Map
                    Map<String, String> dateWashingMap = new HashMap<>();
                    
                    //根据工作服RFID标签编码收集洗涤日期和洗涤次数
                    Map<String, Integer> washingDateCount = new HashMap<>();

                    //遍历洗涤记录
                    for (WashingRecord record : records) {
                        if (record.getWashingTime() != null){
                            String dateStr = dateFormat.format(record.getWashingTime());
                            //统计洗涤次数
                            washingDateCount.put(dateStr, washingDateCount.getOrDefault(dateStr, 0) + 1);
                        }
                    }
                    //为每个日期设置洗涤次数
                    for (String dateStr : sorteDates) {
                        Integer count = washingDateCount.get(dateStr);
                        dateWashingMap.put(dateStr, count == null ? "" : count.toString());
                    }
                    excel.setDateWashingMap(dateWashingMap);
                    //将所有的数据添加到excel中
                    excellist.add(excel);
                    
                }
        }
        log.info("导出洗涤记录总条数：{}", list.size());
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("洗涤记录导出", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        //使用动态表头导出
        EasyExcel.write(response.getOutputStream())
                .head(createDynamicHead(sorteDates))
                .sheet("洗涤记录")
                .doWrite(createDynamicData(excellist,sorteDates));
    }

    /**
     * 根据工作服RFID标签编码查询洗涤次数
     *
     * @param rfidCode 工作服RFID标签编码
     * @return 洗涤次数
     */
    @PostMapping("/countByRfidCode.do")
    public ResultBean countByRfidCode(@RequestParam String rfidCode) {
        int count = washingRecordService.countByRfidCode(rfidCode);
        return ResultBean.success(count);
    }

    /**
     * 根据工作服RFID标签编码查询总的洗涤次数
     * @param rfidCode 工作服标签编码
     * @return 洗涤记录列表
     */
    @PostMapping("/findByRfidCode.do")
    public ResultBean findByRfidCode(@RequestParam String rfidCode) {

        //根据RFID工作服标签编码获取到总洗涤次数
        int totalWashCount = washingRecordService.countByRfidCode(rfidCode);

        //根据RFID工作服标签编码获取到每条洗涤记录
        List<WashingRecord> washingRecords = washingRecordService.findByRfidCode(rfidCode);

        //更新每条洗涤记录的洗涤次数为总洗涤次数
        for (WashingRecord washingRecord : washingRecords) {
            washingRecord.setWashCount(totalWashCount);
        }
        return ResultBean.success(totalWashCount);
    }


    /**
     * 更新所有洗涤记录的洗涤次数
     * @return 更新结果
     */
    @PostMapping("/updateAllWashCount.do")
    public ResultBean updateAllWashCount(){
        log.info("开始更新所有洗涤记录的洗涤次数");
        int count = washingRecordService.updateAllWashCount();
        log.info("完成更新所有洗涤记录的洗涤次数，共更新{}条记录",count);
        return ResultBean.success("成功更新了" + count + "条洗涤记录的洗涤次数");
    }

    /**
     * 根据洗涤时间和客户名称统计工作服品类及其洗涤次数
     *
     * @param washingTime 洗涤时间
     * @param contractNumber 合同编号
     * @return 工作服品类及其洗涤次数的统计结果
     */
    @PostMapping("/countByWorkWearConfigurationCategory.do")
    public ResultBean countByWorkWearConfigurationCategory1(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date washingTime,
                                                            @RequestParam(required = false) String contractNumber) {
        // 调用服务层方法统计工作服品类及其洗涤次数
        Map<String,Integer> stastistics = washingRecordService.countByWashingTimeAndContractNumber(washingTime, contractNumber);

        // 返回成功结果
        return ResultBean.success(stastistics);
    }

    /**
     * 动态表头
     * @param dates 日期列表
     * @return 表头列表
     */
    public List<List<String>> createDynamicHead(List<String> dates) {
        List<List<String>> head = new ArrayList<>();
        //固定列
        head.add(Collections.singletonList("序号"));
        head.add(Collections.singletonList("客户名称"));
        head.add(Collections.singletonList("员工姓名"));
        head.add(Collections.singletonList("员工电话"));
        head.add(Collections.singletonList("部门"));
        head.add(Collections.singletonList("分部门"));
        head.add(Collections.singletonList("配置品类"));
        head.add(Collections.singletonList("产品款号"));
        head.add(Collections.singletonList("产品颜色"));
        head.add(Collections.singletonList("LOGO位置"));
        head.add(Collections.singletonList("产品规格"));
        head.add(Collections.singletonList("员工工号"));
        head.add(Collections.singletonList("工作服RFID标签编码"));
        head.add(Collections.singletonList("RFID标签号"));
        head.add(Collections.singletonList("已洗涤次数"));
        //遍历所有日期，将日期集合dates中的每一个date日期添加到head中
        for (String date : dates) {
            head.add(createHead(date));
        }
        return head;
    }

    /**
     * 创建动态日期
     * @param excelList
     * @param dates
     * @return
     */
    private List<List<Object>> createDynamicData(List<WashingRecordExcelByDate> excelList,List<String> dates) {
        List<List<Object>> data = new ArrayList<>();
        for (WashingRecordExcelByDate excel : excelList) {
            List<Object> row = new ArrayList<>();
            //固定列数据
            row.add(excel.getIndex());
            row.add(excel.getClientName());
            row.add(excel.getEmployeeName());
            row.add(excel.getEmployeePhone());
            row.add(excel.getDepartment());
            row.add(excel.getSubDepartment());
            row.add(excel.getConfigType());
            row.add(excel.getProductModel());
            row.add(excel.getProductColor());
            row.add(excel.getLogoPosition());
            row.add(excel.getProductSpecification());
            row.add(excel.getEmployeeId());
            row.add(excel.getRfidCode());
            row.add(excel.getRfidTagNumber());
            row.add(excel.getWashCount());
            //动态日期列数据
            for (String date : dates) {
                row.add(excel.getDateWashing(date));
            }
            data.add(row);
        }
        return data;
    }

    /**
     * 创建表头单元格
     * @param title
     * @return
     */
    private List<String> createHead(String title) {
        List<String> head = new ArrayList<>();
        head.add(title);
        return head;
    }
}

