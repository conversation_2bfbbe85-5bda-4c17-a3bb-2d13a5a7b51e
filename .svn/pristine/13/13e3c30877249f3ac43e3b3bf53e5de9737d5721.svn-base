package com.rutong.platform.app.service;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import com.rutong.framework.constant.Constants;
import com.rutong.framework.core.exception.ServiceException;
import com.rutong.framework.core.exception.user.UserPasswordNotMatchException;
import com.rutong.framework.manager.AsyncManager;
import com.rutong.framework.manager.factory.AsyncFactory;
import com.rutong.framework.security.context.AuthenticationContextHolder;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.security.service.TokenService;
import com.rutong.framework.utils.MessageUtils;
import com.rutong.platform.wms.entity.WmsCar;
import com.rutong.platform.wms.entity.WmsCarDetail;
import com.rutong.platform.wms.service.WmsCarDetailService;
import com.rutong.platform.wms.service.WmsCarService;

@Service
public class AppService {
	@Autowired
	private TokenService tokenService;
	@Resource
	private AuthenticationManager authenticationManager;
	@Autowired
	private WmsCarDetailService carDetailService;
	@Autowired
	private WmsCarService carService;
	
	public String login(String username, String password) {
		Authentication authentication = null;
		Authentication authenticationToken = new UsernamePasswordAuthenticationToken(username,password);
		try {
			AuthenticationContextHolder.setContext(authenticationToken);
			// 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
			authentication = authenticationManager.authenticate(authenticationToken);
		} catch (Exception e) {
			e.printStackTrace();
			if (e instanceof BadCredentialsException) {
				AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL,
						MessageUtils.message("user.password.not.match")));
				throw new UserPasswordNotMatchException();
			} else {
				AsyncManager.me()
						.execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
				throw new ServiceException(e.getMessage());
			}
		} finally {
			AuthenticationContextHolder.clearContext();
		}
		LoginUser loginUser = (LoginUser) authentication.getPrincipal();
		return tokenService.createToken(loginUser);
	}

	public void saveCarinfo(String carno, String result) {
		WmsCarDetail wmsCarDetail = new WmsCarDetail();
		wmsCarDetail.setCarNo(carno);
		wmsCarDetail.setResult(result);
		carDetailService.save(wmsCarDetail);
		
		WmsCar wmsCar = carService.findByCarno(carno);
		if(wmsCar != null) {
			wmsCar.setStatus("异常");
			carService.update(wmsCar);
		}
	}

	public List<String> getCarList() {
		List<WmsCar> carList = carService.findAll();
		return carList.stream().map(c->c.getCarNo()).collect(Collectors.toList());
	}

}
