package com.rutong.platform.washing.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 20205/08/04
 * 洗涤记录Excel
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WashingRecordExcelByDate {
    
    @ExcelProperty(value = "序号", index = 0)
    private Integer index;
    
    @ExcelProperty(value = "客户名称", index = 1)
    private String clientName;
    
    @ExcelProperty(value = "员工姓名", index = 2)
    private String employeeName;

    @ExcelProperty(value = "员工电话", index = 3)
    private String employeePhone;
    
    @ExcelProperty(value = "部门", index = 4)
    private String department;
    
    @ExcelProperty(value = "分部门", index = 5)
    private String subDepartment;

    @ExcelProperty(value = "工作服品类", index = 6)
    private String workWearConfigurationCategory;
    
    @ExcelProperty(value = "配置品类", index = 7)
    private String configType;
    
    @ExcelProperty(value = "产品款号", index = 8)
    private String productModel;
    
    @ExcelProperty(value = "产品颜色", index = 9)
    private String productColor;
    
    @ExcelProperty(value = "LOGO位置", index = 10)
    private String logoPosition;
    
    @ExcelProperty(value = "产品规格", index = 11)
    private String productSpecification;
    
    @ExcelProperty(value = "员工工号", index = 12)
    private String employeeId;
    
    @ExcelProperty(value = "工作服RFID标签编码", index = 13)
    private String rfidCode;
    
    @ExcelProperty(value = "RFID标签号", index = 14)
    private String rfidTagNumber;
    
    @ExcelProperty(value = "已洗涤次数", index = 15)
    private Integer washCount;

    //动态时间列，储存每个日期的洗涤记录
    //使用Map存储，key为时间，value为该时间对应的洗涤记录
    private Map<String, String> dateWashingMap;


    /**
     * 获取指定日期的洗涤情况
     * 从dateWashingMap中获取对应的清洗后的日期字符串。
     * 如果dateWashingMap中不存在对应的日期字符串，则返回一个空字符串
     * @param dateStr 日期字符串
     */
    public String getDateWashing(String dateStr) {
        if (dateWashingMap != null) {
            String result = dateWashingMap.get(dateStr);
            if (result != null) {
                return result;
            }
        }
        return "";
    }
} 