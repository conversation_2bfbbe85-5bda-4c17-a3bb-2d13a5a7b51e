package com.rutong.platform.client.service;

import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.common.service.BaseService;
import org.springframework.stereotype.Service;

@Service
public class EmployeeProductConfigurationService extends BaseService<EmployeeProductConfiguration> {

	public EmployeeProductConfiguration byRfid(String rfid) {
		return dao.findByPropertyFirst(EmployeeProductConfiguration.class,
				EmployeeProductConfiguration.FIELD_RFID_TAG_NUMBER, rfid);
	}

}
