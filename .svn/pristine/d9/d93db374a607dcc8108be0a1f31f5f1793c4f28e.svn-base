package com.rutong.platform.device.service;

import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.device.entity.DevRecordSorting;
import com.rutong.platform.device.entity.DevSorting;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class DevRecordSortingService extends BaseService<DevRecordSorting> {

    public void saveInfo(DevRecordSorting recordSorting) {
        recordSorting.setCreateTime(new Date());
        dao.persist(recordSorting);
    }

    public DevRecordSorting byRfid(String rfid) {
        return dao.executeQueryFirst("FROM DevRecordSorting WHERE rfidTagNumber = ?0 order by sortingTime desc", DevRecordSorting.class, rfid);
    }
}
