package com.rutong.platform.client.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rutong.framework.bean.ResultBean;
import com.rutong.platform.client.entity.ClauseInfo;
import com.rutong.platform.client.entity.ClauseProductConfig;
import com.rutong.platform.client.entity.ClientInfo;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.ClauseInfoService;
import com.rutong.platform.client.service.ClauseProductConfigService;
import com.rutong.platform.client.service.ClientInfoService;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/client/clientInfo")
public class ClientInfoController extends CrudController<ClientInfo> {

    @Autowired
    private ClientInfoService clientService;
    @Autowired
    private ClauseInfoService clauseInfoService;
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;

    @Autowired
    private ClauseProductConfigService clauseProductConfigService;

    @Override
    public BaseService<ClientInfo> getService() {
        return clientService;
    }


    @Override
    public ResultBean save(ClientInfo entity) {
        entity.setStatus(0L);
        entity.setContractNumber("SC" + System.currentTimeMillis());
        clientService.save(entity);
        ClauseInfo clauseInfo = JSONObject.parseObject(entity.getClauseInfoJson(), ClauseInfo.class);
        clauseInfo.setContractNumber(entity.getContractNumber());
        clauseInfoService.save(clauseInfo);
        List<ClauseProductConfig> clauseProductConfigList = clauseInfo.getClauseProductConfigList();
        for (ClauseProductConfig clauseProductConfig : clauseProductConfigList) {
            clauseProductConfig.setContractNumber(entity.getContractNumber());
            clauseProductConfigService.save(clauseProductConfig);
        }
        JSONObject jsonObject = JSONObject.parseObject(entity.getProductDataJson());
        List<EmployeeProductConfiguration> employeeProductConfigurationList = JSONArray.parseArray(jsonObject.getString("productConfigurations"), EmployeeProductConfiguration.class);
        for (EmployeeProductConfiguration employeeProductConfiguration : employeeProductConfigurationList) {
            employeeProductConfiguration.setContractNumber(entity.getContractNumber());
            employeeProductConfiguration.setEnterpriseName(entity.getEnterpriseName());
            employeeProductConfigurationService.save(employeeProductConfiguration);
        }

        return ResultBean.success(entity);
    }
}
