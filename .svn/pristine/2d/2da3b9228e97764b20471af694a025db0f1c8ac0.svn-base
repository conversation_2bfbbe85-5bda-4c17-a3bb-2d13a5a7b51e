package com.rutong.platform.wechat.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 沟通事项项目DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatItemDTO {
    
    // 状态常量
    public static final String STATUS_MISSING = "缺失";
    public static final String STATUS_MISPLACED = "放错";
    public static final String STATUS_DAMAGED = "破损";
    public static final String STATUS_STAINED = "污渍";
    public static final String STATUS_LABEL_DAMAGED = "标签破损";
    
    // 全部可选状态
    public static final List<String> ALL_STATUSES = Arrays.asList(
        STATUS_MISSING, STATUS_MISPLACED, STATUS_DAMAGED, STATUS_STAINED, STATUS_LABEL_DAMAGED
    );
    private String id;
    /**
     * 配置品类
     */
    private String configurationCategory;
    
    /**
     * 产品款号
     */
    private String productModelNumber;
    
    /**
     * 状态枚举: 缺失、放错、破损、污渍、标签破损
     */
    private String status;
    
    /**
     * 可选状态列表
     */
    private List<String> availableStatuses;
    
    /**
     * 已选状态列表
     */
    private List<String> selectedStatuses;
} 