package com.rutong.platform.washing.controller;

import com.alibaba.excel.EasyExcel;
import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.utils.DataAuthUtil;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.washing.entity.WashingRecord;
import com.rutong.platform.washing.entity.WashingTask;
import com.rutong.platform.washing.excel.WashingRecordExcel;
import com.rutong.platform.washing.excel.WashingRecordExcelByDate;
import com.rutong.platform.washing.excel.WashingRecordExcelWithMultipleTimes;
import com.rutong.platform.washing.service.WashingRecordService;
import com.rutong.platform.washing.service.WashingTaskService;
import com.rutong.platform.wms.entity.WmsDelivery;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import com.rutong.platform.wms.service.WmsDeliveryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 洗涤记录控制器
 */
@RestController
@RequestMapping("/washing/record")
@LogDesc(title = "洗涤记录")
public class WashingRecordController extends CrudController<WashingRecord> {

    private static final Logger log = LoggerFactory.getLogger(WashingRecordController.class);

    @Autowired
    private WashingRecordService washingRecordService;

    @Autowired
    private WashingTaskService washingTaskService;

    @Autowired
    private WmsDeliveryService wmsDeliveryService;
    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;

    @Override
    public BaseService<WashingRecord> getService() {
        return washingRecordService;
    }

    /**
     * 按时间范围查询并按照洗涤时间倒序排序
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 查询结果
     */
    @PostMapping("/findByWashingTime.do")
    public ResultBean findByWashingTime(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {

        List<WashingRecord> result;

        if (startTime != null && endTime != null) {
            // 按时间范围查询
            result = washingRecordService.findByWashingTimeBetween(startTime, endTime);
        } else {
            // 查询所有并按时间倒序排序
            result = washingRecordService.findAllOrderByWashingTimeDesc();
        }

        return ResultBean.success(result);
    }

    /**
     * 高级条件查询接口
     * 根据多个条件查询洗涤记录，支持模糊查询和时间范围
     *
     * @param clientName    客户名称
     * @param employeeName  员工姓名
     * @param employeePhone 员工电话
     * @param department    部门
     * @param employeeId    员工工号
     * @param rfidTagNumber RFID标签号
     * @param rfidCode      工作服RFID标签编码
     * @param configType    配置品类
     * @param productModel  产品款号
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param ids           指定ID列表（优先级最高）
     * @return 查询结果
     */
    @PostMapping("/fetchCustomData.do")
    public ResultBean fetchCustomData(
            @RequestParam(required = false) String clientName,
            @RequestParam(required = false) String employeeName,
            @RequestParam(required = false) String employeePhone,
            @RequestParam(required = false) String department,
            @RequestParam(required = false) String employeeId,
            @RequestParam(required = false) String rfidTagNumber,
            @RequestParam(required = false) String rfidCode,
            @RequestParam(required = false) String configType,
            @RequestParam(required = false) String productModel,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam(required = false) String ids) {

        List<WashingRecord> result = new ArrayList<>();

        // 优先根据ID列表查询
        if (StringUtils.isNotBlank(ids)) {
            String[] idArray = ids.split(",");
            for (String id : idArray) {
                WashingRecord record = washingRecordService.findById(id);
                if (record != null) {
                    result.add(record);
                }
            }
        }
        // 条件查询
        else if (StringUtils.isNotBlank(clientName) ||
                StringUtils.isNotBlank(employeeName) ||
                StringUtils.isNotBlank(employeePhone) ||
                StringUtils.isNotBlank(department) ||
                StringUtils.isNotBlank(employeeId) ||
                StringUtils.isNotBlank(rfidTagNumber) ||
                StringUtils.isNotBlank(rfidCode) ||
                StringUtils.isNotBlank(configType) ||
                StringUtils.isNotBlank(productModel) ||
                startTime != null ||
                endTime != null) {

            // 扩展查询方法，添加新的查询条件
            result = washingRecordService.findByMultipleConditions(
                    clientName,
                    employeeName,
                    employeePhone,
                    department,
                    employeeId,
                    rfidTagNumber,
                    rfidCode,
                    configType,
                    productModel,
                    startTime,
                    endTime
            );
        }
        // 无条件，返回所有记录（按洗涤时间倒序）
        else {
            result = washingRecordService.findAllOrderByWashingTimeDesc();
        }

        return ResultBean.success(result);
    }

    /**
     * 根据rfidTagNumberWork查询洗涤记录（分组）返回所有字段内容并包含每组的计数
     */
    @PostMapping("/fetchGroup.do")
    public PageInfo<WmsDeliveryDetail> fetchGroup(GridParams gridParams,
                                                  @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
                                                  @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        FilterCondition filterCondition = new FilterCondition();
        filterCondition.setOperator("eq");
        filterCondition.setProperty("type");
        filterCondition.setValue("脏衣清点机001");
        filters.add(filterCondition);
        return wmsDeliveryDetailService.findAllByPageGroupWithCount(gridParams, filters, sorts);
    }

    /**
     * 根据rfidTagNumberWork查询洗涤记录（非分组）
     */
    @PostMapping("/fetchList.do")
    public PageInfo<WmsDeliveryDetail> fetchList(GridParams gridParams,
                                                 @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
                                                 @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        if (filters == null) {
            filters = new ArrayList<>();
        }
        FilterCondition typeFilter = new FilterCondition();
        typeFilter.setOperator("eq");
        typeFilter.setProperty("type");
        typeFilter.setValue("脏衣清点机001");
        filters.add(typeFilter);

        return wmsDeliveryDetailService.findAllByPage(gridParams, filters, sorts);
    }

    /**
     * 从脏衣清点机001记录生成洗涤记录
     *
     * @return 生成结果
     */
    @PostMapping("/generateFromHistory.do")
    public ResultBean generateFromHistory() {
        // 查找所有洗涤任务
        List<WashingTask> allTasks = washingTaskService.findAll();
        int count = 0;

        for (WashingTask task : allTasks) {
            // 查找该任务的所有交接记录
            WmsDelivery query = new WmsDelivery();
            query.setOrderId(task.getId());
            List<WmsDelivery> deliveries = wmsDeliveryService.findAllByObject(query, null);

            // 对每个交接记录生成洗涤记录
            for (WmsDelivery delivery : deliveries) {
                washingRecordService.generateWashingRecordsFromDelivery(delivery);
                count++;
            }
        }
        return ResultBean.success("成功从历史记录生成了" + count + "条洗涤记录");
    }



    /**
     * 更新现有洗涤记录中的员工电话、工作服RFID标签编码、配置品类、产品款号和员工企业工号等字段
     * 通过从employee_product_configuration表中查询这些信息并更新到washing_record表中
     *
     * @return 更新结果
     */
    @PostMapping("/updateMissingFields.do")
    public ResultBean updateMissingFields() {
        log.info("开始更新洗涤记录中缺失的字段信息");
        int count = washingRecordService.updateMissingFieldsFromEmployeeConfig();
        log.info("完成更新洗涤记录中缺失的字段信息，共更新{}条记录", count);
        return ResultBean.success("成功更新了" + count + "条洗涤记录的缺失字段");
    }

    /**
     * 导出洗涤记录为Excel
     * 按工作服RFID标签编码分组，每个RFID编码显示一条记录，包含多个洗涤时间列
     *
     * @param response HTTP响应
     * @param filters  筛选条件列表（与查询列表使用相同参数）
     * @param ids      勾选的记录ID列表，多个ID用逗号分隔
     */
    @GetMapping("/export.do")
    public void exportWashingRecords(
            HttpServletResponse response,
            @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
            @RequestParam(required = false) String ids) throws Exception {
        List<WmsDeliveryDetail> list;

        // 根据筛选条件导出
        // 复制并映射过滤条件到wms_delivery_detail字段（与列表查询逻辑一致）
        List<FilterCondition> mappedFilters = new ArrayList<>();
        for (FilterCondition filter : filters) {
            FilterCondition mapped = new FilterCondition();
            mapped.setOperator(filter.getOperator());
            mapped.setValue(filter.getValue());

            String prop = filter.getProperty();
            // 处理日期类型：将字符串转Date
            if (("washingTime".equals(prop) || "createTime".equals(prop)) && filter.getValue() instanceof String) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date date = sdf.parse((String) filter.getValue());
                    mapped.setValue(date);
                } catch (ParseException e) {
                    log.error("日期解析错误", e);
                }
            }

            // 模糊查询值处理
            if (("employeePhone".equals(prop) || "rfidCode".equals(prop)
                    || "configType".equals(prop) || "productModel".equals(prop)
                    || "employeeId".equals(prop))
                    && FilterCondition.LIKE.equals(filter.getOperator())
                    && filter.getValue() instanceof String) {
                String value = (String) filter.getValue();
                if (!value.startsWith("%")) {
                    value = "%" + value;
                }
                if (!value.endsWith("%")) {
                    value = value + "%";
                }
                mapped.setValue(value);
            }

            // 客户名称改为精确匹配
            if ("clientName".equals(prop) && FilterCondition.LIKE.equals(filter.getOperator()) && filter.getValue() instanceof String) {
                mapped.setOperator(FilterCondition.EQ);
                String value = (String) filter.getValue();
                if (value.startsWith("%") && value.endsWith("%")) {
                    value = value.substring(1, value.length() - 1);
                }
                mapped.setValue(value);
            }

            // 字段名映射
            String mappedProp = mapPropertyName(prop);
            mapped.setProperty(mappedProp);
            mappedFilters.add(mapped);
        }

        // 强制限定来源为"脏衣清点机001"
        FilterCondition typeFilter = new FilterCondition();
        typeFilter.setProperty("type");
        typeFilter.setOperator(FilterCondition.EQ);
        typeFilter.setValue(com.rutong.framework.constant.GlobalContsant.CLOTHING_WASH_TYPE_7);
        mappedFilters.add(typeFilter);

        // 增加数据权限
        DataAuthUtil.setDataAuth(mappedFilters);

        // 根据条件查询数据
        if (mappedFilters.isEmpty()) {
            // 如果没有其他过滤条件，只查询type为"脏衣清点机001"的记录
            List<FilterCondition> typeOnlyFilters = new ArrayList<>();
            FilterCondition typeOnlyFilter = new FilterCondition();
            typeOnlyFilter.setProperty("type");
            typeOnlyFilter.setOperator(FilterCondition.EQ);
            typeOnlyFilter.setValue(com.rutong.framework.constant.GlobalContsant.CLOTHING_WASH_TYPE_7);
            typeOnlyFilters.add(typeOnlyFilter);
            list = wmsDeliveryDetailService.findAll(typeOnlyFilters, null);
        } else {
            list = wmsDeliveryDetailService.findAll(mappedFilters, null);
        }

        //搜集所有洗涤信息，生成表头(使用HashSet不重复表头)，allDates是包含日期字符串的日期集合
        Set<String> allDates = new HashSet<>();

        //创建日期格式化对象,日期列显示格式为"MM-dd"
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd");

        //遍历所有洗涤记录list中的WmsDeliveryDetail对象，获取所有日期
        for (WmsDeliveryDetail record : list) {
            //如果日期不为空，则添加到日期集合中
            if (record.getCreateTime() != null) {
                //将日期格式化为字符串
                String dateStr = dateFormat.format(record.getCreateTime());
                //将日期格式化后的字符串添加到日期集合中
                allDates.add(dateStr);
            }
        }
        //日期集合按照日期排序生成一个新的列表，按自然顺序排序的日期字符串
        List<String> sorteDates = allDates.stream().sorted().collect(Collectors.toList());

        //按工作服rfid标签编码进行分组
        Map<String, List<WmsDeliveryDetail>> groupsRecords = new HashMap<>();

        //遍历所有洗涤记录list，得到record
        for (WmsDeliveryDetail record : list) {
            //获取工作服rfid标签编码
            String rfidCode = record.getRfidTagNumberWork();
            if (rfidCode != null && !rfidCode.trim().isEmpty()) {
                groupsRecords.computeIfAbsent(rfidCode, k -> new ArrayList<>()).add(record);
            }
        }

        //创建名为excellist列表，用于保存WashingRecordExcelByDate类型的对象
        List<WashingRecordExcelByDate> excellist = new ArrayList<>();

        //初始化了一个名为 index 的整数变量，并将其值设置为 1
        int index = 1;

        //遍历循环groupsRecords中所有的键值对，其中 entry.getKey() 是分组的键，entry.getValue() 是分组的值
        for (Map.Entry<String, List<WmsDeliveryDetail>> entry : groupsRecords.entrySet()) {
            //获取键值对中的值
            List<WmsDeliveryDetail> records = entry.getValue();
            if (!records.isEmpty()) {
                //使用第一条记录作为基础信息
                WmsDeliveryDetail baseRecord = records.get(0);
                //创建一个新的excel对象
                WashingRecordExcelByDate excel = convertToWashingRecordExcel(baseRecord, index++);

                //创建洗涤时间Map
                Map<String, String> dateWashingMap = new HashMap<>();
                //根据工作服RFID标签编码收集洗涤日期和洗涤次数
                Map<String, Integer> washingDateCount = new HashMap<>();
                //遍历洗涤记录
                for (WmsDeliveryDetail record : records) {
                    if (record.getCreateTime() != null) {
                        String dateStr = dateFormat.format(record.getCreateTime());
                        //统计洗涤次数
                        washingDateCount.put(dateStr, washingDateCount.getOrDefault(dateStr, 0) + 1);
                    }
                }
                //为每个日期设置洗涤次数
                for (String dateStr : sorteDates) {
                    Integer count = washingDateCount.get(dateStr);
                    dateWashingMap.put(dateStr, count == null ? "" : count.toString());
                }
                excel.setDateWashingMap(dateWashingMap);//设置所有日期的洗涤次数
                //将所有的数据添加到excel中
                excellist.add(excel);
            }
        }
        log.info("导出洗涤记录总条数：{}", list.size());
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("洗涤记录导出", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        //使用动态表头导出
        EasyExcel.write(response.getOutputStream())
                .head(createDynamicHead(sorteDates))
                .sheet("洗涤记录")
                .doWrite(createDynamicData(excellist, sorteDates));
    }

    /**
     * 根据工作服RFID标签编码查询洗涤次数
     *
     * @param rfidCode 工作服RFID标签编码
     * @return 洗涤次数
     */
    @PostMapping("/countByRfidCode.do")
    public ResultBean countByRfidCode(@RequestParam String rfidCode) {
        int count = washingRecordService.countByRfidCode(rfidCode);
        return ResultBean.success(count);
    }

    /**
     * 更新所有洗涤记录的洗涤次数
     *
     * @return 更新结果
     */
    @PostMapping("/updateAllWashCount.do")
    public ResultBean updateAllWashCount() {
        log.info("开始更新所有洗涤记录的洗涤次数");
        int count = washingRecordService.updateAllWashCount();
        log.info("完成更新所有洗涤记录的洗涤次数，共更新{}条记录", count);
        return ResultBean.success("成功更新了" + count + "条洗涤记录的洗涤次数");
    }

    /**
     * 根据洗涤时间点和客户名称统计工作服品类及其洗涤次数
     *
     * @param washingTime    洗涤时间
     * @param contractNumber 合同编号
     * @return 工作服品类及其洗涤次数的统计结果
     */
    @PostMapping("/countByWorkWearConfigurationCategory.do")
    public ResultBean countByWorkWearConfigurationCategory1(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date washingTime,
                                                            @RequestParam(required = false) String contractNumber) {
        // 调用服务层方法统计工作服品类及其洗涤次数
        Map<String, Integer> stastistics = washingRecordService.countByWashingTimeAndContractNumber(washingTime, contractNumber);

        // 返回成功结果
        return ResultBean.success(stastistics);
    }

    /**
     * 根据时间范围统计工作服品类及其洗涤次数
     *
     * @param startTime
     * @param endTime
     * @param contractNumber
     * @return
     */
    @PostMapping("/countByWorkWearConfigurationCategoryRange.do")
    public ResultBean countByWorkWearConfigurationCategoryRange(@DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                                                @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
                                                                String contractNumber) {
        //调用服务层方法根据时间范围统计工作服品类及其洗涤次数
        Map<String, Integer> statistics = washingRecordService.countByWorkWearConfigurationCategoryRange(startTime, endTime, contractNumber);
        if (statistics == null) {
            statistics = new HashMap<>();
        }
        return ResultBean.success(statistics);
    }


    /**
     * 动态表头
     *
     * @param dates 日期列表
     * @return 表头列表
     */
    public List<List<String>> createDynamicHead(List<String> dates) {
        List<List<String>> head = new ArrayList<>();
        //固定列
        head.add(Collections.singletonList("序号"));
        head.add(Collections.singletonList("客户名称"));
        head.add(Collections.singletonList("员工姓名"));
        head.add(Collections.singletonList("员工电话"));
        head.add(Collections.singletonList("部门"));
        head.add(Collections.singletonList("分部门"));
        head.add(Collections.singletonList("配置品类"));
        head.add(Collections.singletonList("产品款号"));
        head.add(Collections.singletonList("产品颜色"));
        head.add(Collections.singletonList("LOGO位置"));
        head.add(Collections.singletonList("产品规格"));
        head.add(Collections.singletonList("员工工号"));
        head.add(Collections.singletonList("工作服RFID标签编码"));
        head.add(Collections.singletonList("RFID标签号"));
        head.add(Collections.singletonList("已洗涤次数"));
        //遍历所有日期，将日期集合dates中的每一个date日期添加到head中
        for (String date : dates) {
            head.add(createHead(date));
        }
        return head;
    }

    /**
     * 创建动态日期
     *
     * @param excelList
     * @param dates
     * @return
     */
    private List<List<Object>> createDynamicData(List<WashingRecordExcelByDate> excelList, List<String> dates) {
        List<List<Object>> data = new ArrayList<>();
        for (WashingRecordExcelByDate excel : excelList) {
            List<Object> row = new ArrayList<>();
            //固定列数据
            row.add(excel.getIndex());
            row.add(excel.getClientName());
            row.add(excel.getEmployeeName());
            row.add(excel.getEmployeePhone());
            row.add(excel.getDepartment());
            row.add(excel.getSubDepartment());
            row.add(excel.getConfigType());
            row.add(excel.getProductModel());
            row.add(excel.getProductColor());
            row.add(excel.getLogoPosition());
            row.add(excel.getProductSpecification());
            row.add(excel.getEmployeeId());
            row.add(excel.getRfidCode());
            row.add(excel.getRfidTagNumber());
            row.add(excel.getWashCount());
            //动态日期列数据
            for (String date : dates) {
                row.add(excel.getDateWashing(date));
            }
            data.add(row);
        }
        return data;
    }

    /**
     * 创建表头单元格
     *
     * @param title
     * @return
     */
    private List<String> createHead(String title) {
        List<String> head = new ArrayList<>();
        head.add(title);
        return head;
    }

    /**
     * 将WmsDeliveryDetail转换为WashingRecord
     *
     * @param detail      WmsDeliveryDetail对象
     * @param washingTime 洗涤时间
     * @return WashingRecord对象
     */
    private WashingRecord convertToWashingRecord(WmsDeliveryDetail detail, Date washingTime) {
        WashingRecord rec = new WashingRecord();
        rec.setId(detail.getId());
        rec.setClientName(detail.getEnterpriseName());
        rec.setContractNumber(detail.getContractNumber());
        rec.setEmployeeName(detail.getEmployeeName());
        rec.setWashingTime(washingTime);
        rec.setEmployeePhone(detail.getEmployeePhone());
        rec.setDepartment(detail.getDepartment());
        rec.setSubDepartment(detail.getSubDepartment());
        rec.setConfigType(detail.getConfigurationCategory());
        rec.setProductModel(detail.getProductModelNumber());
        rec.setProductColor(detail.getProductColor());
        rec.setLogoPosition(detail.getLogoPosition());
        rec.setProductSpecification(detail.getProductSpecification());
        rec.setEmployeeId(detail.getEmployeeId());
        rec.setRfidCode(detail.getRfidTagNumberWork());
        rec.setRfidTagNumber(detail.getRfidTagNumber());
        // 计算已洗涤次数,统计 washing_record 中该RFID的总次数
        if (detail.getRfidTagNumberWork() != null && !detail.getRfidTagNumberWork().trim().isEmpty()) {
            rec.setWashCount(washingRecordService.countByRfidCode(detail.getRfidTagNumberWork()));
        } else {
            rec.setWashCount(0);
        }
        rec.setWorkWearConfigurationCategory(detail.getWorkWearConfigurationCategory());
        return rec;
    }

    /**
     * 映射属性名称
     *
     * @param prop 原始属性名
     * @return 映射后的属性名
     */
    private String mapPropertyName(String prop) {
        switch (prop) {
            //"washingTime" 和 "createTime" 都映射为 "createTime"
            case "washingTime":
            case "createTime":
                return "createTime"; // 创建时间
            //"clientName" 映射为 "enterpriseName"
            case "clientName":
                return "enterpriseName";// 客户名称
            //"configType" 映射为 "configurationCategory"
            case "configType":
                return "configurationCategory"; // 产品配置类别
            //"productModel" 映射为 "productModelNumber"
            case "productModel":
                return "productModelNumber"; // 产品型号
            //"rfidCode" 映射为 "rfidTagNumberWork"
            case "rfidCode":
                return "rfidTagNumberWork"; // 工作服rfid标签编码
            default:
                return prop; // 其他字段同名
        }
    }

    /**
     * 将WmsDeliveryDetail转换为WashingRecordExcelByDate
     *
     * @param detail WmsDeliveryDetail对象
     * @param index  序号
     * @return WashingRecordExcelByDate对象
     */
    private WashingRecordExcelByDate convertToWashingRecordExcel(WmsDeliveryDetail detail, int index) {
        WashingRecordExcelByDate excel = new WashingRecordExcelByDate();
        excel.setIndex(index);
        excel.setClientName(detail.getEnterpriseName());//客户名称
        excel.setEmployeeName(detail.getEmployeeName());//员工姓名
        excel.setEmployeePhone(detail.getEmployeePhone());//员工电话
        excel.setDepartment(detail.getDepartment());//部门名称
        excel.setSubDepartment(detail.getSubDepartment());//子部门名称
        excel.setConfigType(detail.getConfigurationCategory());//配置品类
        excel.setProductModel(detail.getProductModelNumber());//产品款号
        excel.setProductColor(detail.getProductColor());//产品颜色
        excel.setLogoPosition(detail.getLogoPosition());//LOGO位置
        excel.setProductSpecification(detail.getProductSpecification());//产品规格
        excel.setEmployeeId(detail.getEmployeeId());//员工ID
        excel.setRfidCode(detail.getRfidTagNumberWork());//工作服RFID标签编码
        excel.setRfidTagNumber(detail.getRfidTagNumber());//RFID标签号
        excel.setWashCount(washingRecordService.countByRfidCode(detail.getRfidTagNumberWork()));//洗涤次数
        return excel;
    }

    /**
     * 根据工作服RFID标签编码查询详细的洗涤记录
     * @param rfidCode 工作服标签编码
     * @return 洗涤记录列表
     */
    /*@PostMapping("/findByRfidCode.do")
    public ResultBean findByRfidCode(@RequestParam String rfidCode) {

        log.info("查询RFID编码: {}", rfidCode);

        //根据RFID工作服标签编码获取到总洗涤次数
        int totalWashCount = washingRecordService.countByRfidCode(rfidCode);
        log.info("washing_record表中该RFID的总洗涤次数: {}", totalWashCount);

        //根据RFID工作服标签编码从wms_delivery_detail表获取到每条洗涤记录
        // 直接使用SQL查询来验证数据
        String sql = "FROM WmsDeliveryDetail WHERE rfidTagNumberWork = ?0";
        List<WmsDeliveryDetail> allDeliveryDetails = wmsDeliveryDetailService.findByRfidTagNumberWork(rfidCode);
        log.info("直接SQL查询，wms_delivery_detail表中找到{}条记录", allDeliveryDetails.size());

        // 打印所有找到的记录的详细信息
        for (WmsDeliveryDetail detail : allDeliveryDetails) {
            log.info("记录ID: {}, type: {}, rfidTagNumberWork: {}, createTime: {}",
                    detail.getId(), detail.getType(), detail.getRfidTagNumberWork(), detail.getCreateTime());
        }

        // 过滤出type为"脏衣清点机001"的记录
        List<WmsDeliveryDetail> deliveryDetails = allDeliveryDetails.stream()
                .filter(detail -> com.rutong.framework.constant.GlobalContsant.CLOTHING_WASH_TYPE_7.equals(detail.getType()))
                .collect(Collectors.toList());
        log.info("过滤type='脏衣清点机001'后，找到{}条记录", deliveryDetails.size());

        // 如果过滤后没有记录，尝试用rfidTagNumber字段查询
        if (deliveryDetails.isEmpty()) {
            log.info("使用rfidTagNumberWork字段未找到type='脏衣清点机001'的记录，尝试使用rfidTagNumber字段查询");
            List<WmsDeliveryDetail> allDeliveryDetails2 = wmsDeliveryDetailService.findByRfidTagNumber(rfidCode);
            log.info("使用rfidTagNumber字段直接SQL查询，找到{}条记录", allDeliveryDetails2.size());

            // 过滤出type为"脏衣清点机001"的记录
            deliveryDetails = allDeliveryDetails2.stream()
                    .filter(detail -> com.rutong.framework.constant.GlobalContsant.CLOTHING_WASH_TYPE_7.equals(detail.getType()))
                    .collect(Collectors.toList());
            log.info("使用rfidTagNumber字段过滤type='脏衣清点机001'后，找到{}条记录", deliveryDetails.size());
        }

        // 转换为WashingRecord格式以兼容前端
        List<WashingRecord> washingRecords = new ArrayList<>();
        for (WmsDeliveryDetail detail : deliveryDetails) {
            WashingRecord record = convertToWashingRecord(detail, detail.getCreateTime());
            record.setWashCount(totalWashCount); // 设置总洗涤次数
            washingRecords.add(record);

            log.info("添加记录: ID={}, 员工={}, 洗涤时间={}, rfidTagNumberWork={}, rfidTagNumber={}",
                    detail.getId(), detail.getEmployeeName(), detail.getCreateTime(),
                    detail.getRfidTagNumberWork(), detail.getRfidTagNumber());
        }

        log.info("最终返回{}条记录", washingRecords.size());
        return ResultBean.success(washingRecords);
    }*/

}

