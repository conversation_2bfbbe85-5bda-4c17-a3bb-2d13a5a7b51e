package com.rutong.platform.wechat.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 配置信息项目DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigItemDTO {
    
    /**
     * 配置品类
     */
    private String configurationCategory;
    
    /**
     * 产品款号
     */
    private String productModelNumber;
    
    /**
     * 产品规格
     */
    private String productSpecification;
    
    /**
     * 产品序列号
     */
    private String productSerialNumber;
    
    /**
     * 启用日期
     */
    private Date protocolStartDate;
    
    /**
     * 截止日期
     */
    private Date protocolEndDate;
} 