package com.rutong.platform.client.service;

import com.rutong.platform.client.entity.ClauseInfo;
import com.rutong.platform.client.entity.ClientInfo;
import com.rutong.platform.common.service.BaseService;
import org.springframework.stereotype.Service;

@Service
public class ClientInfoService extends BaseService<ClientInfo> {


    public ClientInfo findByContractNumber(String contractNumber) {
        return dao.findByPropertyFirst(ClientInfo.class, ClientInfo.FIELD_COL_CONTRACT_NUMBER, contractNumber);
    }

}
