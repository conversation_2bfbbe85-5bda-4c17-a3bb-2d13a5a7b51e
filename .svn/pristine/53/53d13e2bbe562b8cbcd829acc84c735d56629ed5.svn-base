package com.rutong.platform.client.entity;

/**
 * @ClassName ExpenseSettlement * @Description TODO
 * <AUTHOR>
 * @Date 15:36 2024/11/7
 * @Version 1.0
 **/

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 费用结算与查询
 */
@Data
@Entity
@Table(name = "expense_settlement")
public class ExpenseSettlement extends BaseEntity {
    public static final String FIELD_COL_CONTRACT_NUMBER = "contractNumber";

    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 客户名称
     */
    private String enterpriseName;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date settlementStart; // 结算开始时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date settlementEnd; // 结算结束时间
    private BigDecimal monthlyCleaningRentalFee; // 月清洗租赁费用
    private BigDecimal monthlyProductFee; // 月服装产品费用
    private BigDecimal totalMonthlyPayableFee; // 月合计应付总费用
    private BigDecimal totalMonthlyPayableCount; // 月合计应洗次数
    private BigDecimal totalMonthlyPayableFeeContract; // 合同合计应洗次数
    private BigDecimal totalMonthlyPayableFeeContractCount; // 合同合计应付总费用
    private String contractPaymentDate; // 协议付款日
    private Date actualPaymentDate; // 实际付款日期
    private Date billingDate; // 开票日期
    private String invoiceNumber; // 发票号码
    private BigDecimal monthlyPaidAmount; // 月已支付金额
    private BigDecimal monthlyOutstandingAmount; // 月欠款金额
    private BigDecimal cumulativeOutstandingAmount; // 月累计欠款金额
    private int cumulativeOutstandingDays; // 累计欠款天数
    private BigDecimal contractLateFeeRate; // 协议滞纳金比例
    private BigDecimal cumulativeLateFeeAmount; // 累计滞纳金金额
    private String status; // 状况
    private String relatedRemarks; // 相关备注
    private String filler; // 填写人
}
