package com.rutong.platform.wechat.service;

import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.washing.service.WashingRecordService;
import com.rutong.platform.wechat.dto.UserInfoDTO;
import com.rutong.platform.wechat.dto.WashingInfoDTO;
import com.rutong.platform.wechat.dto.WashingItemDTO;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 洗涤信息服务类
 */
@Service
public class WashingInfoService {
    
    private static final Logger log = LoggerFactory.getLogger(WashingInfoService.class);
    
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;
    
    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private WashingRecordService washingRecordService;

    /**
     * 获取当前登录用户的洗涤信息
     * 
     * @return 用户洗涤信息
     */
    public WashingInfoDTO getWashingInfoByLoginUser() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null || loginUser.getUser() == null) {
            log.warn("用户未登录或登录信息不完整");
            return null;
        }
        
        SysUser user = loginUser.getUser();
        String phone = user.getPhone();
        log.info("当前用户手机号: {}", phone);
        
        // 根据手机号获取用户信息
        return getWashingInfoByPhone(phone);
    }
    
    /**
     * 根据员工手机号获取洗涤信息
     *
     * @param phone 员工手机号
     * @return 洗涤信息
     */
    public WashingInfoDTO getWashingInfoByPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            log.warn("员工手机号为空，无法查询洗涤信息");
            return null;
        }
        
        log.info("开始查询员工手机号[{}]的洗涤信息", phone);
        
        UserInfoDTO userInfo = userInfoService.getUserInfoByPhone(phone);
        if (userInfo == null) {
            log.warn("未找到用户信息，phone: {}", phone);
            return null;
        }
        
        log.info("获取到用户信息: userId={}, employeeId={}, name={}", 
                 userInfo.getUserId(), userInfo.getEmployeeId(), userInfo.getName());
        
        // 通过手机号查询配置信息，而不是通过员工工号
        List<EmployeeProductConfiguration> configs = employeeProductConfigurationService.findByEmployeePhone(phone);
        log.info("查询到的配置数量: {}", configs != null ? configs.size() : 0);
        
        if (configs == null || configs.isEmpty()) {
            log.info("未找到员工手机号[{}]的产品配置信息", phone);
            WashingInfoDTO washingInfoDTO = new WashingInfoDTO();
            washingInfoDTO.setEmployeeId(userInfo.getEmployeeId());
            washingInfoDTO.setUserName(userInfo.getName());
            washingInfoDTO.setEmployeePhone(phone);
            washingInfoDTO.setWashingItems(new ArrayList<>());
            return washingInfoDTO;
        }
        
        // 不再过滤"合作中"的配置，展示全部数据
        List<EmployeeProductConfiguration> allConfigs = configs;
        
        List<String> rfidTags = allConfigs.stream()
                .map(EmployeeProductConfiguration::getRfidTagNumber)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        
        log.info("找到RFID标签数量: {}", rfidTags.size());
        
        // 获取洗涤记录
        List<WmsDeliveryDetail> washRecords = wmsDeliveryDetailService.findByRfidTagsAndEmployeeId(rfidTags, userInfo.getEmployeeId());
        
        // 统计每个RFID标签的洗涤次数
        Map<String, Integer> washCounts = new HashMap<>();
        for (WmsDeliveryDetail record : washRecords) {
            String rfid = record.getRfidTagNumber();
            washCounts.put(rfid, washCounts.getOrDefault(rfid, 0) + 1);
        }
        
        List<WashingItemDTO> washingItems = new ArrayList<>();
        
        // 为每个配置项创建一个洗涤项目
        for (EmployeeProductConfiguration config : allConfigs) {
            String weeklyWashDate = config.getWeeklyWashDate();
            Integer weeklyWashFrequency = 0;
            
            if (weeklyWashDate != null && !weeklyWashDate.trim().isEmpty()) {
                String[] washDays = weeklyWashDate.split(",");
                weeklyWashFrequency = washDays.length;
                log.info("配置项[{}]的换洗日期为[{}]，计算得到的每周换洗次数为[{}]", 
                    config.getConfigurationCategory(), weeklyWashDate, weeklyWashFrequency);
            } else {
                log.warn("配置项[{}]的每周换洗日期为空，设置每周换洗次数默认值为0", config.getConfigurationCategory());
            }
            
            int totalWashCount = weeklyWashFrequency * 52;
            

            // 获取该RFID标签的洗涤次数
            int washCount = 0;
            if (config.getRfidTagNumber() != null && !config.getRfidTagNumber().isEmpty()) {
                washCount = washingRecordService.countByRfidCode(config.getRfidTagNumberWork());
            }
            
            log.info("配置项[{}]的总洗涤次数: {}, 已洗次数: {}, 状态: {}", 
                config.getConfigurationCategory(), totalWashCount, washCount, config.getStatus());    

            WashingItemDTO washingItem = new WashingItemDTO();
            washingItem.setConfigurationCategory(config.getConfigurationCategory());
            washingItem.setProductModelNumber(config.getProductModelNumber());
            washingItem.setWeeklyWashDate(weeklyWashDate);
            washingItem.setWeeklyWashFrequency(weeklyWashFrequency);
            washingItem.setTotalWashCount(totalWashCount);
            washingItem.setWashCount(washCount);
            washingItem.setProductSerialNumber(config.getProductSerialNumber());
            
            washingItems.add(washingItem);
        }
        
        log.info("最终生成的洗涤项目数量: {}", washingItems.size());
        
        WashingInfoDTO washingInfoDTO = new WashingInfoDTO();
        washingInfoDTO.setEmployeeId(userInfo.getEmployeeId());
        washingInfoDTO.setUserName(userInfo.getName());
        washingInfoDTO.setEmployeePhone(phone);
        washingInfoDTO.setWashingItems(washingItems);
        return washingInfoDTO;
    }
    
    /**
     * 根据员工工号获取洗涤信息
     *
     * @param employeeId 员工工号
     * @param userName 用户姓名
     * @param employeePhone 员工电话
     * @return 洗涤信息
     */
    public WashingInfoDTO getWashingInfoByEmployeeId(String employeeId, String userName, String employeePhone) {
        if (StringUtils.isBlank(employeeId)) {
            log.warn("员工工号为空，无法查询洗涤信息");
            return null;
        }
        
        log.info("开始查询员工[{}]的洗涤信息", employeeId);
        
        // 不再过滤"合作中"的配置，查询所有配置
        List<EmployeeProductConfiguration> configs = employeeProductConfigurationService.findByEmployeeId(employeeId);
        log.info("查询到的配置数量: {}", configs != null ? configs.size() : 0);
        
        if (configs == null || configs.isEmpty()) {
            log.info("未找到员工[{}]的产品配置信息", employeeId);
            WashingInfoDTO washingInfoDTO = new WashingInfoDTO();
            washingInfoDTO.setEmployeeId(employeeId);
            washingInfoDTO.setUserName(userName);
            washingInfoDTO.setEmployeePhone(employeePhone);
            washingInfoDTO.setWashingItems(new ArrayList<>());
            return washingInfoDTO;
        }
        
        List<String> rfidTags = configs.stream()
                .map(EmployeeProductConfiguration::getRfidTagNumber)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        
        log.info("找到RFID标签数量: {}", rfidTags.size());
        
        List<WmsDeliveryDetail> washRecords = wmsDeliveryDetailService.findByRfidTagsAndEmployeeId(rfidTags, employeeId);
        
        Map<String, Integer> washCounts = new HashMap<>();
        for (WmsDeliveryDetail record : washRecords) {
            String rfid = record.getRfidTagNumber();
            washCounts.put(rfid, washCounts.getOrDefault(rfid, 0) + 1);
        }
        
        List<WashingItemDTO> washingItems = new ArrayList<>();
        
        // 为每个配置项创建一个洗涤项目，不再按类别分组
        for (EmployeeProductConfiguration config : configs) {
            String weeklyWashDate = config.getWeeklyWashDate();
            Integer weeklyWashFrequency = 0;
            
            if (weeklyWashDate != null && !weeklyWashDate.trim().isEmpty()) {
                String[] washDays = weeklyWashDate.split(",");
                weeklyWashFrequency = washDays.length;
                log.info("配置项[{}]的换洗日期为[{}]，计算得到的每周换洗次数为[{}]", 
                    config.getConfigurationCategory(), weeklyWashDate, weeklyWashFrequency);
            } else {
                log.warn("配置项[{}]的每周换洗日期为空，设置每周换洗次数默认值为0", config.getConfigurationCategory());
            }
            
            int totalWashCount = weeklyWashFrequency * 52;

            // 获取该RFID标签的洗涤次数
            int washCount = 0;
            if (config.getRfidTagNumber() != null && !config.getRfidTagNumber().isEmpty()) {
                washCount = washingRecordService.countByRfidCode(config.getRfidTagNumberWork());
            }

            log.info("配置项[{}]的总洗涤次数: {}, 已洗次数: {}, 状态: {}", 
                config.getConfigurationCategory(), totalWashCount, washCount, config.getStatus());

            WashingItemDTO washingItem = new WashingItemDTO();

            washingItem.setConfigurationCategory(config.getConfigurationCategory());
            washingItem.setProductModelNumber(config.getProductModelNumber());
            washingItem.setWeeklyWashDate(weeklyWashDate);
            washingItem.setWeeklyWashFrequency(weeklyWashFrequency);
            washingItem.setTotalWashCount(totalWashCount);
            washingItem.setWashCount(washCount);
            washingItem.setProductSerialNumber(config.getProductSerialNumber());
            
            washingItems.add(washingItem);
        }
        
        log.info("最终生成的洗涤项目数量: {}", washingItems.size());
        
        WashingInfoDTO washingInfoDTO = new WashingInfoDTO();
        washingInfoDTO.setEmployeeId(employeeId);
        washingInfoDTO.setUserName(userName);
        washingInfoDTO.setEmployeePhone(employeePhone);
        washingInfoDTO.setWashingItems(washingItems);
        return washingInfoDTO;
    }
} 