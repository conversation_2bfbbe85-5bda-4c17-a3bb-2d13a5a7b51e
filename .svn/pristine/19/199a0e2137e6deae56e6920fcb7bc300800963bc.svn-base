package com.rutong.platform.wechat.service;

import java.util.List;

import com.rutong.platform.client.entity.EmployeeProductConfigurationHistory;
import com.rutong.platform.client.service.EmployeeProductConfigurationHistoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.rutong.framework.core.exception.ServiceException;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.utils.StringUtils;
import com.rutong.platform.wechat.dto.UserInfoDTO;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.sys.service.SysUserService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 用户信息查询服务
 */
@Service
public class UserInfoService {
    private static final Logger log = LoggerFactory.getLogger(UserInfoService.class);
    
    @Autowired
    private SysUserService sysUserService;
    
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;
    @Autowired
    private EmployeeProductConfigurationHistoryService employeeProductConfigurationHistoryService;
    
    /**
     * 根据手机号查询用户信息
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    public UserInfoDTO getUserInfoByPhone(String phone) {
        // 参数校验
        if (StringUtils.isBlank(phone)) {
            log.warn("查询用户信息失败：手机号为空");
            return null;
        }
        
        try {
            // 查询员工配置
            List<EmployeeProductConfiguration> configs = employeeProductConfigurationService.findByEmployeePhone(phone);
            
            // 创建返回对象
            UserInfoDTO userInfo = new UserInfoDTO();
            userInfo.setPhone(phone);
            
            // 从系统用户表获取用户信息，确保name字段有值
//            SysUser sysUser = sysUserService.selectUserByPhone(phone);
//            if (sysUser != null) {
//                userInfo.setName(sysUser.getNickname());
//                // 如果nickname为空，使用username作为备选
//                if (StringUtils.isBlank(userInfo.getName())) {
//                    userInfo.setName(sysUser.getUsername());
//                }
//            }
            
            // 如果有员工配置，使用配置中的数据（优先级更高）
            if (configs != null && !configs.isEmpty()) {
                EmployeeProductConfiguration config = configs.get(0);
                
                // 用户信息
                if (StringUtils.isNotBlank(config.getEmployeeName())) {
                    userInfo.setName(config.getEmployeeName());
                }
                userInfo.setEmployeeId(config.getEmployeeId());
                userInfo.setEnterpriseName(config.getEnterpriseName());
                userInfo.setDepartment(config.getDepartment());
                userInfo.setLockerSerialNumber(config.getLockerSerialNumber());
                
                // 记录日志
                log.debug("从员工配置中获取用户信息成功: employeeId={}, name={}", 
                          config.getEmployeeId(), config.getEmployeeName());
            } else {
                log.warn("未找到手机号为[{}]的员工配置", phone);
            }
            
            // 确保name字段有值
            if (StringUtils.isBlank(userInfo.getName())) {
                userInfo.setName("用户" + phone.substring(Math.max(0, phone.length() - 4)));
                log.warn("用户[{}]名称为空，使用默认名称: {}", phone, userInfo.getName());
            }
            
            // 记录最终返回的用户信息
            log.debug("返回用户信息: phone={}, name={}, enterpriseName={}, department={}, employeeId={}", 
                      userInfo.getPhone(), 
                      userInfo.getName(), 
                      userInfo.getEnterpriseName(), 
                      userInfo.getDepartment(), 
                      userInfo.getEmployeeId());
            
            return userInfo;
        } catch (Exception e) {
            log.error("查询手机号为[{}]的用户信息失败: {}", phone, e.getMessage(), e);
            return null;
        }
    }
} 