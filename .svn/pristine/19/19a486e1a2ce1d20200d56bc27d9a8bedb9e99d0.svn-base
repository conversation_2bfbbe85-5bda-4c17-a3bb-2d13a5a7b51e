package com.rutong.platform.washing.controller;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.constant.GlobalContsant;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.ClientInfo;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.ClientInfoService;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.inventory.entity.InventoryInformation;
import com.rutong.platform.inventory.service.InventoryInformationService;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.sys.service.SysUserService;
import com.rutong.platform.washing.entity.WashingTask;
import com.rutong.platform.washing.service.WashingTaskService;
import com.rutong.platform.wms.entity.WmsCar;
import com.rutong.platform.wms.entity.WmsDelivery;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.service.WmsCarService;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import com.rutong.platform.wms.service.WmsDeliveryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 库存
 */
@RestController
@RequestMapping("/washing/washingTask")
@LogDesc(title = "洗涤任务单")
public class WashingTaskController extends CrudController<WashingTask> {


    @Autowired
    private WashingTaskService washingTaskService;
    @Autowired
    private WmsDeliveryService wmsDeliveryService;
    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;

    @Autowired
    private WmsCarService wmsCarService;
    @Autowired
    private ClientInfoService clientInfoService;
    @Autowired
    private SysUserService sysUserService;

    @Override
    public BaseService<WashingTask> getService() {
        return washingTaskService;
    }


    /**
     * 洗涤任务单创建
     * @param entity
     * @return
     */
    @Override
    public ResultBean save(WashingTask entity) {
        ClientInfo byId = clientInfoService.findById(entity.getClientId());
        entity.setClientName(byId.getEnterpriseName());

        //任务单号按照日期显示,精确到毫秒
        //如果是2025年5月23日10点46分49秒11毫秒,生成的任务单号将是：RW2025052310464911
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSS");
        String timestamp = sdf.format(new Date());
        entity.setTaskOrderNumber("RW" + timestamp);
        
        SysUser byId1 = sysUserService.findById(entity.getDriverEmployeeId());
        entity.setDriverName(byId1.getNickname());
        entity.setDriverPhone(byId1.getPhone());
        entity.setDriverEmployeeId(entity.getDriverEmployeeId());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        entity.setDispatcherName(loginUser.getUser().getNickname());
        entity.setDispatcherEmpId(loginUser.getUserId());
        entity.setStatus(0L);
        return super.save(entity);
    }


    /**
     * 删除洗涤任务单
     * 只有未收取状态的任务单才能删除
     * @param entity
     * @return
     */
    @Override
    public ResultBean delete(WashingTask entity) {
        // 检查参数是否为空
        if (entity == null || entity.getId() == null || entity.getId().trim().isEmpty()) {
            return ResultBean.error("无效的参数：任务单ID不能为空", -1);
        }
        try {
            WashingTask task = washingTaskService.findById(entity.getId());
            if (task == null) {
                return ResultBean.error("任务单不存在", -1);
            }
            // 检查任务单状态，仅允许删除未收取状态的任务单
            if (task.getStatus() == null || task.getStatus() != 0L) {
                return ResultBean.error("只有未收取状态的任务单才能删除！", -1);
            }
            List<WmsDelivery> byDeliveryId = wmsDeliveryService.findByDeliveryId(entity.getId());
            for (WmsDelivery wmsDelivery : byDeliveryId) {
                List<WmsDeliveryDetail> byDeliveryId1 = wmsDeliveryDetailService.findByDeliveryId(wmsDelivery.getId());
                for (WmsDeliveryDetail wmsDeliveryDetail : byDeliveryId1) {
                    wmsDeliveryDetailService.delete(wmsDeliveryDetail);
                }
                wmsDeliveryService.delete(wmsDelivery);
            }
            return super.delete(entity);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultBean.error("删除失败：" + e.getMessage(), -1);
        }
    }

    /**
     * 批量删除
     * @param entitys
     * @return
     */
    @Override
    public ResultBean deleteBatch(List<WashingTask> entitys) {
        return super.deleteBatch(entitys);
    }


    /**
     * 获取异常信息
     * @param recordId
     * @param sorts
     * @return
     */
    @PostMapping(value = "/fetchDetails.do")
    public ResultBean fetchDetails(String recordId, @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        // 处理排序条件
        if (sorts.isEmpty()) {
            SortCondition sortCondition = new SortCondition();
            sortCondition.setDirection(SortCondition.SORT_DESC);
            sortCondition.setProperty(BaseEntity.FIELD_CREATE_TIME);
            sorts.add(sortCondition);
        }
        // 获取相关数据
        WmsDelivery byId = wmsDeliveryService.findById(recordId);
        return ResultBean.success(washingTaskService.calculationAnomalies(byId));
    }

}
