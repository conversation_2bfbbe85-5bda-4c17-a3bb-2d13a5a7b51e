package com.rutong.platform.sys.entity;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 参数配置表 sys_config
 */
@Entity
@Table(name = "sys_config")
public class SysConfig extends BaseEntity {

	/** 参数名称 */
	private String configname;

	/** 参数键名 */
	private String configkey;

	/** 参数键值 */
	private String configvalue;

	public String getConfigname() {
		return configname;
	}

	public void setConfigname(String configname) {
		this.configname = configname;
	}

	public String getConfigkey() {
		return configkey;
	}

	public void setConfigkey(String configkey) {
		this.configkey = configkey;
	}

	public String getConfigvalue() {
		return configvalue;
	}

	public void setConfigvalue(String configvalue) {
		this.configvalue = configvalue;
	}

}
