package com.rutong.framework.core.dao;

import java.io.Serializable;
import java.math.BigInteger;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

import org.hibernate.Session;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.platform.common.entity.BaseEntity;

public interface Dao {
	/**
	 * 当前线程的session对象，在事务结束后会自动关闭。
	 * 
	 * @return
	 */
	public Session getCurrentSession();

	/**
	 * 获取当前数据库连接的Connection对象
	 * 
	 * @return
	 */
	public Connection getConnection();

	/**
	 * 加载实体对象<如数据库不存在,返回null。 >
	 * 
	 * @param c  实体对象
	 * @param id 实体对象主键
	 * @return
	 */
	public <T, ID extends Serializable> T findById(Class<T> c, ID id);

	public <T> void persist(T t);

	public void flush();

	public void clear();
	/**
	 * 删除数据
	 * 
	 * @param T 实体对象类
	 * @param t
	 * @return
	 */
	public <T> void delete(T t);

	/**
	 * 根据查询对象查询总页数
	 * 
	 * @param <T>
	 * @param t
	 * @param query
	 * @return
	 */
	public <T> Long count(Class<T> t, List<FilterCondition> filters);

	/**
	 * 分页查询对象
	 * 
	 * @param <T>
	 * @param t
	 * @param gridParams
	 * @param query
	 * @return
	 */
	public <T> List<T> findAll(Class<T> t, GridParams gridParams, List<FilterCondition> filters,
			List<SortCondition> sorts);

	/**
	 * 根据bean类取得所有记录
	 * 
	 * @param className
	 * @param t
	 * @return 所有记录列表
	 */
	public <T> List<T> findAll(Class<T> t);

	/**
	 * 根据bean类取得所有记录可以设置查询条件
	 * 
	 * @param className
	 * @param t
	 * @return 所有记录列表
	 */
	public <T> List<T> findAll(Class<T> t, List<FilterCondition> filters);

	/**
	 * 基础查询，通过实体对象类查询对象列表信息
	 * 
	 * @param t
	 * @param obj 参数 格式，usercode,admin,password,admin
	 * @return
	 */
	public <T> List<T> findByProperty(Class<T> t, Object... obj);

	/**
	 * 基础查询，通过实体对象类查询对象数量信息
	 * 
	 * @param t
	 * @param obj 参数 格式，usercode,admin,password,admin
	 * @return
	 */
	public <T> Long countByProperty(Class<T> t, Object... obj);

	/**
	 * 基础查询，通过实体对象类查询对象信息
	 * 
	 * @param t
	 * @param obj 参数 格式，usercode,admin,password,admin
	 * @return
	 */
	public <T> T findByPropertyFirst(Class<T> t, Object... obj);

	/**
	 * 基础查询
	 * 
	 * @param sql
	 * @param params 数组参数 例如： user.name=?
	 * @return List<Map>: 每一元素Map中应一行, Map中的键对应数据库中的字段名
	 */
	public List<Map<String, Object>> executeSQLQuery(String sql, Object... params);

	/**
	 * 基础查询
	 * 
	 * @param sql
	 * @param c   对象Class
	 * @DynamicUpdate</b>
	 * @param params 数组参数 例如： user.name=?
	 * @return List<T>: 返回实体对象集合
	 */
	public <T> List<T> executeSQLQuery(String sql, Class<T> c, Object... params);
	
	/**
	 * hql查询
	 * @param <T>
	 * @param hql
	 * @param c
	 * @param params
	 * @return
	 */
	public <T> List<T> executeQuery(String hql, Class<T> c, Object... params);
	
	/**
	 * 基础查询
	 * 
	 * @param sql
	 * @param c   对象Class
	 * @DynamicUpdate</b>
	 * @param params 数组参数 例如： user.name=?
	 * @return List<T>: 返回实体对象集合
	 */
	public BigInteger countSQLQuery(String sql, Object... params);

	/**
	 * sql更新数据
	 * 
	 * @param sql
	 * @param params
	 * @return
	 */
	public <T> int executeSQLUpdate(String sql, Object... params);

	/**
	 * 基础删除，通过实体对象类删除对象信息
	 * 
	 * @param t
	 * @param obj 参数 格式，usercode,admin,password,admin
	 * @return
	 */
	public <T> int deleteByProperty(Class<T> t, Object... obj);

}
