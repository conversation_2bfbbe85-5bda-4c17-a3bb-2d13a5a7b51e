package com.rutong.platform.client.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * 导出工具类
 */
public class ExportUtil {

    /**
     * 为Excel文件名编码，处理中文文件名问题
     * @param fileName 原始文件名
     * @return 编码后的文件名
     */
    public static String encodingExcelFilename(String fileName) {
        try {
            return URLEncoder.encode(fileName, "UTF-8") + ".xlsx";
        } catch (UnsupportedEncodingException e) {
            return fileName + ".xlsx";
        }
    }
} 