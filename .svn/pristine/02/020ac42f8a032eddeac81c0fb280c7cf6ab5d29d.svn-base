package com.rutong.platform.wechat.controller;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.wechat.dto.UserInfoDTO;
import com.rutong.platform.wechat.dto.WorkClothesProfileDTO;
import com.rutong.platform.wechat.service.UserInfoService;
import com.rutong.platform.wechat.service.ChatInfoService;
import com.rutong.platform.wechat.dto.ChatInfoDTO;
import com.rutong.platform.wechat.dto.ChatItemDTO;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import com.rutong.platform.washing.service.WashingRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;
import com.rutong.framework.core.jpa.FilterCondition;

@RestController
@RequestMapping("/wechat/workClothesProfile")
public class WorkClothesProfileController {
    // 日志对象，用于记录日志信息
    private static final Logger log = LoggerFactory.getLogger(WorkClothesProfileController.class);
    
    // 自动注入UserInfoService，用于获取用户信息
    @Autowired
    private UserInfoService userInfoService;
    
    // 自动注入EmployeeProductConfigurationService，用于获取员工产品配置信息
    @Autowired
    private EmployeeProductConfigurationService configService;

    @Autowired
    private ChatInfoService chatInfoService;

    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;
    
    //注入WashingRecordService，用于获取已洗涤次数
    @Autowired
    private WashingRecordService washingRecordService;

    // 处理GET请求，获取工服档案详情
    @GetMapping("/detail")
    public ResultBean getWorkClothesProfile(@RequestParam(value = "clothesCode", required = false) String clothesCode) {
        try {
            // 获取当前登录用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null || loginUser.getUser() == null) {
                // 如果用户未登录，记录警告日志并返回错误信息
                log.warn("用户未登录，无法查询工服档案");
                return ResultBean.error("用户未登录", 401);
            }
            
            // 获取用户手机号
            String userPhone = loginUser.getUser().getPhone();
            if (userPhone == null || userPhone.isEmpty()) {
                log.warn("用户手机号为空，无法查询工服档案");
                return ResultBean.error("用户信息不完整", 400);
            }
            
            log.info("查询手机号[{}]的工服档案", userPhone);

            // 直接根据手机号查询服装配置，不再使用员工工号
            List<EmployeeProductConfiguration> configs = configService.findByEmployeePhone(userPhone);
            if (configs == null || configs.isEmpty()) {
                log.warn("未找到手机号[{}]的服装信息", userPhone);
                return ResultBean.error("未找到服装信息", 404);
            }
            
            // 创建两个映射：一个按配置类型和款号，另一个按ID
            Map<String, EmployeeProductConfiguration> configIdMap = new HashMap<>();
            
            for (EmployeeProductConfiguration config : configs) {
                // 确保只处理属于当前用户手机号的配置
                if (userPhone.equals(config.getEmployeePhone())) {
                    configIdMap.put(config.getId(), config);
                }
            }

            // 如果过滤后没有配置，返回错误
            if (configIdMap.isEmpty()) {
                log.warn("未找到手机号[{}]的有效服装配置", userPhone);
                return ResultBean.error("未找到当前用户的服装信息", 404);
            }

            // 使用Set来存储唯一的配置，避免重复
            Set<String> processedRfidTags = new HashSet<>();
            List<EmployeeProductConfiguration> uniqueConfigs = new ArrayList<>();
            
            for (EmployeeProductConfiguration config : configs) {
                String rfidTag = config.getRfidTagNumberWork();
                // 如果这个RFID标签还没处理过，添加到结果中
                if (rfidTag != null && !processedRfidTags.contains(rfidTag)) {
                    processedRfidTags.add(rfidTag);
                    uniqueConfigs.add(config);
                }
            }
            
            if (uniqueConfigs.isEmpty()) {
                log.warn("未找到匹配的服装信息");
                return ResultBean.error("未找到服装信息", 404);
            }
            
            // 默认第一件或指定服装
            EmployeeProductConfiguration selected = null;
            if (clothesCode != null && !clothesCode.isEmpty()) {
                // 如果指定了clothesCode，查找匹配的服装
                for (EmployeeProductConfiguration cfg : uniqueConfigs) {
                    if (clothesCode.equals(cfg.getRfidTagNumberWork())) {
                        selected = cfg;
                        break;
                    }
                }
            }
            
            // 如果没有指定clothesCode或没有找到匹配的服装，默认使用第一件
            if (selected == null && !uniqueConfigs.isEmpty()) {
                selected = uniqueConfigs.get(0);
            }
            
            // 如果指定了clothesCode，返回单个服装的详细信息
            if (clothesCode != null && !clothesCode.isEmpty() && selected != null) {
                Map<String, Object> clothesProfile = new HashMap<>();
                
                // 主要信息
                clothesProfile.put("clothesCode", selected.getRfidTagNumberWork());
                clothesProfile.put("type", selected.getConfigurationCategory());
                clothesProfile.put("enterpriseName", selected.getEnterpriseName());
                clothesProfile.put("productSize", selected.getProductModelNumber());
                clothesProfile.put("productSerialNumber", selected.getProductSerialNumber());
                
                // 所属员工信息
                clothesProfile.put("employeeName", selected.getEmployeeName());
                clothesProfile.put("employeeId", selected.getEmployeeId());
                clothesProfile.put("lockerSerialNumber", selected.getLockerSerialNumber());
                
                // 其他信息
                clothesProfile.put("status", selected.getStatus());
                
                // 从washing_record表获取实际洗涤次数
                int washCount = 0;
                if (selected.getRfidTagNumberWork() != null && !selected.getRfidTagNumberWork().isEmpty()) {
                    washCount = washingRecordService.countByRfidCode(selected.getRfidTagNumberWork());
                }
                clothesProfile.put("washCount", washCount);
                
                clothesProfile.put("protocolStartDate", selected.getProtocolStartDate());
                clothesProfile.put("protocolEndDate", selected.getProtocolEndDate());
                
                List<Map<String, Object>> clothesProfiles = new ArrayList<>();
                clothesProfiles.add(clothesProfile);
                
                Map<String, Object> result = new HashMap<>();
                result.put("clothesProfiles", clothesProfiles);
                
                log.info("返回手机号[{}]的指定工服档案信息，服装编码[{}]", userPhone, clothesCode);
                return ResultBean.success(result);
            }
            
            // 创建一个包装对象，包含所有服装的详细信息
            Map<String, Object> result = new HashMap<>();
            
            // 优化数据结构，避免重复数据
            List<Map<String, Object>> clothesProfiles = new ArrayList<>();
            
            for (EmployeeProductConfiguration config : uniqueConfigs) {
                Map<String, Object> clothesProfile = new HashMap<>();
                
                // 主要信息
                clothesProfile.put("clothesCode", config.getRfidTagNumberWork());
                clothesProfile.put("type", config.getConfigurationCategory());
                clothesProfile.put("enterpriseName", config.getEnterpriseName());
                clothesProfile.put("productSize", config.getProductModelNumber());
                clothesProfile.put("productSerialNumber", config.getProductSerialNumber());
                
                // 所属员工信息
                clothesProfile.put("employeeName", config.getEmployeeName());
                clothesProfile.put("employeeId", config.getEmployeeId());
                clothesProfile.put("lockerSerialNumber", config.getLockerSerialNumber());
                
                // 其他信息
                clothesProfile.put("status", config.getStatus());
                
                // 从washing_record表获取实际洗涤次数
                int washCount = 0;
                if (config.getRfidTagNumberWork() != null && !config.getRfidTagNumberWork().isEmpty()) {
                    washCount = washingRecordService.countByRfidCode(config.getRfidTagNumberWork());
                }
                clothesProfile.put("washCount", washCount);
                
                clothesProfile.put("protocolStartDate", config.getProtocolStartDate());
                clothesProfile.put("protocolEndDate", config.getProtocolEndDate());
                
                clothesProfiles.add(clothesProfile);
            }
            
            // 将服装详细信息存储，不再包含allClothes
            result.put("clothesProfiles", clothesProfiles);
            
            log.info("返回手机号[{}]的所有工服档案信息，共[{}]件服装", userPhone, clothesProfiles.size());
            return ResultBean.success(result);
            
        } catch (Exception e) {
            // 捕获异常，记录错误日志并返回系统异常信息
            log.error("查询工服档案失败: {}", e.getMessage(), e);
            return ResultBean.error("系统异常，请稍后重试", 500);
        }
    }
} 