package com.rutong.platform.client.entity;

import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 服务记录查询冬夏装明细表
 */
@Data
@Entity
@Table(name = "employee_product_configuration_detail")
public class EmployeeProductConfigurationDetail extends BaseEntity {


    //主表中配置id
    @Column(name = "employee_product_configuration_Id")
    private String employeeProductConfigurationId;

    //开始时间
    @Column(name = "start_time")
    private Date startTime;

    //结束时间
    @Column(name = "end_time")
    private Date endTime;

    //每周清洗时间
    @Column(name = "weekly_wash_date")
    private String weeklyWashDate;

    //单次清洗费用
    @Column(name = "single_wash_rental_fee")
    private BigDecimal singleWashRentalFee;



}