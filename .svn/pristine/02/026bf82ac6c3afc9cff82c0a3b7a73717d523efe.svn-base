package com.rutong.platform.client.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 服务记录查询（产品配置信息历史记录）
 */
@Data
@Entity
@Table(name = "employee_product_configuration_history")
public class EmployeeProductConfigurationHistory extends BaseEntity {

    public static final String FIELD_COL_CONTRACT_NUMBER = "contractNumber";
    public static final String FIELD_COL_RFID_TAG_NUMBER = "rfidTagNumber";
    public static final String EMPLOYEE_PHONE = "employeePhone";

    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 客户名称
     */
    private String enterpriseName;

    @ExcelProperty(value = "员工姓名", index = 1)
    private String employeeName;

    @ExcelProperty(value = "员工电话", index = 2)
    private String employeePhone;

    @ExcelProperty(value = "部门", index = 3)
    private String department;

    @ExcelProperty(value = "分部门", index = 4)
    private String subDepartment;

    @ExcelProperty(value = "配置品类", index = 5)
    private String configurationCategory;

    @ExcelProperty(value = "产品款号", index = 6)
    private String productModelNumber;

    @ExcelProperty(value = "产品颜色", index = 7)
    private String productColor;

    @ExcelProperty(value = "LOGO位置", index = 8)
    private String logoPosition;

    @ExcelProperty(value = "产品规格", index = 9)
    private String productSpecification;

    @ExcelProperty(value = "协议各品类配置数量", index = 10)
    private Integer protocolConfigurationQuantity;

    @ExcelProperty(value = "员工企业工号", index = 11)
    private String employeeId;

    @ExcelProperty(value = "RFID标签号", index = 12)
    private String rfidTagNumber;

    @ExcelProperty(value = "产品序列号", index = 13)
    private String productSerialNumber;

    @ExcelProperty(value = "存衣柜协议配置型号", index = 14)
    private String lockerModel;

    @ExcelProperty(value = "协议存衣柜配置数量", index = 15)
    private Integer lockerQuantity;

    @ExcelProperty(value = "更衣柜序列号", index = 16)
    private String lockerSerialNumber;

    @ExcelProperty(value = "脏衣柜协议配置型号", index = 17)
    private String dirtyLockerModel;

    @ExcelProperty(value = "协议脏衣柜配置数量", index = 18)
    private Integer dirtyLockerQuantity;

    @ExcelProperty(value = "脏衣柜序列号", index = 19)
    private String dirtyLockerSerialNumber;

    @ExcelProperty(value = "协议配置品类使用月份", index = 20)
    private String protocolUsageMonths;

    @ExcelProperty(value = "协议每周作息时间", index = 21)
    private String weeklySchedule;

    @ExcelProperty(value = "协议每周换洗日期", index = 22)
    private String weeklyWashDate;

    @ExcelProperty(value = "协议每周换洗次数", index = 23)
    private Integer weeklyWashFrequency;

    @ExcelProperty(value = "配置产品洗涤信息", index = 24)
    private String productWashInfo;

    @ExcelProperty(value = "协议单次清洗租赁费", index = 25)
    private BigDecimal singleWashRentalFee;

    @ExcelProperty(value = "协议每周清洗租赁费", index = 26)
    private BigDecimal weeklyWashRentalFee;

    @ExcelProperty(value = "协议服装结算价", index = 27)
    private BigDecimal clothingSettlementPrice;

    @ExcelProperty(value = "本月服装余值", index = 28)
    private BigDecimal clothingResidualValue;

    @ExcelProperty(value = "客方本月服装丢失次数", index = 29)
    private Integer clothingLossCount;

    @ExcelProperty(value = "客方本月服装丢失费", index = 30)
    private BigDecimal clothingLossFee;

    @ExcelProperty(value = "月合计费用", index = 31)
    private BigDecimal monthlyTotalCost;

    @ExcelProperty(value = "协议启用日期", index = 32)
    private Date protocolStartDate;

    @ExcelProperty(value = "协议截止日期", index = 33)
    private Date protocolEndDate;

    @ExcelProperty(value = "离职日期", index = 34)
    private Date resignationDate;

    @ExcelProperty(value = "属性", index = 35)
    private String attributes;

    @ExcelProperty(value = "状态", index = 36)
    private String status;

    @Transient
    private String countInfo;

    private String parentId;
    
    /**
     * 变更类型
     */
    private String changeType;
    
    /**
     * 旧值
     */
    private String oldValue;
    
    /**
     * 新值
     */
    private String newValue;
    
    /**
     * 操作人
     */
    private String operator;

    public String getContractNumber() {
        return contractNumber;
    }

    public void setContractNumber(String contractNumber) {
        this.contractNumber = contractNumber;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getEmployeePhone() {
        return employeePhone;
    }

    public void setEmployeePhone(String employeePhone) {
        this.employeePhone = employeePhone;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getSubDepartment() {
        return subDepartment;
    }

    public void setSubDepartment(String subDepartment) {
        this.subDepartment = subDepartment;
    }

    public String getConfigurationCategory() {
        return configurationCategory;
    }

    public void setConfigurationCategory(String configurationCategory) {
        this.configurationCategory = configurationCategory;
    }

    public String getProductModelNumber() {
        return productModelNumber;
    }

    public void setProductModelNumber(String productModelNumber) {
        this.productModelNumber = productModelNumber;
    }

    public String getProductColor() {
        return productColor;
    }

    public void setProductColor(String productColor) {
        this.productColor = productColor;
    }

    public String getLogoPosition() {
        return logoPosition;
    }

    public void setLogoPosition(String logoPosition) {
        this.logoPosition = logoPosition;
    }

    public String getProductSpecification() {
        return productSpecification;
    }

    public void setProductSpecification(String productSpecification) {
        this.productSpecification = productSpecification;
    }

    public Integer getProtocolConfigurationQuantity() {
        return protocolConfigurationQuantity;
    }

    public void setProtocolConfigurationQuantity(Integer protocolConfigurationQuantity) {
        this.protocolConfigurationQuantity = protocolConfigurationQuantity;
    }

// 声明一个公共的方法，返回类型为String，方法名为getEmployeeId
    public String getEmployeeId() {
    // 返回当前对象的employeeId属性的值
        return employeeId;
    }

    public void setEmployeeId(String employeeID) {
        this.employeeId = employeeID;
    }

    public String getRfidTagNumber() {
        return rfidTagNumber;
    }

    public void setRfidTagNumber(String rfidTagNumber) {
        this.rfidTagNumber = rfidTagNumber;
    }

    public String getProductSerialNumber() {
        return productSerialNumber;
    }

    public void setProductSerialNumber(String productSerialNumber) {
        this.productSerialNumber = productSerialNumber;
    }

    public String getLockerModel() {
        return lockerModel;
    }

    public void setLockerModel(String lockerModel) {
        this.lockerModel = lockerModel;
    }

    public Integer getLockerQuantity() {
        return lockerQuantity;
    }

    public void setLockerQuantity(Integer lockerQuantity) {
        this.lockerQuantity = lockerQuantity;
    }

    public String getLockerSerialNumber() {
        return lockerSerialNumber;
    }

    public void setLockerSerialNumber(String lockerSerialNumber) {
        this.lockerSerialNumber = lockerSerialNumber;
    }

    public String getDirtyLockerModel() {
        return dirtyLockerModel;
    }

    public void setDirtyLockerModel(String dirtyLockerModel) {
        this.dirtyLockerModel = dirtyLockerModel;
    }

    public Integer getDirtyLockerQuantity() {
        return dirtyLockerQuantity;
    }

    public void setDirtyLockerQuantity(Integer dirtyLockerQuantity) {
        this.dirtyLockerQuantity = dirtyLockerQuantity;
    }

    public String getDirtyLockerSerialNumber() {
        return dirtyLockerSerialNumber;
    }

    public void setDirtyLockerSerialNumber(String dirtyLockerSerialNumber) {
        this.dirtyLockerSerialNumber = dirtyLockerSerialNumber;
    }

    public String getProtocolUsageMonths() {
        return protocolUsageMonths;
    }

    public void setProtocolUsageMonths(String protocolUsageMonths) {
        this.protocolUsageMonths = protocolUsageMonths;
    }

    public String getWeeklySchedule() {
        return weeklySchedule;
    }

    public void setWeeklySchedule(String weeklySchedule) {
        this.weeklySchedule = weeklySchedule;
    }

    public String getWeeklyWashDate() {
        return weeklyWashDate;
    }

    public void setWeeklyWashDate(String weeklyWashDate) {
        this.weeklyWashDate = weeklyWashDate;
    }

    public Integer getWeeklyWashFrequency() {
        return weeklyWashFrequency;
    }

    public void setWeeklyWashFrequency(Integer weeklyWashFrequency) {
        this.weeklyWashFrequency = weeklyWashFrequency;
    }

    public String getProductWashInfo() {
        return productWashInfo;
    }

    public void setProductWashInfo(String productWashInfo) {
        this.productWashInfo = productWashInfo;
    }

    public BigDecimal getSingleWashRentalFee() {
        return singleWashRentalFee;
    }

    public void setSingleWashRentalFee(BigDecimal singleWashRentalFee) {
        this.singleWashRentalFee = singleWashRentalFee;
    }

    public BigDecimal getWeeklyWashRentalFee() {
        return weeklyWashRentalFee;
    }

    public void setWeeklyWashRentalFee(BigDecimal weeklyWashRentalFee) {
        this.weeklyWashRentalFee = weeklyWashRentalFee;
    }

    public BigDecimal getClothingSettlementPrice() {
        return clothingSettlementPrice;
    }

    public void setClothingSettlementPrice(BigDecimal clothingSettlementPrice) {
        this.clothingSettlementPrice = clothingSettlementPrice;
    }

    public BigDecimal getClothingResidualValue() {
        return clothingResidualValue;
    }

    public void setClothingResidualValue(BigDecimal clothingResidualValue) {
        this.clothingResidualValue = clothingResidualValue;
    }

    public Integer getClothingLossCount() {
        return clothingLossCount;
    }

    public void setClothingLossCount(Integer clothingLossCount) {
        this.clothingLossCount = clothingLossCount;
    }

    public BigDecimal getClothingLossFee() {
        return clothingLossFee;
    }

    public void setClothingLossFee(BigDecimal clothingLossFee) {
        this.clothingLossFee = clothingLossFee;
    }

    public BigDecimal getMonthlyTotalCost() {
        return monthlyTotalCost;
    }

    public void setMonthlyTotalCost(BigDecimal monthlyTotalCost) {
        this.monthlyTotalCost = monthlyTotalCost;
    }

    public Date getProtocolStartDate() {
        return protocolStartDate;
    }

    public void setProtocolStartDate(Date protocolStartDate) {
        this.protocolStartDate = protocolStartDate;
    }

    public Date getProtocolEndDate() {
        return protocolEndDate;
    }

    public void setProtocolEndDate(Date protocolEndDate) {
        this.protocolEndDate = protocolEndDate;
    }

    public Date getResignationDate() {
        return resignationDate;
    }

    public void setResignationDate(Date resignationDate) {
        this.resignationDate = resignationDate;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCountInfo() {
        return countInfo;
    }

    public void setCountInfo(String countInfo) {
        this.countInfo = countInfo;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    public String getOldValue() {
        return oldValue;
    }

    public void setOldValue(String oldValue) {
        this.oldValue = oldValue;
    }

    public String getNewValue() {
        return newValue;
    }

    public void setNewValue(String newValue) {
        this.newValue = newValue;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
