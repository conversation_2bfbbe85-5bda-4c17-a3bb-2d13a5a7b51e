package com.rutong.platform.device.controller;

import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.device.entity.DevRecordSorting;
import com.rutong.platform.device.entity.DevSorting;
import com.rutong.platform.device.service.DevRecordSortingService;
import com.rutong.platform.device.service.SortingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/device/recordSorting")
@LogDesc(title = "分拣记录")
public class DevRecordSortingController extends CrudController<DevRecordSorting> {

    @Autowired
    private DevRecordSortingService devRecordSortingService;

    @Override
    public BaseService<DevRecordSorting> getService() {
        return devRecordSortingService;
    }

}
