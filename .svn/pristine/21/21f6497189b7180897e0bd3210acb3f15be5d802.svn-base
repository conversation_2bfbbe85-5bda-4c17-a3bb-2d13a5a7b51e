package com.rutong.platform.device.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

@Data
public class DevRecordSortingExcel {
    @ExcelProperty("序号")
    private Integer index;
    @ExcelProperty("客户名称")
    private String clientName;
    @ExcelProperty("分拣时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date sortingTime;
    @ExcelProperty("分拣人")
    private String sortingMan;
    @ExcelProperty("格口")
    private String cabNo;
    @ExcelProperty("分拣编号")
    private String sortingno;
    @ExcelProperty("员工姓名")
    private String employeeName;
    @ExcelProperty("员工电话")
    private String employeePhone;
    @ExcelProperty("部门")
    private String department;
    @ExcelProperty("分部门")
    private String subDepartment;
    @ExcelProperty("配置品类")
    private String configurationCategory;
    @ExcelProperty("产品款号")
    private String productModelNumber;
    @ExcelProperty("产品颜色")
    private String productColor;
    @ExcelProperty("LOGO位置")
    private String logoPosition;
    @ExcelProperty("产品规格")
    private String productSpecification;
    @ExcelProperty("员工企业工号")
    private String employeeId;
    @ExcelProperty("RFID标签号")
    private String rfidTagNumber;
    @ExcelProperty("产品序列号")
    private String productSerialNumber;
} 