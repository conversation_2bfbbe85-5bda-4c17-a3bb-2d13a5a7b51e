package com.rutong.platform.washing.service;

import com.rutong.framework.constant.GlobalContsant;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.washing.entity.WashingRecord;
import com.rutong.platform.washing.entity.WashingTask;
import com.rutong.platform.wms.entity.WmsDelivery;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import com.rutong.platform.wms.service.WmsDeliveryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class WashingRecordService extends BaseService<WashingRecord> {
    
    private static final Logger log = LoggerFactory.getLogger(WashingRecordService.class);

    @Autowired
    private WmsDeliveryService wmsDeliveryService;
    
    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;
    
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;

    /**
     * 根据洗涤时间范围查询洗涤记录
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 洗涤记录列表
     */
    public List<WashingRecord> findByWashingTimeBetween(Date startTime, Date endTime) {
        String sql = "FROM WashingRecord WHERE washingTime >= ?0 AND washingTime <= ?1 ORDER BY washingTime DESC";
        return dao.executeQuery(sql, WashingRecord.class, startTime, endTime);
    }
    
    /**
     * 查询所有洗涤记录并按照洗涤时间倒序排序
     * @return 洗涤记录列表
     */
    public List<WashingRecord> findAllOrderByWashingTimeDesc() {
        String sql = "FROM WashingRecord ORDER BY washingTime DESC";
        return dao.executeQuery(sql, WashingRecord.class);
    }
    
    /**
     * 根据多个条件查询洗涤记录
     * @param clientName 客户名称
     * @param employeeName 员工姓名
     * @param employeePhone 员工电话
     * @param department 部门
     * @param employeeId 员工工号
     * @param rfidTagNumber RFID标签号
     * @param rfidCode 工作服RFID标签编码
     * @param configType 配置品类
     * @param productModel 产品款号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 洗涤记录列表
     */
    public List<WashingRecord> findByMultipleConditions(
            String clientName,
            String employeeName,
            String employeePhone,
            String department,
            String employeeId,
            String rfidTagNumber,
            String rfidCode,
            String configType,
            String productModel,
            Date startTime,
            Date endTime) {
        
        // 构建动态查询
        StringBuilder sql = new StringBuilder("FROM WashingRecord WHERE 1=1");
        java.util.List<Object> params = new java.util.ArrayList<>();
        
        // 添加条件
        if (StringUtils.isNotBlank(clientName)) {
            sql.append(" AND clientName = ?").append(params.size());
            params.add(clientName);
        }
        
        if (StringUtils.isNotBlank(employeeName)) {
            sql.append(" AND employeeName LIKE ?").append(params.size());
            params.add("%" + employeeName + "%");
        }
        
        if (StringUtils.isNotBlank(employeePhone)) {
            sql.append(" AND employeePhone LIKE ?").append(params.size());
            params.add("%" + employeePhone + "%");
        }
        
        if (StringUtils.isNotBlank(department)) {
            sql.append(" AND department LIKE ?").append(params.size());
            params.add("%" + department + "%");
        }
        
        if (StringUtils.isNotBlank(employeeId)) {
            sql.append(" AND employeeId LIKE ?").append(params.size());
            params.add("%" + employeeId + "%");
        }
        
        if (StringUtils.isNotBlank(rfidTagNumber)) {
            sql.append(" AND rfidTagNumber LIKE ?").append(params.size());
            params.add("%" + rfidTagNumber + "%");
        }
        
        // 新增条件：工作服RFID标签编码
        /*if (StringUtils.isNotBlank(rfidCode)) {
            sql.append(" AND rfidCode LIKE ?").append(params.size());
            params.add("%" + rfidCode + "%");
        }*/
        
        // 新增条件：配置品类
        if (StringUtils.isNotBlank(configType)) {
            sql.append(" AND configType LIKE ?").append(params.size());
            params.add("%" + configType + "%");
        }
        
        // 新增条件：产品款号
        if (StringUtils.isNotBlank(productModel)) {
            sql.append(" AND productModel LIKE ?").append(params.size());
            params.add("%" + productModel + "%");
        }
        
        // 时间范围
        if (startTime != null) {
            sql.append(" AND washingTime >= ?").append(params.size());
            params.add(startTime);
        }
        
        if (endTime != null) {
            sql.append(" AND washingTime <= ?").append(params.size());
            params.add(endTime);
        }
        
        // 按洗涤时间倒序排序
        sql.append(" ORDER BY washingTime DESC");
        
        return dao.executeQuery(sql.toString(), WashingRecord.class, params.toArray());
    }

    /**
     * 根据脏衣清点机001生成洗涤记录
     * @param delivery 交接记录
     */
    @Transactional
    public void generateWashingRecordsFromDelivery(WmsDelivery delivery) {
        //if (GlobalContsant.CLOTHING_WASH_TYPE_2.equals(delivery.getRemark())) {
        if (GlobalContsant.CLOTHING_WASH_TYPE_7.equals(delivery.getRemark())) {
            // 查找该交接记录下的所有服装明细
            List<WmsDeliveryDetail> details = wmsDeliveryDetailService.findByDeliveryId(delivery.getId());
            
            // 为每个服装明细创建一条洗涤记录
            for (WmsDeliveryDetail detail : details) {
                createWashingRecord(delivery, detail);
            }
        }
    }

    /**
     * 创建单条洗涤记录
     * @param delivery 交接记录
     * @param detail 交接明细
     */
    private void createWashingRecord(WmsDelivery delivery, WmsDeliveryDetail detail) {
        WashingRecord record = new WashingRecord();
        
        // 首先尝试通过RFID标签号查询EmployeeProductConfiguration
        EmployeeProductConfiguration config = null;
        if (StringUtils.isNotBlank(detail.getRfidTagNumber())) {
            config = employeeProductConfigurationService.byRfid(detail.getRfidTagNumber());
        }
        
        // 如果通过RFID标签号没找到，尝试通过产品序列号查询
        if (config == null && StringUtils.isNotBlank(detail.getProductSerialNumber())) {
            config = employeeProductConfigurationService.byProductSerialNumber(detail.getProductSerialNumber());
        }
        
        // 填充洗涤记录信息
        // 设置客户名称
        // 修改客户名称的获取方式：优先从EmployeeProductConfiguration中获取
        if (config != null && StringUtils.isNotBlank(config.getEnterpriseName())) {
            record.setClientName(config.getEnterpriseName());
            log.info("从EmployeeProductConfiguration获取客户名称: {}", config.getEnterpriseName());
        } else {
            // 如果在EmployeeProductConfiguration中找不到，则使用原来的方式
            record.setClientName(delivery.getClientInfo() != null ? delivery.getClientInfo().getEnterpriseName() : "");
            log.info("使用WmsDelivery的客户名称: {}", record.getClientName());
        }

        //设置员工姓名
        record.setEmployeeName(detail.getEmployeeName());

        //设置洗涤时间
        record.setWashingTime(delivery.getCreateTime()); // 使用交接记录的创建时间作为洗涤时间
        
        //设置员工电话
        if (config != null && StringUtils.isNotBlank(config.getEmployeePhone())) {
            record.setEmployeePhone(config.getEmployeePhone());
            log.info("从EmployeeProductConfiguration获取员工电话: {}", config.getEmployeePhone());
        } else if(StringUtils.isNotBlank(detail.getEmployeePhone())){
            record.setEmployeePhone(detail.getEmployeePhone());
            log.info("从WmsDeliveryDetail获取员工电话: {}", detail.getEmployeePhone());
        }
        record.setEmployeePhone("无");
        log.info("未能从EmployeeProductConfiguration获取员工电话");

        //设置部门名称
        record.setDepartment(detail.getDepartment());
        //设置子部门名称
        record.setSubDepartment(detail.getSubDepartment());
        
        //设置配置品类
        if (config != null && StringUtils.isNotBlank(config.getConfigurationCategory())) {
            record.setConfigType(config.getConfigurationCategory());
        } else {
            record.setConfigType(detail.getConfigurationCategory());
        }
        
        // 设置产品款号
        if (config != null && StringUtils.isNotBlank(config.getProductModelNumber())) {
            record.setProductModel(config.getProductModelNumber());
        } else if (StringUtils.isNotBlank(detail.getProductModelNumber())) {
            record.setProductModel(detail.getProductModelNumber());
        } else {
            record.setProductModel("无");
        }

        // 设置产品颜色
        record.setProductColor(detail.getProductColor());

        // 获取LOGO位置
        record.setLogoPosition(detail.getLogoPosition());

        // 获取产品规格
        record.setProductSpecification(detail.getProductSpecification());
        
        // 获取员工企业工号
        if (config != null && StringUtils.isNotBlank(config.getEmployeeId())) {
            record.setEmployeeId(config.getEmployeeId());
        } else if (StringUtils.isNotBlank(detail.getEmployeeId())) {
            record.setEmployeeId(detail.getEmployeeId());
        } else {
            record.setEmployeeId("无");
        }
        
        // 获取工作服RFID标签编码
        if (config != null && StringUtils.isNotBlank(config.getRfidTagNumberWork())) {
            record.setRfidCode(config.getRfidTagNumberWork());
        } else {
            record.setRfidCode(detail.getRfidTagNumberWork());
        }

        // 设置RFID标签号
        record.setRfidTagNumber(detail.getRfidTagNumber());

        //设置合同号
        record.setContractNumber(detail.getContractNumber());
        
        // 设置洗涤次数
        setWashCount(record);
        
        // 保存洗涤记录
        save(record);
    }
    
    /**
     * 更新现有洗涤记录中的员工电话、工作服RFID标签编码、配置品类、产品款号和员工企业工号等字段
     * 通过从employee_product_configuration表中查询这些信息并更新到washing_record表中
     * @return 更新的记录数量
     */
    @Transactional
    public int updateMissingFieldsFromEmployeeConfig() {
        // 查询所有洗涤记录
        List<WashingRecord> allRecords = findAll();
        int updatedCount = 0;
        
        for (WashingRecord record : allRecords) {
            boolean updated = false;
            
            // 首先尝试通过RFID标签号查询EmployeeProductConfiguration
            EmployeeProductConfiguration config = null;
            if (StringUtils.isNotBlank(record.getRfidTagNumber())) {
                config = employeeProductConfigurationService.byRfid(record.getRfidTagNumber());
            }
            
            // 如果通过RFID标签号没找到，尝试通过产品序列号(工作服RFID标签编码)查询
            if (config == null && StringUtils.isNotBlank(record.getRfidCode())) {
                config = employeeProductConfigurationService.byProductSerialNumber(record.getRfidCode());
            }
            
            if (config != null) {
                // 更新客户名称
                if (StringUtils.isNotBlank(config.getEnterpriseName())) {
                    record.setClientName(config.getEnterpriseName());
                    updated = true;
                }
                
                // 更新员工电话
                if ((StringUtils.isBlank(record.getEmployeePhone()) || "null".equals(record.getEmployeePhone())) 
                        && StringUtils.isNotBlank(config.getEmployeePhone())) {
                    record.setEmployeePhone(config.getEmployeePhone());
                    updated = true;
                }
                
                // 更新配置品类
                if ((StringUtils.isBlank(record.getConfigType()) || "null".equals(record.getConfigType())) 
                        && StringUtils.isNotBlank(config.getConfigurationCategory())) {
                    record.setConfigType(config.getConfigurationCategory());
                    updated = true;
                }
                
                // 更新产品款号
                if ((StringUtils.isBlank(record.getProductModel()) || "null".equals(record.getProductModel())) 
                        && StringUtils.isNotBlank(config.getProductModelNumber())) {
                    record.setProductModel(config.getProductModelNumber());
                    updated = true;
                }
                
                // 更新员工企业工号
                if ((StringUtils.isBlank(record.getEmployeeId()) || "null".equals(record.getEmployeeId())) 
                        && StringUtils.isNotBlank(config.getEmployeeId())) {
                    record.setEmployeeId(config.getEmployeeId());
                    updated = true;
                }
                
                // 更新工作服RFID标签编码
                if ((StringUtils.isBlank(record.getRfidCode()) || "null".equals(record.getRfidCode())) 
                        && StringUtils.isNotBlank(config.getRfidTagNumberWork())) {
                    record.setRfidCode(config.getRfidTagNumberWork());
                    updated = true;
                } else if (StringUtils.isNotBlank(record.getRfidCode()) && 
                           record.getRfidCode().equals(config.getProductSerialNumber())) {
                    // 如果当前rfidCode是产品序列号，则更新为正确的工作服RFID标签编码
                    if (StringUtils.isNotBlank(config.getRfidTagNumberWork())) {
                        record.setRfidCode(config.getRfidTagNumberWork());
                        updated = true;
                    } else if (StringUtils.isNotBlank(config.getRfidTagNumber())) {
                        record.setRfidCode(config.getRfidTagNumber());
                        updated = true;
                    }
                }
                
                // 如果有更新，保存记录
                if (updated) {
                    update(record);
                    updatedCount++;
                }
            } else {
                log.debug("未找到匹配的EmployeeProductConfiguration记录，洗涤记录ID: {}, RFID: {}", record.getId(), record.getRfidTagNumber());
            }
        }

        return updatedCount;
    }


    /**
     * 根据工作服RFID标签编码查询总洗涤次数
     * @param rfidCode 工作服RFID标签编码
     * @return 洗涤次数
     */
    public int countByRfidCode(String rfidCode) {
        // 判断rfidCode是否为空或空白
        if(StringUtils.isBlank(rfidCode)){
            // 如果为空或空白，则返回0
            return 0;
        }
        // 统计washing_record表中rfid_code等于指定值的记录数量。
        // 其中?0是一个占位符，将在执行时被传入的rfidCode参数替换
        String sql = "SELECT COUNT(*) \n" +
                "FROM wms_delivery_detail \n" +
                "WHERE rfid_tag_number_work = ?0 \n" +
                "AND type = '脏衣清点机001'";
        //使用dao对象的countSQLQuery方法，传入sql和rfidCode参数，获取查询结果
        BigInteger count = dao.countSQLQuery(sql, rfidCode);
        //如果count不为null，则返回count的整数值，否则返回0
        return count != null ? count.intValue() : 0;
    }

    /**
     * 根据工作服RFID标签编码查询所有的洗涤记录
     * @param rfidCode 工作服标签编码
     * @return 洗涤记录列表
     */
    public List<WashingRecord> findByRfidCode(String rfidCode) {
       //判断rfidCode是否为空或者是空白
        if(StringUtils.isBlank(rfidCode)){
            //返回一个新的ArrayList对象
            return new ArrayList<>();
        }
        String sql = "FROM WashingRecord WHERE rfidCode = ?0 ORDER BY washingTime DESC";
        //执行查询语句，返回查询结果
        return dao.executeQuery(sql,WashingRecord.class,rfidCode);
    }

    /**
     * 设置洗涤记录的洗涤次数
     * @param record 洗涤记录
     */
    private void setWashCount(WashingRecord record) {
        if (record != null && StringUtils.isNotBlank(record.getRfidCode())) {
            int count = countByRfidCode(record.getRfidCode());
            record.setWashCount(count + 1); // 加1是因为当前记录还未保存
        }
    }


    /**
     * 更新所有洗涤记录的洗涤次数
     * @return 更新的记录数量
     */
   @Transactional
    public int updateAllWashCount() {
        // 查询所有洗涤记录
        List<WashingRecord> allRecords = findAll();
        int updatedCount = 0;
        
        log.info("开始更新所有洗涤记录的洗涤次数，共有{}条记录需要处理", allRecords.size());
        
        // 首先统计每个RFID编码的洗涤次数
        Map<String, Integer> rfidCountMap = new HashMap<>();
        for (WashingRecord record : allRecords) {
            if (StringUtils.isNotBlank(record.getRfidCode())) {
                rfidCountMap.put(record.getRfidCode(), rfidCountMap.getOrDefault(record.getRfidCode(), 0) + 1);
            }
        }
        
        // 然后更新每条记录的洗涤次数
        for (WashingRecord record : allRecords) {
            if (StringUtils.isNotBlank(record.getRfidCode()) && rfidCountMap.containsKey(record.getRfidCode())) {
                Integer count = rfidCountMap.get(record.getRfidCode());
                if (count != null && !count.equals(record.getWashCount())) {
                    record.setWashCount(count);
                    update(record);
                    updatedCount++;
                }
            }
        }
        
        log.info("完成更新所有洗涤记录的洗涤次数，共更新了{}条记录", updatedCount);
        return updatedCount;
    }

    /**
     * 根据洗涤时间和合同编号统计工作服品类及其洗涤次数
     * @param washingTime 洗涤时间
     * @param contractNumber 合同编号
     * @return 工作服品类及其洗涤次数的映射
     */
    public Map<String, Integer> countByWashingTimeAndContractNumber(Date washingTime, String contractNumber) {
        //根据洗涤时间和客户名称查询洗涤记录
        List<WashingRecord> records = findByMultipleConditions(washingTime, contractNumber);

        //新建一个Map，用于存放工作服品类及其洗涤次数
        Map<String, Integer> statistics = new HashMap<>();

        //对records进行遍历
        for (WashingRecord record : records) {
            String workWearConfigurationCategory = record.getWorkWearConfigurationCategory();
            //如果工作服品类为空，则使用"未知"代替
            if (StringUtils.isBlank(workWearConfigurationCategory)) {
                workWearConfigurationCategory = "未知";
            }
            //统计结果
            statistics.put(workWearConfigurationCategory,statistics.getOrDefault(workWearConfigurationCategory,0) + 1);
        }
        return statistics;
    }

    /**
     * 根据工作服品类范围统计工作服品类及其洗涤次数
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param contractNumber 合同编号
     * @return 工作服品类及其洗涤次数的映射
     */
    public Map<String, Integer> countByWorkWearConfigurationCategoryRange(Date startTime, Date endTime, String contractNumber) {
        //根据洗涤时间范围和客户名称查询洗涤记录
        List<WmsDeliveryDetail> records = findByMultipleConditionsRange(startTime, endTime, contractNumber);
        //新建一个Map，用于存放工作服品类及其洗涤次数
        Map<String, Integer> statistics = new HashMap<>();
        //对records进行遍历
        for (WmsDeliveryDetail record : records) {
            String workWearConfigurationCategory = record.getWorkWearConfigurationCategory();
            //如果工作服品类为空，则使用"未知"代替
            if (StringUtils.isBlank(workWearConfigurationCategory)) {
                workWearConfigurationCategory = "未知";
            }
            statistics.put(workWearConfigurationCategory,statistics.getOrDefault(workWearConfigurationCategory,0) + 1);
        }
        return statistics;

    }

    /**
     * 根据洗涤时间和合同编号范围查询洗涤记录
     * @param startTime 開始时间
     * @param endTime 結束时间
     * @param contractNumber 合同编号
     * @return 洗涤记录列表
     */
    private List<WmsDeliveryDetail> findByMultipleConditionsRange(Date startTime, Date endTime, String contractNumber) {
        //初始化SQL语句
        String sql = "FROM WmsDeliveryDetail WHERE 1=1";
        //新建一个参数列表
        List<Object> params = new ArrayList<>();
        // 时间范围
        if (startTime != null) {
            sql = sql + " AND createTime >= ?" + params.size();
            params.add(startTime);
        }

        if (endTime != null) {
            // 设置为当天的最后一毫秒
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endTime);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            Date endOfDay = calendar.getTime();
            
            sql = sql + " AND createTime <= ?" + params.size();
            params.add(endOfDay);
        }
        // 如果合同编号不为空，则添加合同编号条件
        if (StringUtils.isNotBlank(contractNumber)) {
            sql = sql + " AND contractNumber = ?" + params.size();
            params.add(contractNumber);
        }
        //设置type为脏衣清点机001
        sql = sql + " AND type = ?" + params.size();
        params.add("脏衣清点机001");
        // 调用dao的executeQuery方法，执行查询并返回结果
        return dao.executeQuery(sql, WmsDeliveryDetail.class, params.toArray());
    }


    /**
     * 根据洗涤时间和合同编号查询洗涤记录
     * @param washingTime 洗涤时间
     * @param contractNumber 合同编号
     * @return 洗涤记录列表
     */
    public List<WashingRecord> findByMultipleConditions(Date washingTime, String contractNumber) {
        //初始化SQL语句
        String sql = "FROM WashingRecord WHERE 1=1";

        //新建一个参数列表
        List<Object> params = new ArrayList<>();

        //如果合同编号不为空，则添加合同编号条件
        if(StringUtils.isNotBlank(contractNumber)){
            // 将contractNumber参数添加到sql语句中，params.size()的作用是获取当前参数列表的大小
            sql = sql + " AND contractNumber = ?" + params.size();
            // 将clientName参数添加到参数列表中
            params.add(contractNumber);
        }
        //如果洗涤时间不为空，则添加洗涤时间条件
        if(washingTime != null){
            // 将washingTime参数添加到sql语句中，params.size()的作用是获取当前参数列表的大小
            sql = sql + " AND washingTime >= ?" + params.size();
            params.add(washingTime);

            // 使用Calendar类来操作日期，计算当天结束时间
            Calendar calendar = Calendar.getInstance();
            //设置时间
            calendar.setTime(washingTime);
            //使用Calendar类来操作日期，计算当天结束时间
            calendar.add(Calendar.DATE, 1);
            //计算当天的结束时间（第二天的00:00:00减去1毫秒）
            calendar.add(Calendar.MILLISECOND, -1);
            //计算当天的结束时间
            Date endOfDay = calendar.getTime();

            // 添加当天结束时间条件
            sql = sql + " AND washingTime <= ?" + params.size();
            params.add(endOfDay);
        }

        // 调用dao的executeQuery方法，执行查询并返回结果
        return dao.executeQuery(sql, WashingRecord.class, params.toArray());
    }


}