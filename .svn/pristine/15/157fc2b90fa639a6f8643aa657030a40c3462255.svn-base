package com.rutong.platform.common.service;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.core.dao.Dao;
import com.rutong.framework.core.exception.DaoException;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.BeanUtils;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.framework.utils.StringUtils;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.common.entity.TreeEntity;

@Transactional
public abstract class BaseService<T extends BaseEntity> {
	@Resource
	protected Dao dao;

	@SuppressWarnings("unchecked")
	public Class<T> getPersistentClass() {
		Type superClass = getClass().getGenericSuperclass();
		if (superClass instanceof ParameterizedType) {
			Type[] actualTypeArguments = ((ParameterizedType) superClass).getActualTypeArguments();
			if (actualTypeArguments.length > 0) {
				Class<T> type = (Class<T>) actualTypeArguments[0];
				return type;
			}
		}
		return null;
	}

	public T findById(String id) {
		return dao.findById(getPersistentClass(), id);
	}

	public void save(T t) {
		LoginUser loginUser = SecurityUtils.getLoginUser();
		t.setCreateBy(loginUser.getUsername());
		t.setCreateTime(new Date());
		dao.saveOrUpdate(t);
	}

	public T update(T t) {
		T object = findById(t.getId());
		BeanUtils.copyProperties(object, t);
		LoginUser loginUser = SecurityUtils.getLoginUser();
		object.setUpdateBy(loginUser.getUsername());
		object.setUpdateTime(new Date());
		dao.saveOrUpdate(object);
		return object;
	}

	public PageInfo<T> findAllByPage(GridParams gridParams, List<FilterCondition> filters) {
		Long count = dao.count(getPersistentClass(), filters);
		List<T> dataList = new ArrayList<T>();
		if (count > 0L) {
			dataList = dao.findAll(getPersistentClass(), gridParams, filters);
		}
		return new PageInfo<T>(count, dataList);
	}

	public void delete(T entity) {
		dao.delete(findById(entity.getId()));
	}

	public PageInfo<T> findAllByTree(GridParams gridParams, List<FilterCondition> filters){
		return null;
	}

}
