package com.rutong.platform.sys.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;
@Entity
@Table(name = "sys_user_role")
public class SysUserRole implements Serializable{
	private static final long serialVersionUID = 3028831492002547679L;

	public static final String ROLE_ID = "roleid";
	public static final String USER_ID = "userid";

	@Id
	@GenericGenerator(name = "generator", strategy = "org.hibernate.id.UUIDGenerator")
	@GeneratedValue(generator = "generator")
	@Column(unique = true, nullable = false, length = 40)
	private String id;
	
	@Column(nullable = false,length = 40)
	private String userid;
	
	@Column(nullable = false,length = 40)
	private String roleid;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUserid() {
		return userid;
	}

	public void setUserid(String userid) {
		this.userid = userid;
	}

	public String getRoleid() {
		return roleid;
	}

	public void setRoleid(String roleid) {
		this.roleid = roleid;
	}
	
}
