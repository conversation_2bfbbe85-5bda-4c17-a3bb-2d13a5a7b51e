package com.rutong.platform.wms.service;

import com.rutong.framework.utils.StringUtils;
import org.hibernate.query.Query;
import org.springframework.stereotype.Service;

import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class WmsDeliveryDetailService extends BaseService<WmsDeliveryDetail> {

    public List<WmsDeliveryDetail> findByContractNumber(String contractNumber, String type, String date) {
        return dao.findByProperty(WmsDeliveryDetail.class, "contractNumber", contractNumber, "type", type, "batchNo", date, "status", 0);
    }

    public List<WmsDeliveryDetail> findByContractNumberAndProductSerialNumber(String contractNumber, String productSerialNumber, String type) {
        return dao.findByProperty(WmsDeliveryDetail.class, "contractNumber", contractNumber, "productSerialNumber", productSerialNumber, "type", type);
    }

    // 根据订单ID和备注查询
    public List<WmsDeliveryDetail> findByOrderIdAndRemark(String orderId, String type) {
        String sql = "FROM WmsDeliveryDetail WHERE orderId = ?0  and type = ?1";
        return dao.executeQuery(sql, WmsDeliveryDetail.class, orderId, type);
    }


    public List<WmsDeliveryDetail> findByDeliveryId(String delivery_id) {
        String sql = "FROM WmsDeliveryDetail WHERE delivery_id = ?0 ";
        return dao.executeQuery(sql, WmsDeliveryDetail.class, delivery_id);
    }

    public List<WmsDeliveryDetail> findByDeliveryIdAndRemark(String delivery_id, String type) {
        String sql = "FROM WmsDeliveryDetail WHERE delivery_id = ?0 and type = ?1 ";
        return dao.executeQuery(sql, WmsDeliveryDetail.class, delivery_id, type);
    }

    public List<WmsDeliveryDetail> findByRfid(String rfid, String date) {
        return dao.findByProperty(WmsDeliveryDetail.class, "rfidTagNumber", rfid, "batchNo", date);
    }

    public void updateInfo(WmsDeliveryDetail wmsDeliveryDetail) {
        //调用了dao.persist(wmsDeliveryDetail)，
        // 其功能是将传入的WmsDeliveryDetail实体对象持久化到数据库中。
        // persist方法会根据实体的状态决定是插入新记录还是更新已有记录。
        dao.persist(wmsDeliveryDetail);
    }

    public long countByUserAndDate(LoginUser loginUser, Date date) {
        List<FilterCondition> filterConditions = new ArrayList<FilterCondition>();
        FilterCondition filterCondition = new FilterCondition();
        filterCondition.setOperator(FilterCondition.EQ);
        filterCondition.setProperty(BaseEntity.FIELD_OWNER_USER_ID);
        filterCondition.setValue(loginUser.getUserId());
        filterConditions.add(filterCondition);
        FilterCondition filterCondition2 = new FilterCondition();
        filterCondition2.setOperator(FilterCondition.GT);
        filterCondition2.setProperty(BaseEntity.FIELD_CREATE_TIME);
        filterCondition2.setValue(date);
        filterConditions.add(filterCondition2);
        return dao.count(WmsDeliveryDetail.class, filterConditions);
    }

    /**
     * 按照 rfidTagNumberWork 分组查询，并返回每组的计数
     *
     * @param gridParams 分页参数
     * @param filters    过滤条件
     * @param sorts      排序条件
     * @return 分组查询结果，包含计数信息
     */
    public PageInfo<WmsDeliveryDetail> findAllByPageGroupWithCount(GridParams gridParams,
                                                                   List<FilterCondition> filters,
                                                                   List<SortCondition> sorts) {
        StringBuilder sqlBuilder = new StringBuilder();
        StringBuilder countSqlBuilder = new StringBuilder();
        List<Object> parameters = new ArrayList<>();
        List<Object> countParameters = new ArrayList<>();

        sqlBuilder.append("SELECT d, COUNT(d.rfidTagNumberWork)  as rfidTagNumberWorkCount FROM WmsDeliveryDetail d ");
        countSqlBuilder.append("SELECT COUNT(DISTINCT d.rfidTagNumberWork) FROM WmsDeliveryDetail d ");

        if (filters != null && !filters.isEmpty()) {
            sqlBuilder.append("WHERE ");
            countSqlBuilder.append("WHERE ");

            for (int i = 0; i < filters.size(); i++) {
                FilterCondition filter = filters.get(i);
                if (i > 0) {
                    sqlBuilder.append(" AND ");
                    countSqlBuilder.append(" AND ");
                }

                String property = filter.getProperty();
                String operator = filter.getOperator();
                Object value = filter.getValue();
                // 日期类型转换
                if (isDateField(property)) {
                    value = convertToDate(value);
                }
                String columnName = "d." + property;

                appendCondition(sqlBuilder, columnName, operator, value, parameters, i + 1);

                appendCondition(countSqlBuilder, columnName, operator, value, countParameters, i + 1);
            }
        }

        // 添加分组
        sqlBuilder.append(" GROUP BY d.rfidTagNumberWork ");

        // 构建ORDER BY条件
        if (sorts != null && !sorts.isEmpty()) {
            sqlBuilder.append("ORDER BY ");
            for (int i = 0; i < sorts.size(); i++) {
                SortCondition sort = sorts.get(i);
                if (i > 0) {
                    sqlBuilder.append(", ");
                }
                String columnName = sort.getProperty();
                sqlBuilder.append(columnName).append(" ").append(sort.getDirection());
            }
        } else {
            // 默认按创建时间倒序排序
            sqlBuilder.append("ORDER BY d.createTime DESC ");
        }

        // 执行查询获取总数
        Long count = 0L;
        if (!countParameters.isEmpty()) {
            Query countQuery = dao.getCurrentSession().createQuery(countSqlBuilder.toString());
            for (int i = 0; i < countParameters.size(); i++) {
                countQuery.setParameter(i + 1, countParameters.get(i));
            }
            count = ((Number) countQuery.getSingleResult()).longValue();
        } else {
            Query countQuery = dao.getCurrentSession().createQuery(countSqlBuilder.toString());
            count = ((Number) countQuery.getSingleResult()).longValue();
        }

        // 执行查询获取数据
        List<WmsDeliveryDetail> dataList = new ArrayList<>();
        if (count > 0L) {
            Query query = dao.getCurrentSession().createQuery(sqlBuilder.toString());

            for (int i = 0; i < parameters.size(); i++) {
                query.setParameter(i + 1, parameters.get(i));
            }

            if (gridParams != null) {
                query.setFirstResult(gridParams.getStart());
                query.setMaxResults(gridParams.getPageSize());
            }
            List<Object[]> results = query.getResultList();
            for (Object[] result : results) {
                WmsDeliveryDetail detail = (WmsDeliveryDetail) result[0];
                detail.setRfidTagNumberWorkCount(((Number)result[1]).longValue());
                dataList.add(detail);
            }
        }
        return new PageInfo<>(count, dataList);
    }
    // 判断是否是日期字段
    private boolean isDateField(String property) {
        return property.toLowerCase().contains("time")
                || property.toLowerCase().contains("date");
    }

    // 转换日期参数
    private Date convertToDate(Object value) {
        if (value == null) return null;
        if (value instanceof Date) return (Date) value;

        try {
            // 支持多种日期格式
            String strValue = value.toString();
            if (strValue.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(strValue);
            } else if (strValue.matches("\\d{4}-\\d{2}-\\d{2}")) {
                return new SimpleDateFormat("yyyy-MM-dd").parse(strValue);
            }
            throw new IllegalArgumentException("Unsupported date format: " + strValue);
        } catch (ParseException e) {
            throw new IllegalArgumentException("Invalid date value: " + value);
        }
    }
    // 修改后的辅助方法：使用带序号的参数占位符
    private void appendCondition(StringBuilder builder, String columnName,
                                 String operator, Object value,
                                 List<Object> parameters, int paramIndex) {
        if (FilterCondition.EQ.equals(operator)) {
            builder.append(columnName).append(" = ?").append(paramIndex);
            parameters.add(value);
        } else if (FilterCondition.LIKE.equals(operator)) {
            builder.append(columnName).append(" LIKE ?").append(paramIndex);
            parameters.add("%" + value + "%");
        } else if (FilterCondition.GT.equals(operator)) {
            builder.append(columnName).append(" > ?").append(paramIndex);
            parameters.add(value);
        } else if (FilterCondition.LT.equals(operator)) {
            builder.append(columnName).append(" < ?").append(paramIndex);
            parameters.add(value);
        } else if (FilterCondition.GE.equals(operator)) {
            builder.append(columnName).append(" >= ?").append(paramIndex);
            parameters.add(value);
        } else if (FilterCondition.LE.equals(operator)) {
            builder.append(columnName).append(" <= ?").append(paramIndex);
            parameters.add(value);
        }
    }

}
