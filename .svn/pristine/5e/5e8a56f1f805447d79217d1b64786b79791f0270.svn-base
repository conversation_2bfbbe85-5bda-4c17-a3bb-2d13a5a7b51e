package com.rutong.platform.sys.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysOperLog;
import com.rutong.platform.sys.service.SysOperLogService;

@RestController
@RequestMapping(value = "/system/operlog")
public class SysOperlogController extends CrudController<SysOperLog> {
	
	@Autowired
	private SysOperLogService operLogService;

	@Override
	public BaseService<SysOperLog> getService() {
		return operLogService;
	}

}
