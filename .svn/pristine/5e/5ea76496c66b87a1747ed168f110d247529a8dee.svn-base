package com.rutong.platform.app.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.ResultBean;
import com.rutong.platform.app.utils.AppConstan;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.device.entity.DevInCabinet;
import com.rutong.platform.device.entity.DevRecordSorting;
import com.rutong.platform.device.entity.DevSorting;
import com.rutong.platform.device.service.DevRecordSortingService;
import com.rutong.platform.device.service.InCabinetService;
import com.rutong.platform.device.service.SortingService;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;

@RestController
@RequestMapping(value = "/unauth")
public class UnauthController {
    @Autowired
    private InCabinetService inCabinetService;
    @Autowired
    private SortingService devSortingService;
    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;
    @Autowired
    private DevRecordSortingService devRecordSortingService;

    // 根据主机号和列号查询货架
    @GetMapping("/getShelfByHostSid.do")
    public ResultBean getShelfByHostSid(String host, Integer sid) {
        DevInCabinet cabinet = inCabinetService.findByHostAndSid(host, sid);
        if (cabinet == null) {
            return ResultBean.error();
        }
        if (cabinet.getType().equals(AppConstan.DEVICE_TYPE_NO)) {
            // 按列号查询
            List<DevInCabinet> caList = inCabinetService.findByCol(cabinet.getCol());
            return ResultBean.success(caList);
        }
        return ResultBean.success();
    }

    /**
     * 产线分拣格口
     */
    @GetMapping("/productionLineSorting.do")
    public ResultBean productionLineSorting(String ip, String rfid) {
        // 查询分拣设备
        List<DevSorting> devSorting = devSortingService.findByIpInfo(ip);
        if (devSorting == null || devSorting.isEmpty()) {
            return ResultBean.error("未找到对应IP的分拣设备", -1);
        }

        // 查询员工产品配置
        EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byRfid(rfid);
        if (employeeProductConfiguration == null) {
            return ResultBean.error("未找到RFID对应的员工产品配置", -1);
        }

        String lockerSerialNumber = employeeProductConfiguration.getLockerSerialNumber();// 更衣柜序列号

        // 查询格口信息
        DevInCabinet byCabNo = inCabinetService.findByCabNo(lockerSerialNumber);
        if (StringUtils.isEmpty(byCabNo)) {
            return ResultBean.error("没有查询到格口" + lockerSerialNumber, -1);
        }

        boolean foundMatch = false;
        for (DevSorting sorting : devSorting) {
            if (sorting.getDevno().equals(byCabNo.getSortingno())) {
                foundMatch = true;

                // 创建分拣记录并保存到数据库
//                DevRecordSorting recordSorting = new DevRecordSorting();

//                // 设置基本分拣信息
//                LoginUser loginUser = SecurityUtils.getLoginUser();
//                recordSorting.setSortingMan(loginUser.getUser().getNickname());  // 分拣人
//                recordSorting.setCabNo(lockerSerialNumber);  // 格口
//                recordSorting.setSortingno(byCabNo.getSortingno());  // 分拣编号
//
//                // 设置员工信息
//                recordSorting.setEmployeeName(employeeProductConfiguration.getEmployeeName());  // 员工姓名
//                recordSorting.setEmployeePhone(employeeProductConfiguration.getEmployeePhone());  // 员工电话
//                recordSorting.setDepartment(employeeProductConfiguration.getDepartment());  // 部门
//                recordSorting.setSubDepartment(employeeProductConfiguration.getSubDepartment());  // 分部门
//                recordSorting.setEmployeeId(employeeProductConfiguration.getEmployeeId());  // 员工企业工号
//
//                // 设置产品信息
//                recordSorting.setConfigurationCategory(employeeProductConfiguration.getConfigurationCategory());  // 配置品类
//                recordSorting.setProductModelNumber(employeeProductConfiguration.getProductModelNumber());  // 产品款号
//                recordSorting.setProductColor(employeeProductConfiguration.getProductColor());  // 产品颜色
//                recordSorting.setLogoPosition(employeeProductConfiguration.getLogoPosition());  // LOGO位置
//                recordSorting.setProductSpecification(employeeProductConfiguration.getProductSpecification());  // 产品规格
//                recordSorting.setProductSerialNumber(employeeProductConfiguration.getProductSerialNumber());  // 产品序列号
//
//                // 设置RFID和合同信息
//                recordSorting.setRfidTagNumber(rfid);  // RFID标签号
//                recordSorting.setContractNumber(employeeProductConfiguration.getContractNumber());  // 合同编号
//                recordSorting.setEnterpriseName(employeeProductConfiguration.getEnterpriseName());  // 企业名称
//
//                // 设置客户名称和分拣时间
//                recordSorting.setClientName(employeeProductConfiguration.getEnterpriseName());  // 客户名称
//                recordSorting.setSortingTime(new Date());  // 分拣时间
//
//                // 保存记录
//                devRecordSortingService.save(recordSorting);

                return ResultBean.success(byCabNo);
            }
        }

        return ResultBean.error("不是当前口分拣", -1);
    }

    // 修改灯RFID
    @PostMapping("/changeLightRfid.do")
    public ResultBean changeLightRifd(String ip, Integer sid, String rfid) {
        DevInCabinet cabinet = inCabinetService.findByHostAndSid(ip, sid);
        if (cabinet == null) {
            return ResultBean.error();
        }
        cabinet.setRfid(rfid);
        cabinet.setStatus("占用");
        inCabinetService.updateInfo(cabinet);

        return ResultBean.success();
    }

    // 修改灯
    @PostMapping("/changeLight.do")
    public ResultBean changeLight(String ip, Integer sid) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        // 创建一个用于存储键值对的HashMap实例，键为String类型，值为Object类型
        Map<String, Object> map = new HashMap<>();

        DevInCabinet cabinet = inCabinetService.findByHostAndSid(ip, sid);
        if (cabinet == null) {
            return ResultBean.error();
        }
        if ("可分配".equals(cabinet.getType())) {
            cabinet.setStatus("占用");
        } else {
            List<DevInCabinet> listOff = new ArrayList<>();
            List<DevInCabinet> list = inCabinetService.findBySortingnoAndCol(cabinet.getSortingno(), cabinet.getCol());
            //遍历list中的DevInCabinet对象
            for (DevInCabinet devInCabinet : list) {
                //若状态为“占用”或类型为“不可分配”
                if ("占用".equals(devInCabinet.getStatus()) || "不可分配".equals(devInCabinet.getType())) {
                    //则加入listOff
                    listOff.add(devInCabinet);
                }
                List<WmsDeliveryDetail> byRfid = wmsDeliveryDetailService.findByRfid(devInCabinet.getRfid(),
                        simpleDateFormat.format(new Date()));

                //遍历byRfid列表中的每个WmsDeliveryDetail对象
                for (WmsDeliveryDetail wmsDeliveryDetail : byRfid) {
                    wmsDeliveryDetail.setStatus(1L);
                    wmsDeliveryDetailService.updateInfo(wmsDeliveryDetail);
                }
                devInCabinet.setStatus("空闲");
                devInCabinet.setRfid(null);
                inCabinetService.updateInfo(devInCabinet);
            }
            //将键为"type"的值设置为"off"，将键为"data"的值设置为listOff
            map.put("type", "off");
            map.put("data", listOff);
            return ResultBean.success(map);
        }
        try {
            EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService
                    .byRfid(cabinet.getRfid());
            String lockerSerialNumber = employeeProductConfiguration.getLockerSerialNumber();
            String[] sss = lockerSerialNumber.split("-");
            String client = sss[0];
            String col = sss[1];
            List<String> list = new ArrayList<>();
            List<DevInCabinet> bySortingnoAndCol = inCabinetService.findBySortingnoAndCol(client,
                    Integer.parseInt(col));
            for (DevInCabinet devInCabinet : bySortingnoAndCol) {
                list.add(devInCabinet.getCabNo());
            }
            List<String> listOK = new ArrayList<>();

            List<WmsDeliveryDetail> wmsDeliveryDetails = wmsDeliveryDetailService.findByContractNumber(
                    employeeProductConfiguration.getContractNumber(), "(客户端)收取", simpleDateFormat.format(new Date()));
            //遍历wmsDeliveryDetails获取到每个WmsDeliveryDetail对象
            for (WmsDeliveryDetail wmsDeliveryDetail : wmsDeliveryDetails) {
                String lockerSerialNumber1 = wmsDeliveryDetail.getLockerSerialNumber();
                //如果list包含 lockerSerialNumber1
                if (list.contains(lockerSerialNumber1)) {
                    //则将 lockerSerialNumber1 添加到 listOK 中
                    listOK.add(lockerSerialNumber1);
                }
            }
            //遍历listOK
            boolean status = true;
            for (String s : listOK) {
                DevInCabinet byCabNo = inCabinetService.findByCabNo(s);
                if (byCabNo.getStatus().equals("空闲")) {
                    status = false;
                }
            }
            if (status) {
                //从inCabinetService服务中根据
                //主机IP地址(ip)、列号(col)和类型("不可分配")来查找设备信息，
                //并将结果存储在List<DevInCabinet>类型的列表list1中。
                List<DevInCabinet> list1 = inCabinetService.findByHostAndColAndType(ip, col, "不可分配");
                map.put("type", "on");
                map.put("data", list1);
                return ResultBean.success(map);
            }
        } catch (NullPointerException e) {
            return ResultBean.error();
        }
        return ResultBean.error();
    }


}
