package com.rutong.platform.wechat.service;

import java.util.concurrent.TimeUnit;
import java.util.Random;
import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.stereotype.Service;

import com.rutong.framework.core.exception.ServiceException;
import com.rutong.framework.core.exception.user.UserNotExistsException;
import com.rutong.framework.core.redis.RedisCache;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.security.service.TokenService;
import com.rutong.framework.security.service.UserDetailsServiceImpl;
import com.rutong.framework.utils.StringUtils;
import com.rutong.platform.sys.entity.SysUser;
import com.rutong.platform.sys.service.SysUserService;

/**
 * 手机号登录服务类
 */
@Service
public class PhoneLoginService {
    private static final Logger log = LoggerFactory.getLogger(PhoneLoginService.class);
    
    // Redis验证码前缀
    private static final String PHONE_VERIFY_CODE_PREFIX = "phone_verify_code:";
    
    // 验证码有效期（分钟）
    private static final int VERIFY_CODE_EXPIRE_MINUTES = 5;
    
    // 固定的验证码
    private static final String FIXED_VERIFY_CODE = "123456";
    
    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private SysUserService userService;
    
    @Autowired
    private TokenService tokenService;
    
    @Resource
    private AuthenticationManager authenticationManager;
    
    @Autowired
    private UserDetailsServiceImpl userDetailsService;



    /**
     * 发送验证码
     * 
     * @param phone 手机号
     */
    public void sendVerifyCode(String phone) {
        if (StringUtils.isEmpty(phone)) {
            throw new ServiceException("手机号不能为空");
        }
        
        // 验证手机号格式
        if (!isValidPhoneNumber(phone)) {
            throw new ServiceException("手机号格式不正确");
        }
        
        // 查询用户是否存在
        SysUser user = userService.selectUserByPhone(phone);
        if (user == null) {
            throw new UserNotExistsException();
        }
        
        // 使用固定验证码
        String verifyCode = FIXED_VERIFY_CODE;
        
        // 缓存验证码，并设置过期时间
        String cacheKey = getVerifyCodeKey(phone);
        redisCache.setCacheObject(cacheKey, verifyCode, VERIFY_CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        // 这里模拟发送短信验证码，实际应用中应该调用短信发送服务
        log.info("向手机号 {} 发送验证码: {}", phone, verifyCode);
        
        // 发送成功后可以添加限流措施，防止恶意请求
        // 例如可以记录手机号的发送次数，限制同一手机号一天内的发送次数
    }
    
    /**
     * 手机号登录
     * 
     * @param phone 手机号
     * @param verifyCode 验证码
     * @return 登录token
     */
    public String login(String phone, String verifyCode) {
        if (StringUtils.isEmpty(phone) || StringUtils.isEmpty(verifyCode)) {
            throw new ServiceException("手机号和验证码不能为空");
        }
        
        // 验证手机号格式
        if (!isValidPhoneNumber(phone)) {
            throw new ServiceException("手机号格式不正确");
        }
        
        // 查询用户是否存在
        SysUser user = userService.selectUserByPhone(phone);
        if (user == null) {
            throw new UserNotExistsException();
        }
        
        // 验证验证码 - 检查是否是固定验证码
        if (FIXED_VERIFY_CODE.equals(verifyCode)) {
            // 固定验证码验证通过
            log.info("使用固定验证码登录成功: {}", phone);
        } else {
            // 尝试从Redis获取验证码进行验证
            String cacheKey = getVerifyCodeKey(phone);
            String cachedVerifyCode = redisCache.getCacheObject(cacheKey);
            
            if (cachedVerifyCode == null) {
                throw new ServiceException("验证码已过期，请重新获取");
            }
            
            if (!verifyCode.equals(cachedVerifyCode)) {
                throw new ServiceException("验证码错误");
            }
            
            // 验证码验证成功后删除缓存的验证码
            redisCache.deleteObject(cacheKey);
        }
        
        // 使用用户名密码凭证进行身份验证
        // 这里我们使用Phone作为用户的唯一标识符进行认证
        try {
            LoginUser loginUser = (LoginUser) userDetailsService.createLoginUser(user);
            //todo redis保存toekn

            // 生成token并返回
            return tokenService.createToken(loginUser);
        } catch (Exception e) {
            log.error("手机号登录异常", e);
            throw new ServiceException("登录失败，请稍后再试");
        }
    }
    
    /**
     * 获取验证码Redis缓存键
     * 
     * @param phone 手机号
     * @return 缓存键
     */
    private String getVerifyCodeKey(String phone) {
        return PHONE_VERIFY_CODE_PREFIX + phone;
    }
    
    /**
     * 生成随机验证码
     * 
     * @param length 验证码长度
     * @return 验证码
     */
    private String generateVerifyCode(int length) {
        // 该方法已经不再使用，但保留以备将来需要
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }
    
    /**
     * 验证手机号格式
     * 
     * @param phone 手机号
     * @return 是否有效
     */
    private boolean isValidPhoneNumber(String phone) {
        // 简单的手机号格式验证，可以根据需要调整
        return phone.matches("^1[3-9]\\d{9}$");
    }
} 