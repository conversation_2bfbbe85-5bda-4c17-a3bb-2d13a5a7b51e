package com.rutong.platform.device.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.rutong.platform.common.entity.BaseEntity;

/**
 * 分拣存放柜 1000 个
 */

@Entity
@Table(name = "dev_in_cabinet")
public class DevInCabinet extends BaseEntity {

	public static final String FIELD_CABNO = "cabNo";
	public static final String FIELD_HOST = "host";
	public static final String FIELD_SID = "sid";
	public static final String FIELD_COL = "col";
	public static final String FIELD_SORTINGNO = "sortingno";

	@Column(nullable = false,unique = true)
	private String cabNo; // 衣柜编号
	@Column(nullable = false)
	private String host; //主机地址
	@Column(nullable = false)
	private int sid;
	@Column(nullable = false)
	private int col;  //列号
	@Column(nullable = false)
	private String type; //可分配，不可分配(背面按钮，不分配人员)
	@Column(nullable = false)
	private String sortingno;  //工作台编号
	private String status; // 状态： 占用/空闲
	private String rfid; // 状态： 占用/空闲

    public String getRfid() {
        return rfid;
    }

    public void setRfid(String rfid) {
        this.rfid = rfid;
    }

    public String getCabNo() {
		return cabNo;
	}

	public void setCabNo(String cabNo) {
		this.cabNo = cabNo;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getHost() {
		return host;
	}

	public void setHost(String host) {
		this.host = host;
	}

	public int getSid() {
		return sid;
	}

	public void setSid(int sid) {
		this.sid = sid;
	}

	public int getCol() {
		return col;
	}

	public void setCol(int col) {
		this.col = col;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getSortingno() {
		return sortingno;
	}

	public void setSortingno(String sortingno) {
		this.sortingno = sortingno;
	}

}
