package com.rutong.platform.wechat.entity;

import com.rutong.platform.common.entity.BaseEntity;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * 服务信息实体类
 */
@Entity
@Table(name = "wechat_service_info")
public class ServiceInfo extends BaseEntity {

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 姓名
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 企业名称
     */
    @Column(name = "company_name")
    private String companyName;

    /**
     * 年间部门
     */
    @Column(name = "department")
    private String department;

    /**
     * 员工工号
     */
    @Column(name = "employee_id")
    private String employeeId;

    /**
     * 更衣柜/列号
     */
    @Column(name = "locker_id")
    private String lockerId;
    
    /**
     * 配置品类
     */
    @Column(name = "configuration_category")
    private String configurationCategory;
    
    /**
     * 产品款号
     */
    @Column(name = "product_id")
    private String productId;
    
    /**
     * 收件时间
     */
    @Column(name = "receive_time")
    private Date receiveTime;
    
    /**
     * 送件时间
     */
    @Column(name = "delivery_time")
    private Date deliveryTime;
    
    /**
     * 提取问题
     */
    @Column(name = "collection_issue")
    private String collectionIssue;
    
    /**
     * 问题处理
     */
    @Column(name = "issue_solution")
    private String issueSolution;

    // Getters and Setters
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public String getLockerId() {
        return lockerId;
    }

    public void setLockerId(String lockerId) {
        this.lockerId = lockerId;
    }

    public String getConfigurationCategory() {
        return configurationCategory;
    }

    public void setConfigurationCategory(String configurationCategory) {
        this.configurationCategory = configurationCategory;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public String getCollectionIssue() {
        return collectionIssue;
    }

    public void setCollectionIssue(String collectionIssue) {
        this.collectionIssue = collectionIssue;
    }

    public String getIssueSolution() {
        return issueSolution;
    }

    public void setIssueSolution(String issueSolution) {
        this.issueSolution = issueSolution;
    }
} 