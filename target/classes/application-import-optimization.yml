# Excel导入性能优化配置文件
# 可以通过 spring.profiles.active=import-optimization 激活此配置

import:
  performance:
    # 批量保存的批次大小
    batch-size: 100
    
    # 历史记录批量保存的批次大小  
    history-batch-size: 100
    
    # 是否启用变更追踪
    change-tracking-enabled: true
    
    # 是否异步处理变更追踪
    async-change-tracking: true
    
    # 数据库查询的IN语句最大长度
    max-in-clause-size: 1000
    
    # 是否启用性能监控日志
    performance-logging-enabled: true
    
    # 是否跳过无变更的更新操作
    skip-no-change-updates: true
    
    # 线程池配置
    core-pool-size: 2
    max-pool-size: 4
    queue-capacity: 100

# 数据库连接池优化配置
spring:
  datasource:
    hikari:
      # 最大连接数
      maximum-pool-size: 20
      # 最小空闲连接数
      minimum-idle: 5
      # 连接超时时间（毫秒）
      connection-timeout: 30000
      # 空闲连接最大存活时间（毫秒）
      idle-timeout: 600000
      # 连接最大存活时间（毫秒）
      max-lifetime: 1800000
      # 连接测试查询
      connection-test-query: SELECT 1

# JPA/Hibernate优化配置
  jpa:
    hibernate:
      # 批量操作大小
      jdbc:
        batch_size: 100
        batch_versioned_data: true
      # 二级缓存配置
      cache:
        use_second_level_cache: false
        use_query_cache: false
    properties:
      hibernate:
        # 批量插入优化
        jdbc:
          batch_size: 100
          batch_versioned_data: true
        # 排序批量插入
        order_inserts: true
        order_updates: true
        # 批量更新优化
        batch_fetch_style: DYNAMIC

# 日志配置
logging:
  level:
    com.rutong.platform.client.service.EmployeeProductConfigurationService: DEBUG
    com.rutong.platform.client.service.ChangeTrackingService: DEBUG
    com.rutong.platform.client.service.EmployeeProductConfigurationHistoryService: DEBUG
    # Hibernate SQL日志（生产环境建议关闭）
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# 线程池配置
task:
  execution:
    pool:
      core-size: 2
      max-size: 4
      queue-capacity: 100
      thread-name-prefix: import-task-
      keep-alive: 60s
