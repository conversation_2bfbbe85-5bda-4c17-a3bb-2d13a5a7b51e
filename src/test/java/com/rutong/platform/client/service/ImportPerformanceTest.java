package com.rutong.platform.client.service;

import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.util.ImportPerformanceMonitor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 导入性能测试类
 * 用于测试和验证导入优化效果
 */
@SpringBootTest
@ActiveProfiles("test")
public class ImportPerformanceTest {

    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;

    @Autowired
    private ImportPerformanceMonitor performanceMonitor;

    /**
     * 测试小批量数据导入性能
     */
    @Test
    @Transactional
    public void testSmallBatchImport() {
        System.out.println("=== 小批量导入性能测试 (100条记录) ===");
        
        List<EmployeeProductConfiguration> testData = generateTestData(100);
        String contractNumber = "TEST_CONTRACT_001";
        
        performanceMonitor.recordImportStart(testData.size());
        
        long startTime = System.currentTimeMillis();
        Map<String, Object> result = employeeProductConfigurationService.importData(testData, contractNumber);
        long endTime = System.currentTimeMillis();
        
        performanceMonitor.recordImportEnd();
        
        System.out.println("导入结果: " + result);
        System.out.println("总耗时: " + (endTime - startTime) + "ms");
        System.out.println("平均每条记录耗时: " + (double)(endTime - startTime) / testData.size() + "ms");
        
        performanceMonitor.printPerformanceReport();
    }

    /**
     * 测试中等批量数据导入性能
     */
    @Test
    @Transactional
    public void testMediumBatchImport() {
        System.out.println("=== 中等批量导入性能测试 (1000条记录) ===");
        
        List<EmployeeProductConfiguration> testData = generateTestData(1000);
        String contractNumber = "TEST_CONTRACT_002";
        
        performanceMonitor.recordImportStart(testData.size());
        
        long startTime = System.currentTimeMillis();
        Map<String, Object> result = employeeProductConfigurationService.importData(testData, contractNumber);
        long endTime = System.currentTimeMillis();
        
        performanceMonitor.recordImportEnd();
        
        System.out.println("导入结果: " + result);
        System.out.println("总耗时: " + (endTime - startTime) + "ms");
        System.out.println("平均每条记录耗时: " + (double)(endTime - startTime) / testData.size() + "ms");
        System.out.println("处理速度: " + (double)testData.size() * 1000 / (endTime - startTime) + " 条/秒");
        
        performanceMonitor.printPerformanceReport();
    }

    /**
     * 测试更新场景的性能
     */
    @Test
    @Transactional
    public void testUpdatePerformance() {
        System.out.println("=== 更新场景性能测试 ===");
        
        String contractNumber = "TEST_CONTRACT_003";
        
        // 先插入一批数据
        List<EmployeeProductConfiguration> initialData = generateTestData(500);
        employeeProductConfigurationService.importData(initialData, contractNumber);
        
        // 修改数据进行更新测试
        List<EmployeeProductConfiguration> updateData = new ArrayList<>();
        for (int i = 0; i < 500; i++) {
            EmployeeProductConfiguration config = generateTestRecord(i);
            config.setRfidTagNumberWork("RFID_WORK_" + String.format("%06d", i)); // 使用相同的RFID
            config.setEmployeeName("更新后的员工_" + i); // 修改员工姓名
            config.setDepartment("更新后的部门_" + (i % 10)); // 修改部门
            updateData.add(config);
        }
        
        performanceMonitor.recordImportStart(updateData.size());
        
        long startTime = System.currentTimeMillis();
        Map<String, Object> result = employeeProductConfigurationService.importData(updateData, contractNumber);
        long endTime = System.currentTimeMillis();
        
        performanceMonitor.recordImportEnd();
        
        System.out.println("更新结果: " + result);
        System.out.println("更新耗时: " + (endTime - startTime) + "ms");
        System.out.println("平均每条记录更新耗时: " + (double)(endTime - startTime) / updateData.size() + "ms");
        
        performanceMonitor.printPerformanceReport();
    }

    /**
     * 生成测试数据
     */
    private List<EmployeeProductConfiguration> generateTestData(int count) {
        List<EmployeeProductConfiguration> dataList = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            dataList.add(generateTestRecord(i));
        }
        
        return dataList;
    }

    /**
     * 生成单条测试记录
     */
    private EmployeeProductConfiguration generateTestRecord(int index) {
        EmployeeProductConfiguration config = new EmployeeProductConfiguration();
        
        // 基本信息
        config.setSerialNumber(index + 1);
        config.setEmployeeName("测试员工_" + index);
        config.setEmployeePhone("1380000" + String.format("%04d", index));
        config.setDepartment("测试部门_" + (index % 10));
        config.setSubDepartment("测试分部门_" + (index % 5));
        
        // 产品信息
        config.setWorkWearConfigurationCategory("工作服品类_" + (index % 3));
        config.setConfigurationCategory("配置品类_" + (index % 3));
        config.setProductModelNumber("MODEL_" + String.format("%04d", index % 100));
        config.setProductColor("颜色_" + (index % 5));
        config.setLogoPosition("LOGO位置_" + (index % 3));
        config.setProductSpecification("规格_" + (index % 5));
        
        // 配置信息
        config.setProtocolConfigurationQuantity(index % 10 + 1);
        config.setEmployeeId("EMP_" + String.format("%06d", index));
        config.setRfidTagNumberWork("RFID_WORK_" + String.format("%06d", index));
        config.setRfidTagNumber("RFID_" + String.format("%06d", index));
        config.setProductSerialNumber("SERIAL_" + String.format("%06d", index));
        
        // 柜子信息
        config.setLockerModel("柜子型号_" + (index % 5));
        config.setLockerQuantity(1);
        config.setLockerSerialNumber("LOCKER_" + String.format("%06d", index));
        config.setSortingBoxNumber("BOX_" + String.format("%04d", index % 100));
        
        // 费用信息
        config.setWeeklyWashDate("1,3,5"); // 周一、三、五
        config.setWeeklyWashFrequency(3);
        config.setSingleWashRentalFee("10.00");
        config.setWeeklyWashRentalFee("25.00");
        config.setClothingSettlementPrice(new BigDecimal("100.00"));
        config.setMonthlyTotalCost(new BigDecimal("300.00"));
        
        // 日期信息
        config.setProtocolStartDate(new Date());
        config.setProtocolEndDate(new Date(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000)); // 一年后
        
        // 其他信息
        config.setAttributes("测试属性");
        config.setServiceType("测试服务");
        
        return config;
    }

    /**
     * 内存使用情况监控
     */
    private void printMemoryUsage(String stage) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        System.out.println("=== " + stage + " 内存使用情况 ===");
        System.out.println("已用内存: " + (usedMemory / 1024 / 1024) + " MB");
        System.out.println("总内存: " + (totalMemory / 1024 / 1024) + " MB");
        System.out.println("最大内存: " + (maxMemory / 1024 / 1024) + " MB");
        System.out.println("内存使用率: " + String.format("%.2f", (double)usedMemory / maxMemory * 100) + "%");
    }
}
