package com.rutong.platform.wms.entity;

import com.rutong.platform.common.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * 服装审核管理
 */
@Entity
@Table(name = "wms_clothing_examine")
public class WmsClothingExamine extends BaseEntity {

	public static final String FIELD_STATUS = "status";
	public static final String FIELD_COL_RFID = "cloRfid";
	public static final String FIELD_COL_NUMBER = "cloNumber";
	public static final String FIELD_ENTERPRISE_NAME = "enterpriseName";

	private String cloType; // 品类

	private String cloModel; // 款号

	private String cloColor; // 颜色

	private String cloLogo; // logo位置

	private String cloSpec; // 规格

	private Long cloCount; // 数量

	private String cloRfid; // rfid号

	@Column(nullable = false, unique = true, length = 96)
	private String cloNumber; // 序列号

	@Column(nullable = false)
	private Integer status; // 0在库 1租赁

	private Date leaseTime; // 最后租赁时间

	private String operationType;  //操作类型

	//@Transient
	private String result;  //原因

	private String enterpriseName; //客户名称

	private Integer examineStatus;//审核状态,0待审核 1审核通过 2审核未通过 3未提交（初始状态）

	private String outboundResult;  //出库原因

	private Date examineTime;//提交出入库时间

	private String examineUser;//提交出入库操作人

	private Date checkTime; //审核时间

	private String checkUser; //审核操作人


	public String getCheckUser() {
		return checkUser;
	}

	public void setCheckUser(String checkUser) {
		this.checkUser = checkUser;
	}

	public Date getCheckTime() {
		return checkTime;
	}

	public void setCheckTime(Date checkTime) {
		this.checkTime = checkTime;
	}

	public Date getExamineTime() {
		return examineTime;
	}

	public void setExamineTime(Date examineTime) {
		this.examineTime = examineTime;
	}

	public String getExamineUser() {
		return examineUser;
	}

	public void setExamineUser(String examineUser) {
		this.examineUser = examineUser;
	}

	public String getOutboundResult() {return outboundResult;}

	public void setOutboundResult(String outboundResult) {this.outboundResult = outboundResult;}

	public String getOperationType() {return operationType;}

	public void setOperationType(String operationType) {this.operationType = operationType;}

	public Integer getExamineStatus() {return examineStatus;}

	public void setExamineStatus(Integer examineStatus) {this.examineStatus = examineStatus;}

	public String getEnterpriseName() {
		return enterpriseName;
	}

	public void setEnterpriseName(String enterpriseName) {
		this.enterpriseName = enterpriseName;
	}

    public Long getCloCount() {
        return cloCount;
    }

    public void setCloCount(Long cloCount) {
        this.cloCount = cloCount;
    }

    public String getCloType() {
		return cloType;
	}

	public void setCloType(String cloType) {
		this.cloType = cloType;
	}

	public String getCloModel() {
		return cloModel;
	}

	public void setCloModel(String cloModel) {
		this.cloModel = cloModel;
	}

	public String getCloColor() {
		return cloColor;
	}

	public void setCloColor(String cloColor) {
		this.cloColor = cloColor;
	}

	public String getCloLogo() {
		return cloLogo;
	}

	public void setCloLogo(String cloLogo) {
		this.cloLogo = cloLogo;
	}

	public String getCloSpec() {
		return cloSpec;
	}

	public void setCloSpec(String cloSpec) {
		this.cloSpec = cloSpec;
	}

	public String getCloRfid() {
		return cloRfid;
	}

	public void setCloRfid(String cloRfid) {
		this.cloRfid = cloRfid;
	}

	public String getCloNumber() {
		return cloNumber;
	}

	public void setCloNumber(String cloNumber) {
		this.cloNumber = cloNumber;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getLeaseTime() {
		return leaseTime;
	}

	public void setLeaseTime(Date leaseTime) {
		this.leaseTime = leaseTime;
	}

	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		this.result = result;
	}

}
