package com.rutong.platform.wms.service;

import java.math.BigInteger;
import java.util.Date;

import org.springframework.stereotype.Service;

import com.rutong.framework.core.exception.ServiceException;
import com.rutong.framework.utils.BeanUtils;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsException;

@Service
public class WmsExceptionService extends BaseService<WmsException> {

    @Override
    public void save(WmsException data) {
        EmployeeProductConfiguration configuration = dao.findByPropertyFirst(EmployeeProductConfiguration.class,
                EmployeeProductConfiguration.FIELD_RFID_TAG_NUMBER,
                data.getCloRfid());

        if (configuration == null) {
            throw new ServiceException("无效标签号");
        }

        WmsException exception = new WmsException();
        exception.setCloColor(configuration.getProductColor());
        exception.setCloLogo(configuration.getLogoPosition());
        exception.setCloModel(configuration.getProductModelNumber());
        exception.setCloNumber(configuration.getProductSerialNumber());
        exception.setCloRfid(configuration.getRfidTagNumber());
        exception.setCloSpec(configuration.getProductSpecification());
        exception.setCloType(configuration.getConfigurationCategory());
        exception.setCloWorkType(configuration.getWorkWearConfigurationCategory());
        exception.setEmployeeName(configuration.getEmployeeName());//员工姓名
        exception.setDepartment(configuration.getDepartment());//部门名称
        exception.setClientName(configuration.getEnterpriseName());//客户名称
        exception.setCreateBy("分拣工作台");
        exception.setId(null);
        exception.setEtype(data.getEtype());
        exception.setCreateTime(new Date());
        dao.persist(exception);
    }

    public BigInteger groupByWorkWearConfigurationCategory(String createTime, String clientName) {
        return dao.countSQLQuery("select count(*) from wms_exception where DATE(create_time)  = ?0 and client_name = ?1", createTime, clientName);
    }
}
