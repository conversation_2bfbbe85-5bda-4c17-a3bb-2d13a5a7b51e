package com.rutong.platform.wms.service;

import com.rutong.framework.utils.BeanUtils;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsClothingExamine;
import com.rutong.platform.wms.entity.WmsClothingOutbound;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.wms.entity.WmsClothing;
import com.rutong.framework.bean.ResultBean;

@Service
public class WmsClothingExamineService extends BaseService<WmsClothingExamine> {

    @Autowired
    private WmsClothingOutboundService outboundService;
    
    @Autowired
    private WmsClothingService wmsClothingService;

    public WmsClothingExamine findByCloRfid(String cloRfid) {
        return dao.findByPropertyFirst(WmsClothingExamine.class, WmsClothingExamine.FIELD_COL_RFID, cloRfid);
    }

    public WmsClothingExamine findByCloNumber(String cloNumber) {
        return dao.findByPropertyFirst(WmsClothingExamine.class, WmsClothingExamine.FIELD_COL_NUMBER, cloNumber);
    }

    public WmsClothingExamine findByCloNumberAndEnterpriseName(String cloNumber, String enterpriseName) {
        return dao.findByPropertyFirst(WmsClothingExamine.class, WmsClothingExamine.FIELD_COL_NUMBER, cloNumber, WmsClothingExamine.FIELD_ENTERPRISE_NAME, enterpriseName);
    }

    /**
     * 审核通过显示在出库服装界面的方法
     * @param wmsClothingExamine
     * @param result
     * @param count
     * @param type
     */
    public void updateOutbound(WmsClothingExamine wmsClothingExamine, String result, Long count, String type) {
        WmsClothingOutbound wmsClothingOutbound = new WmsClothingOutbound();
        BeanUtils.copyProperties(wmsClothingOutbound, wmsClothingExamine);
        wmsClothingOutbound.setId(null);
        wmsClothingOutbound.setOutboundTime(new Date());
        wmsClothingOutbound.setOutboundResult(result);
        wmsClothingOutbound.setOperationType(type);
        wmsClothingOutbound.setCloCount(count);
        wmsClothingOutbound.setOutboundUser(wmsClothingExamine.getExamineUser());
        outboundService.save(wmsClothingOutbound);
    }

    /**
     * 处理出库审核
     * 前端已删除输入数量，仅保留审核功能
     */
    public ResultBean handleOutbound(WmsClothingExamine wmsClothingExamine, WmsClothingExamine data) {
        // 根据服装序列号查找wms_clothing表中的实际库存
        //WmsClothing wmsClothing = wmsClothingService.findByCloNumber(wmsClothingExamine.getCloNumber());
        WmsClothing wmsClothing = wmsClothingService.findByCloNumberAndEnterpriseName(wmsClothingExamine.getCloNumber(), wmsClothingExamine.getEnterpriseName());
        if (wmsClothing == null) {
            return ResultBean.error("未找到对应的服装库存记录", 500);
        }
        
        // 使用审核记录中的数量进行出库（前端已删除输入数量功能）
        long outboundCount = wmsClothingExamine.getCloCount();
        long remainingCount = wmsClothing.getCloCount() - outboundCount;
        
        if (remainingCount < 0) {
            return ResultBean.error("库存不足无法出库", 500);
        }
        
        // 更新wms_clothing表中的库存数量
        if (remainingCount == 0) {
            wmsClothingService.delete(wmsClothing);
        } else {
            wmsClothing.setCloCount(remainingCount);
            wmsClothingService.update(wmsClothing);
        }
        
        // 更新审核状态
        wmsClothingExamine.setExamineStatus(1);
        wmsClothingExamine.setCheckTime(new Date());
        LoginUser loginUser = SecurityUtils.getLoginUser();//审核操作人
        wmsClothingExamine.setCheckUser(loginUser.getUser().getNickname());
        wmsClothingExamine.setResult(data.getResult()); // 设置前端传过来的审核原因
        
        // 记录出库流水，保留审核原因
        updateOutbound(wmsClothingExamine, data.getResult(), outboundCount, "出库");
        return ResultBean.success();
    }
    
    /**
     * 处理入库审核
     * 前端已删除输入数量，仅保留审核功能
     */
    public ResultBean handleInbound(WmsClothingExamine wmsClothingExamine, WmsClothingExamine data) {
        // 根据服装序列号查找wms_clothing表中的实际库存
        //WmsClothing wmsClothing = wmsClothingService.findByCloNumber(wmsClothingExamine.getCloNumber());
        WmsClothing wmsClothing = wmsClothingService.findByCloNumberAndEnterpriseName(wmsClothingExamine.getCloNumber(), wmsClothingExamine.getEnterpriseName());
        if (wmsClothing == null) {
            // 如果库存表中没有该服装，创建新记录
            wmsClothing = new WmsClothing();
            BeanUtils.copyProperties(wmsClothing, wmsClothingExamine);
            wmsClothing.setId(null);
            wmsClothing.setStatus(0); // 0表示在库
            wmsClothing.setCloCount(wmsClothingExamine.getCloCount()); // 使用审核记录中的数量
            wmsClothingService.save(wmsClothing);
        } else {
            // 如果库存表中已有该服装，增加数量
            wmsClothing.setCloCount(wmsClothing.getCloCount() + wmsClothingExamine.getCloCount());
            wmsClothingService.update(wmsClothing);
        }
        
        // 更新审核状态
        wmsClothingExamine.setExamineStatus(1);
        wmsClothingExamine.setCheckTime(new Date());
        LoginUser loginUser = SecurityUtils.getLoginUser();//审核操作人
        wmsClothingExamine.setCheckUser(loginUser.getUser().getNickname());
        wmsClothingExamine.setResult(data.getResult()); // 设置前端传过来的审核原因
        
        // 记录入库流水，保留审核原因
        updateOutbound(wmsClothingExamine, data.getResult(), wmsClothingExamine.getCloCount(), "入库");
        return ResultBean.success();
    }

}
