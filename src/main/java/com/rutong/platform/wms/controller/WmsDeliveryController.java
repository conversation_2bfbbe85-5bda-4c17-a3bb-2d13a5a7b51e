package com.rutong.platform.wms.controller;

import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.platform.washing.entity.WashingTask;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsDelivery;
import com.rutong.platform.wms.service.WmsDeliveryService;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/wms/delivery")
@LogDesc(title = "服装交接")
public class WmsDeliveryController extends CrudController<WmsDelivery>{

	@Autowired
	private WmsDeliveryService deliveryService;

	@Override
	public BaseService<WmsDelivery> getService() {
		return deliveryService;
	}



    /**
     * 获取洗涤任务详情
     * @param entity
     * @return
     */
    @PostMapping(value = "/getHandoverRecords.do")
    public ResultBean getHandoverRecords(WmsDelivery entity) {
        List<SortCondition> sorts = new ArrayList<>();
        SortCondition sortCondition = new SortCondition();
        sortCondition.setDirection(SortCondition.SORT_ASC);
        sortCondition.setProperty("createTime");
        sorts.add(sortCondition);
        List<WmsDelivery> byContractNumberAndProductSerialNumber = deliveryService.findAllByObject(entity,sorts);
        return ResultBean.success(byContractNumberAndProductSerialNumber);
    }
}
