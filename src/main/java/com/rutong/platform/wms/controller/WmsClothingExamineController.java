package com.rutong.platform.wms.controller;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.BeanUtils;
import com.rutong.framework.utils.Global;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.util.ExportUtil;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsClothing;
import com.rutong.platform.wms.entity.WmsClothingExamine;
import com.rutong.platform.wms.entity.WmsClothingExcel;
import com.rutong.platform.wms.excel.ClothingExcel;
import com.rutong.platform.wms.service.WmsClothingExamineService;
import com.rutong.platform.wms.service.WmsClothingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/wms/examine")
@LogDesc(title = "服装审核管理")
public class WmsClothingExamineController extends CrudController<WmsClothingExamine> {

    @Autowired
    private WmsClothingExamineService wmsClothingExamineService;

    @Autowired
    private WmsClothingService wmsClothingService;

    @Override
    public BaseService<WmsClothingExamine> getService() {
        return wmsClothingExamineService;
    }

    /**
     * 在库列表
     */
    @Override
    public PageInfo<WmsClothingExamine> findAllByPage(GridParams gridParams,
                                                      @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
                                                      @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        // 处理时间范围过滤 - 将字符串格式的日期转换为Date对象
        for (int i = 0; i < filters.size(); i++) {
            FilterCondition filter = filters.get(i);
            // 如果是日期字符串，需要转换为Date对象
            if ((filter.getProperty().equals("examineTime") || filter.getProperty().equals("createTime"))
                    && filter.getValue() instanceof String) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String dateStr = (String) filter.getValue();
                    Date date = sdf.parse(dateStr);
                    filter.setValue(date);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
        }
        
        FilterCondition filterCondition = new FilterCondition();
        filterCondition.setProperty(WmsClothingExamine.FIELD_STATUS);
        filterCondition.setOperator(FilterCondition.EQ);
        filterCondition.setValue(String.valueOf(0));
        filters.add(filterCondition);
        return super.findAllByPage(gridParams, filters, sorts);
    }

    /**
     * 租赁列表
     *
     * @param gridParams
     * @param filters
     * @param sorts
     * @return
     */
    @PostMapping(value = "/fetchdata2.do")
    public PageInfo<WmsClothingExamine> findAllByPage2(GridParams gridParams,
                                                       @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
                                                       @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        // 处理时间范围过滤 - 将字符串格式的日期转换为Date对象
        for (int i = 0; i < filters.size(); i++) {
            FilterCondition filter = filters.get(i);
            // 如果是日期字符串，需要转换为Date对象
            if ((filter.getProperty().equals("examineTime") || filter.getProperty().equals("createTime"))
                    && filter.getValue() instanceof String) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String dateStr = (String) filter.getValue();
                    Date date = sdf.parse(dateStr);
                    filter.setValue(date);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
        }
        
        FilterCondition filterCondition = new FilterCondition();
        filterCondition.setProperty(WmsClothingExamine.FIELD_STATUS);
        filterCondition.setOperator(FilterCondition.EQ);
        filterCondition.setValue(String.valueOf(1));
        filters.add(filterCondition);
        return super.findAllByPage(gridParams, filters, sorts);
    }

    /**
     * 批量导入服装
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importExcel.do")
    public ResultBean importExcel(@RequestParam("file") MultipartFile file) {
        List<WmsClothingExamine> wmsClothings = new ArrayList<WmsClothingExamine>();
        try {
            List<ClothingExcel> dataList = EasyExcel.read(file.getInputStream(), ClothingExcel.class, null)
                    .doReadAllSync();
            for (int i = 1; i < dataList.size(); i++) {
                ClothingExcel clothingExcel = dataList.get(i);
                // 从第三行开始插入

                WmsClothingExamine clothing = wmsClothingExamineService.findByCloNumber(clothingExcel.getCloNumber());
                if (clothing == null) {
                    WmsClothingExamine WmsClothingExamine = new WmsClothingExamine();
                    BeanUtils.copyProperties(WmsClothingExamine, clothingExcel);
                    WmsClothingExamine.setStatus(0);
                    WmsClothingExamine.setExamineStatus(3);//导入的数据默认为未提交
                    wmsClothings.add(WmsClothingExamine);
                } else {
                    clothing.setCloCount(clothing.getCloCount() + Long.parseLong(clothingExcel.getCloCount()));
                    wmsClothingExamineService.save(clothing);

                    wmsClothingExamineService.updateOutbound(clothing, "", Long.parseLong(clothingExcel.getCloCount()), "入库");
                }
            }
            wmsClothingExamineService.saveAll(wmsClothings);
        } catch (IOException e) {
            e.printStackTrace();
            return ResultBean.error(e.getMessage(), 500);
        }
        return ResultBean.success();
    }

    /**
     * 审核出入库
     */
    @PostMapping(value = "/outbound.do")
    public ResultBean outbound(@RequestBody WmsClothingExamine data) {
        WmsClothingExamine wmsClothingExamine = wmsClothingExamineService.findById(data.getId());
        // 添加空值检查，防止空指针异常
        if (wmsClothingExamine == null) {
            return ResultBean.error("未找到对应的服装审核记录", 500);
        }

        // 根据操作类型判断是出库还是入库
        String operationType = wmsClothingExamine.getOperationType();
        if ("出库".equals(operationType)) {
            return wmsClothingExamineService.handleOutbound(wmsClothingExamine, data);
        } else if ("入库".equals(operationType)) {
            return wmsClothingExamineService.handleInbound(wmsClothingExamine, data);
        } else {
            return ResultBean.error("未知的操作类型: " + operationType, 400);
        }
    }

    /**
     * 导出在库服装
     *
     * @param filters
     * @return
     * @throws IOException
     */
    @PostMapping(value = "/export.do")
    public ResultBean exportClothing(@RequestList(clazz = FilterCondition.class) List<FilterCondition> filters) throws IOException {
        String fileName = "在库服装";
        fileName = ExportUtil.encodingExcelFilename(fileName);
        String folder = Global.getTempPath() + File.separator + "pio" + File.separator;
        FileUtil.touch(folder + fileName);
        List<WmsClothingExcel> list = new ArrayList<>();
        // 导出的Excel按照创建时间进行降序排序
        List<SortCondition> sorts = new ArrayList<>();
        if (sorts.isEmpty()) {
            SortCondition sortCondition = new SortCondition();
            sortCondition.setProperty("createTime");
            sortCondition.setDirection(SortCondition.SORT_DESC);
            sorts.add(sortCondition);
        }
        List<WmsClothingExamine> allByObject = wmsClothingExamineService.findAll(filters, sorts);
        for (WmsClothingExamine WmsClothingExamine : allByObject) {
            WmsClothingExcel wmsClothingExcel = new WmsClothingExcel();
            BeanUtils.copyProperties(wmsClothingExcel, WmsClothingExamine);
            list.add(wmsClothingExcel);
        }
        EasyExcel.write(folder + fileName, WmsClothingExcel.class)
                .sheet("服装库存")
                .doWrite(list);
        return ResultBean.success(fileName);
    }

    /**
     * 批量提交服装出库审核为待审核
     * 未提交状态不能审核
     *
     * @param dataList
     */
    @PostMapping("/uploadBatch.do")
    public ResultBean uploadBatch(@RequestBody List<WmsClothingExamine> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return ResultBean.error("提交数据不能为空", 400);
        }
        for (WmsClothingExamine item : dataList) {
            if (item == null || item.getId() == null) {
                return ResultBean.error("存在无效记录或缺少ID", 400);
            }
            WmsClothingExamine wmsClothingExamine = new WmsClothingExamine();
            wmsClothingExamine.setExamineStatus(0);
            wmsClothingExamine.setId(item.getId());
            wmsClothingExamineService.update(wmsClothingExamine);
        }
        return ResultBean.success();
    }

    /**
     * 批量提交服装出库审核为待审核
     * 未提交状态不能审核
     *
     * @param dataList
     */
    @PostMapping("/reject.do")
    public ResultBean rejectBatch(@RequestBody List<WmsClothingExamine> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return ResultBean.error("提交数据不能为空", 400);
        }
        for (WmsClothingExamine item : dataList) {
            if (item == null || item.getId() == null) {
                return ResultBean.error("存在无效记录或缺少ID", 400);
            }
            WmsClothingExamine wmsClothingExamine = new WmsClothingExamine();
            wmsClothingExamine.setExamineStatus(2);
            wmsClothingExamine.setCheckTime(new Date());
            LoginUser loginUser = SecurityUtils.getLoginUser();//审核操作人
            wmsClothingExamine.setCheckUser(loginUser.getUser().getNickname());
            wmsClothingExamine.setResult("未通过");
            wmsClothingExamine.setId(item.getId());
            wmsClothingExamineService.update(wmsClothingExamine);
        }
        return ResultBean.success();
    }

}