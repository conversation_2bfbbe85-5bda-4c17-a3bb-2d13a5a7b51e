package com.rutong.platform.wms.controller;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.utils.Global;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.excel.EmployeeProductConfigurationExcel;
import com.rutong.platform.client.util.ExportUtil;
import com.rutong.platform.wms.entity.WmsDelivery;
import com.rutong.platform.wms.excel.WmsDeliveryDetailNew;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/wms/deliverydetail")
@LogDesc(title = "服装交接明细")
public class WmsDeliveryDetailController extends CrudController<WmsDeliveryDetail>{

	@Autowired
	private WmsDeliveryDetailService deliveryDetailService;

	@Override
	public BaseService<WmsDeliveryDetail> getService() {
		return deliveryDetailService;
	}

	/**
	 * 按批次查看配送明细
	 * @param deliveryId
	 * @return
	 */
    @PostMapping(value = "/fetchByBatch.do")
    public ResultBean fetchByBatch(String deliveryId) {
        List<WmsDeliveryDetail> byContractNumberAndProductSerialNumber = deliveryDetailService.findByDeliveryId(deliveryId);
        return ResultBean.success(byContractNumberAndProductSerialNumber);
    }

	/**
	 * 配送明细导出
	 * @param deliveryId
	 * @return
	 * @throws IOException
	 */
	@PostMapping("/toExport.do")
	public ResultBean export(String deliveryId) throws IOException {
		String fileName = "配送明细";
		fileName = ExportUtil.encodingExcelFilename(fileName);
		String folder = Global.getTempPath() + File.separator + "pio" + File.separator;
		FileUtil.touch(folder + fileName);
		// 模拟数据，实际需要从数据库中获取
		List<WmsDeliveryDetailNew> list = new ArrayList<>();

		List<WmsDeliveryDetail> allByObject = deliveryDetailService.findByDeliveryId(deliveryId);

		for (WmsDeliveryDetail wmsDeliveryDetail : allByObject) {
			WmsDeliveryDetailNew wmsDeliveryDetailNew = new WmsDeliveryDetailNew();
			//BeanUtils.copyProperties(wmsDeliveryDetail, wmsDeliveryDetailNew);//前面是源，后面是目标
			wmsDeliveryDetailNew.setOrderNumber(wmsDeliveryDetail.getOrderNumber());//任务单号
			wmsDeliveryDetailNew.setBatchNo(wmsDeliveryDetail.getBatchNo());
			wmsDeliveryDetailNew.setCreateTime(wmsDeliveryDetail.getCreateTime());
			wmsDeliveryDetailNew.setContractNumber(wmsDeliveryDetail.getContractNumber());
			wmsDeliveryDetailNew.setEnterpriseName(wmsDeliveryDetail.getEnterpriseName());
			wmsDeliveryDetailNew.setEmployeeName(wmsDeliveryDetail.getEmployeeName());
			wmsDeliveryDetailNew.setEmployeePhone(wmsDeliveryDetail.getEmployeePhone());
			wmsDeliveryDetailNew.setDepartment(wmsDeliveryDetail.getDepartment());
			wmsDeliveryDetailNew.setSubDepartment(wmsDeliveryDetail.getSubDepartment());
			wmsDeliveryDetailNew.setConfigurationCategory(wmsDeliveryDetail.getConfigurationCategory());
			wmsDeliveryDetailNew.setProductModelNumber(wmsDeliveryDetail.getProductModelNumber());
			wmsDeliveryDetailNew.setProductColor(wmsDeliveryDetail.getProductColor());
			wmsDeliveryDetailNew.setProductSpecification(wmsDeliveryDetail.getProductSpecification());
			wmsDeliveryDetailNew.setEmployeeId(wmsDeliveryDetail.getEmployeeId());
			wmsDeliveryDetailNew.setRfidTagNumberWork(wmsDeliveryDetail.getRfidTagNumberWork());
			wmsDeliveryDetailNew.setRfidTagNumber(wmsDeliveryDetail.getRfidTagNumber());
			wmsDeliveryDetailNew.setProductSerialNumber(wmsDeliveryDetail.getProductSerialNumber());
			wmsDeliveryDetailNew.setLockerSerialNumber(wmsDeliveryDetail.getLockerSerialNumber());
			wmsDeliveryDetailNew.setStatus(wmsDeliveryDetail.getStatus());
			wmsDeliveryDetailNew.setWorkWearCategory(wmsDeliveryDetail.getWorkWearConfigurationCategory());
			list.add(wmsDeliveryDetailNew);
		}
		EasyExcel.write(folder + fileName, WmsDeliveryDetailNew.class)
				.sheet("配送明细")
				.doWrite(list);

		return ResultBean.success(fileName);

	}
}
