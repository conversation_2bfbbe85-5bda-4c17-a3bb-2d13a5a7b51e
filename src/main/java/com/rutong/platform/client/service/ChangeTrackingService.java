package com.rutong.platform.client.service;

import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.annotation.ChangeTracking;
import com.rutong.platform.client.annotation.FieldTracking;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.entity.EmployeeProductConfigurationHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 变更追踪服务
 * 提供手动变更追踪功能，用于特殊场景
 */
@Service
public class ChangeTrackingService {

    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;

    @Autowired
    private EmployeeProductConfigurationHistoryService historyService;

    /**
     * 手动追踪实体变更
     *
     * @param newEntity 新实体
     * @param entityId  实体ID
     */
    public void trackChanges(EmployeeProductConfiguration newEntity, String entityId) {
        try {
            // 获取原始实体
            EmployeeProductConfiguration originalEntity = employeeProductConfigurationService.findById(entityId);
            if (originalEntity == null) {
                return;
            }

            // 检测变更
            List<EmployeeProductConfigurationHistory> historyList = detectChanges(newEntity, originalEntity);

            if (!historyList.isEmpty()) {
                new Thread(() -> {
                    historyService.saveAll(historyList);
                }).start();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量追踪实体变更
     *
     * @param entities 实体列表（包含ID）
     */
    public void batchTrackChanges(List<EmployeeProductConfiguration> entities) {
        if (entities == null || entities.isEmpty()) {
            return;
        }

        List<EmployeeProductConfigurationHistory> allHistoryList = new ArrayList<>();

        for (EmployeeProductConfiguration entity : entities) {
            if (entity.getId() == null) {
                continue;
            }

            try {
                // 获取原始实体
                EmployeeProductConfiguration originalEntity = employeeProductConfigurationService.findById(entity.getId());
                if (originalEntity == null) {
                    continue;
                }

                // 检测变更
                List<EmployeeProductConfigurationHistory> historyList = detectChanges(entity, originalEntity);
                allHistoryList.addAll(historyList);

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (!allHistoryList.isEmpty()) {
            // 异步批量保存历史记录
            historyService.asyncBatchSaveHistory(allHistoryList);
        }
    }

    /**
     * 检测两个实体之间的变更
     */
    private List<EmployeeProductConfigurationHistory> detectChanges(
            EmployeeProductConfiguration newEntity,
            EmployeeProductConfiguration originalEntity) {

        List<EmployeeProductConfigurationHistory> historyList = new ArrayList<>();

        if (newEntity == null || originalEntity == null) {
            return historyList;
        }

        Class<?> entityClass = newEntity.getClass();
        ChangeTracking tracking = entityClass.getAnnotation(ChangeTracking.class);
        if (tracking == null) {
            return historyList;
        }

        Set<String> ignoreFields = new HashSet<>(Arrays.asList(tracking.ignoreFields()));

        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Date currentTime = new Date();

        // 获取所有字段
        List<Field> allFields = getAllFields(entityClass);

        for (Field field : allFields) {
            String fieldName = field.getName();

            // 跳过忽略的字段
            if (ignoreFields.contains(fieldName)) {
                continue;
            }

            // 检查字段是否标记为忽略
            FieldTracking fieldTracking = field.getAnnotation(FieldTracking.class);
            if (fieldTracking != null && fieldTracking.ignore()) {
                continue;
            }

            try {
                field.setAccessible(true);
                Object newValue = field.get(newEntity);
                Object oldValue = field.get(originalEntity);

                // 检查值是否发生变更
                if (!Objects.equals(newValue, oldValue)) {
                    String displayName = getFieldDisplayName(field, fieldTracking);

                    // 创建历史记录
                    EmployeeProductConfigurationHistory history = createHistoryRecord(originalEntity);
                    history.setChangeType(displayName + "变更");
                    history.setOldValue(valueToString(oldValue));
                    history.setNewValue(valueToString(newValue));

                    // 设置操作信息
                    if (loginUser != null) {
                        history.setOperator(loginUser.getUsername());
                        history.setCreateBy(loginUser.getUsername());
                        history.setOwnerDeptId(loginUser.getDeptId());
                        history.setOwnerUserId(loginUser.getUserId());
                    }
                    history.setCreateTime(currentTime);

                    historyList.add(history);
                }
            } catch (Exception e) {
                // 忽略无法访问的字段
            }
        }

        return historyList;
    }

    /**
     * 获取类的所有字段，包括父类字段
     */
    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();

        while (clazz != null && clazz != Object.class) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }

        return fields;
    }

    /**
     * 获取字段显示名称
     */
    private String getFieldDisplayName(Field field, FieldTracking fieldTracking) {
        if (fieldTracking != null && !fieldTracking.displayName().isEmpty()) {
            return fieldTracking.displayName();
        }

        // 使用默认的字段名映射
        return getDefaultDisplayName(field.getName());
    }

    /**
     * 获取默认的字段显示名称
     */
    private String getDefaultDisplayName(String fieldName) {
        Map<String, String> displayNames = new HashMap<>();
        displayNames.put("contractNumber", "合同编号");
        displayNames.put("enterpriseName", "客户名称");
        displayNames.put("employeeName", "员工姓名");
        displayNames.put("employeePhone", "员工电话");
        displayNames.put("department", "部门");
        displayNames.put("subDepartment", "分部门");
        displayNames.put("workWearConfigurationCategory", "工作服品类");
        displayNames.put("configurationCategory", "配置品类");
        displayNames.put("productModelNumber", "产品款号");
        displayNames.put("productColor", "产品颜色");
        displayNames.put("logoPosition", "LOGO位置");
        displayNames.put("productSpecification", "产品规格");
        displayNames.put("protocolConfigurationQuantity", "协议各品类配置数量");
        displayNames.put("employeeId", "员工企业工号");
        displayNames.put("rfidTagNumberWork", "工作服RFID标签编码");
        displayNames.put("rfidTagNumber", "RFID标签号");
        displayNames.put("productSerialNumber", "员工产品序列号");
        displayNames.put("lockerModel", "存衣柜协议配置型号");
        displayNames.put("lockerQuantity", "协议存衣柜配置数量");
        displayNames.put("lockerSerialNumber", "员工更衣柜序列号");
        displayNames.put("sortingBoxNumber", "检品分拣柜号");
        displayNames.put("dirtyLockerModel", "脏衣柜协议配置型号");
        displayNames.put("dirtyLockerQuantity", "协议脏衣柜配置数量");
        displayNames.put("dirtyLockerSerialNumber", "脏衣柜序列号");
        displayNames.put("protocolUsageMonths", "协议配置品类使用月份");
        displayNames.put("weeklySchedule", "协议每周作息时间");
        displayNames.put("weeklyWashDate", "协议每周换洗日期");
        displayNames.put("weeklyWashFrequency", "协议每周换洗次数");
        displayNames.put("productWashInfo", "配置产品洗涤信息");
        displayNames.put("singleWashRentalFee", "协议单次清洗租赁费");
        displayNames.put("weeklyWashRentalFee", "协议每周清洗租赁费");
        displayNames.put("clothingSettlementPrice", "协议服装结算价");
        displayNames.put("clothingResidualValue", "本月服装余值");
        displayNames.put("clothingLossCount", "客方本月服装丢失次数");
        displayNames.put("clothingLossFee", "客方本月服装丢失费");
        displayNames.put("monthlyTotalCost", "月合计费用");
        displayNames.put("protocolStartDate", "协议启用日期");
        displayNames.put("protocolEndDate", "协议截止日期");
        displayNames.put("resignationDate", "离职日期");
        displayNames.put("attributes", "属性");
        displayNames.put("status", "状态");
        displayNames.put("serviceType", "服务类型");

        return displayNames.getOrDefault(fieldName, fieldName);
    }

    /**
     * 创建历史记录基础对象
     */
    private EmployeeProductConfigurationHistory createHistoryRecord(EmployeeProductConfiguration entity) {
        EmployeeProductConfigurationHistory history = new EmployeeProductConfigurationHistory();

        // 复制所有字段到历史记录
        BeanUtils.copyProperties(entity, history);

        // 设置历史记录特有字段
        history.setParentId(entity.getId());
        history.setId(null);

        return history;
    }

    /**
     * 将值转换为字符串
     */
    private String valueToString(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Date) {
            return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((Date) value);
        }
        if (value instanceof BigDecimal) {
            return ((BigDecimal) value).toPlainString();
        }
        return value.toString();
    }

    /**
     * 批量导入时的变更追踪 - 专门处理导入场景（高性能优化版本）
     *
     * @param importEntities 导入的实体列表
     */
    public void batchImportTrackChanges(List<EmployeeProductConfiguration> importEntities, LoginUser loginUser) {
        if (importEntities == null || importEntities.isEmpty()) {
            return;
        }

        System.out.println("开始高性能批量导入变更追踪，共 " + importEntities.size() + " 条记录");

        // 1. 批量查询原始数据，减少数据库查询次数
        Map<String, EmployeeProductConfiguration> originalEntitiesMap = batchQueryOriginalEntities(importEntities);

        List<EmployeeProductConfigurationHistory> allHistoryList = new ArrayList<>();
        int processedCount = 0;
        int changedCount = 0;

        for (EmployeeProductConfiguration importEntity : importEntities) {
            String rfidWork = importEntity.getRfidTagNumberWork();
            if (rfidWork == null) {
                continue; // 新增记录，跳过
            }

            try {
                // 从批量查询结果中获取原始数据
                EmployeeProductConfiguration originalEntity = originalEntitiesMap.get(rfidWork);
                if (originalEntity == null) {
                    continue; // 新增记录，跳过变更追踪
                }

                // 使用快速变更检测
                List<EmployeeProductConfigurationHistory> historyList = fastDetectChanges(importEntity, originalEntity, loginUser);
                if (!historyList.isEmpty()) {
                    allHistoryList.addAll(historyList);
                    changedCount++;
                }
                processedCount++;

            } catch (Exception e) {
                System.err.println("处理导入记录 " + rfidWork + " 的变更追踪失败: " + e.getMessage());
            }
        }

        System.out.println("批量导入变更追踪完成 - 处理: " + processedCount + ", 有变更: " + changedCount + ", 历史记录: " + allHistoryList.size());

        if (!allHistoryList.isEmpty()) {
            // 异步批量保存历史记录
            historyService.asyncBatchSaveHistory(allHistoryList);
        }
    }

    /**
     * 批量查询原始实体数据
     */
    private Map<String, EmployeeProductConfiguration> batchQueryOriginalEntities(List<EmployeeProductConfiguration> importEntities) {
        // 收集所有需要查询的RFID工作标签
        Set<String> rfidWorkSet = importEntities.stream()
                .map(EmployeeProductConfiguration::getRfidTagNumberWork)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (rfidWorkSet.isEmpty()) {
            return new HashMap<>();
        }

        // 批量查询（这里需要在Service中添加批量查询方法）
        List<EmployeeProductConfiguration> originalEntities = employeeProductConfigurationService.findByRfidTagNumberWorkIn(new ArrayList<>(rfidWorkSet));

        // 构建Map索引
        return originalEntities.stream()
                .filter(entity -> entity.getRfidTagNumberWork() != null)
                .collect(Collectors.toMap(
                        EmployeeProductConfiguration::getRfidTagNumberWork,
                        entity -> entity,
                        (existing, replacement) -> existing // 如果有重复，保留现有的
                ));
    }

    /**
     * 快速变更检测 - 只检测关键字段，提高性能
     */
    private List<EmployeeProductConfigurationHistory> fastDetectChanges(
            EmployeeProductConfiguration newEntity,
            EmployeeProductConfiguration originalEntity, LoginUser loginUser) {

        List<EmployeeProductConfigurationHistory> historyList = new ArrayList<>();

        if (newEntity == null || originalEntity == null) {
            return historyList;
        }

        // 获取当前登录用户和时间
        Date currentTime = new Date();

        // 定义需要检测的关键字段
        Map<String, String> fieldsToCheck = getKeyFieldsMap();

        for (Map.Entry<String, String> entry : fieldsToCheck.entrySet()) {
            String fieldName = entry.getKey();
            String displayName = entry.getValue();

            try {
                Object newValue = getFieldValue(newEntity, fieldName);
                Object oldValue = getFieldValue(originalEntity, fieldName);

                // 检查值是否发生变更
                if (!Objects.equals(newValue, oldValue)) {
                    // 创建历史记录
                    EmployeeProductConfigurationHistory history = createHistoryRecord(originalEntity);
                    history.setChangeType(displayName + "变更");
                    history.setOldValue(valueToString(oldValue));
                    history.setNewValue(valueToString(newValue));

                    // 设置操作信息
                    if (loginUser != null) {
                        history.setOperator(loginUser.getUsername());
                        history.setCreateBy(loginUser.getUsername());
                        history.setOwnerDeptId(loginUser.getDeptId());
                        history.setOwnerUserId(loginUser.getUserId());
                    }
                    history.setCreateTime(currentTime);
                    history.setTableData(newEntity.getTableData());
                    historyList.add(history);
                }
            } catch (Exception e) {
                // 忽略无法访问的字段
                System.err.println("检测字段 " + fieldName + " 变更时出错: " + e.getMessage());
            }
        }

        return historyList;
    }

    /**
     * 获取关键字段映射
     */
    private Map<String, String> getKeyFieldsMap() {
        Map<String, String> fieldsMap = new HashMap<>();
        fieldsMap.put("employeeName", "员工姓名");
        fieldsMap.put("employeePhone", "员工电话");
        fieldsMap.put("department", "部门");
        fieldsMap.put("subDepartment", "分部门");
        fieldsMap.put("workWearConfigurationCategory", "工作服品类");
        fieldsMap.put("configurationCategory", "配置品类");
        fieldsMap.put("productModelNumber", "产品款号");
        fieldsMap.put("productColor", "产品颜色");
        fieldsMap.put("logoPosition", "LOGO位置");
        fieldsMap.put("productSpecification", "产品规格");
        fieldsMap.put("protocolConfigurationQuantity", "协议各品类配置数量");
        fieldsMap.put("employeeId", "员工企业工号");
        fieldsMap.put("rfidTagNumber", "RFID标签号");
        fieldsMap.put("productSerialNumber", "员工产品序列号");
        fieldsMap.put("weeklyWashDate", "协议每周换洗日期");
        fieldsMap.put("weeklyWashFrequency", "协议每周换洗次数");
        fieldsMap.put("singleWashRentalFee", "协议单次清洗租赁费");
        fieldsMap.put("weeklyWashRentalFee", "协议每周清洗租赁费");
        fieldsMap.put("protocolStartDate", "协议启用日期");
        fieldsMap.put("protocolEndDate", "协议截止日期");
        fieldsMap.put("attributes", "属性");
        fieldsMap.put("serviceType", "服务类型");
        return fieldsMap;
    }

    /**
     * 快速获取字段值
     */
    private Object getFieldValue(Object entity, String fieldName) throws Exception {
        String methodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
        java.lang.reflect.Method method = entity.getClass().getMethod(methodName);
        return method.invoke(entity);
    }
}
