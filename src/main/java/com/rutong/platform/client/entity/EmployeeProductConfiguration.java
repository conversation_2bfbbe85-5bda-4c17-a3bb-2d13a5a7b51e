package com.rutong.platform.client.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rutong.platform.client.annotation.ChangeTracking;
import com.rutong.platform.client.dto.EmployeeProductConfigurationDto;
import com.rutong.platform.client.listener.ChangeTrackingEntityListener;
import com.rutong.platform.common.entity.BaseEntity;
import lombok.Data;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 服务记录查询（产品配置信息）
 */
@Data
@Entity
@Table(name = "employee_product_configuration")
@EntityListeners(ChangeTrackingEntityListener.class)
@ChangeTracking(historyClass = EmployeeProductConfigurationHistory.class, async = true)
public class EmployeeProductConfiguration extends EmployeeProductConfigurationDto {

    //冬装夏装查询列表
    //被@Transient标注的字段不会被 JPA 映射到数据库表的列
    //适用于那些仅在内存中计算或临时使用
    @Transient
    private String tableData;


}
