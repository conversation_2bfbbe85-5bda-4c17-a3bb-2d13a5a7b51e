package com.rutong.platform.client.util;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 导入性能监控工具类
 * 用于监控和统计批量导入的性能指标
 */
@Component
public class ImportPerformanceMonitor {

    private final Map<String, Long> timingMap = new ConcurrentHashMap<>();
    private final Map<String, Integer> counterMap = new ConcurrentHashMap<>();

    /**
     * 开始计时
     * @param operation 操作名称
     */
    public void startTiming(String operation) {
        timingMap.put(operation + "_start", System.currentTimeMillis());
    }

    /**
     * 结束计时并返回耗时
     * @param operation 操作名称
     * @return 耗时（毫秒）
     */
    public long endTiming(String operation) {
        Long startTime = timingMap.get(operation + "_start");
        if (startTime == null) {
            return 0;
        }
        long duration = System.currentTimeMillis() - startTime;
        timingMap.put(operation + "_duration", duration);
        timingMap.remove(operation + "_start");
        return duration;
    }

    /**
     * 获取操作耗时
     * @param operation 操作名称
     * @return 耗时（毫秒）
     */
    public long getDuration(String operation) {
        return timingMap.getOrDefault(operation + "_duration", 0L);
    }

    /**
     * 增加计数器
     * @param counter 计数器名称
     * @param increment 增量
     */
    public void incrementCounter(String counter, int increment) {
        counterMap.merge(counter, increment, Integer::sum);
    }

    /**
     * 增加计数器（增量为1）
     * @param counter 计数器名称
     */
    public void incrementCounter(String counter) {
        incrementCounter(counter, 1);
    }

    /**
     * 获取计数器值
     * @param counter 计数器名称
     * @return 计数值
     */
    public int getCounter(String counter) {
        return counterMap.getOrDefault(counter, 0);
    }

    /**
     * 重置所有统计数据
     */
    public void reset() {
        timingMap.clear();
        counterMap.clear();
    }

    /**
     * 获取性能报告
     * @return 性能报告Map
     */
    public Map<String, Object> getPerformanceReport() {
        Map<String, Object> report = new HashMap<>();
        
        // 添加计时信息
        Map<String, Long> timings = new HashMap<>();
        for (Map.Entry<String, Long> entry : timingMap.entrySet()) {
            if (entry.getKey().endsWith("_duration")) {
                String operation = entry.getKey().replace("_duration", "");
                timings.put(operation, entry.getValue());
            }
        }
        report.put("timings", timings);
        
        // 添加计数信息
        report.put("counters", new HashMap<>(counterMap));
        
        // 计算一些性能指标
        Map<String, Object> metrics = new HashMap<>();
        
        // 计算平均处理速度
        int totalRecords = getCounter("total_records");
        long totalTime = getDuration("total_import");
        if (totalTime > 0 && totalRecords > 0) {
            metrics.put("records_per_second", (double) totalRecords * 1000 / totalTime);
            metrics.put("ms_per_record", (double) totalTime / totalRecords);
        }
        
        // 计算各阶段耗时占比
        if (totalTime > 0) {
            metrics.put("read_time_percentage", (double) getDuration("excel_read") * 100 / totalTime);
            metrics.put("process_time_percentage", (double) getDuration("data_process") * 100 / totalTime);
            metrics.put("save_time_percentage", (double) getDuration("data_save") * 100 / totalTime);
            metrics.put("tracking_time_percentage", (double) getDuration("change_tracking") * 100 / totalTime);
        }
        
        report.put("metrics", metrics);
        
        return report;
    }

    /**
     * 打印性能报告
     */
    public void printPerformanceReport() {
        Map<String, Object> report = getPerformanceReport();
        
        System.out.println("=== 导入性能报告 ===");
        
        // 打印计时信息
        @SuppressWarnings("unchecked")
        Map<String, Long> timings = (Map<String, Long>) report.get("timings");
        if (timings != null && !timings.isEmpty()) {
            System.out.println("耗时统计:");
            for (Map.Entry<String, Long> entry : timings.entrySet()) {
                System.out.println("  " + entry.getKey() + ": " + entry.getValue() + "ms");
            }
        }
        
        // 打印计数信息
        @SuppressWarnings("unchecked")
        Map<String, Integer> counters = (Map<String, Integer>) report.get("counters");
        if (counters != null && !counters.isEmpty()) {
            System.out.println("数量统计:");
            for (Map.Entry<String, Integer> entry : counters.entrySet()) {
                System.out.println("  " + entry.getKey() + ": " + entry.getValue());
            }
        }
        
        // 打印性能指标
        @SuppressWarnings("unchecked")
        Map<String, Object> metrics = (Map<String, Object>) report.get("metrics");
        if (metrics != null && !metrics.isEmpty()) {
            System.out.println("性能指标:");
            for (Map.Entry<String, Object> entry : metrics.entrySet()) {
                Object value = entry.getValue();
                if (value instanceof Double) {
                    System.out.println("  " + entry.getKey() + ": " + String.format("%.2f", value));
                } else {
                    System.out.println("  " + entry.getKey() + ": " + value);
                }
            }
        }
        
        System.out.println("==================");
    }

    /**
     * 记录导入开始
     * @param totalRecords 总记录数
     */
    public void recordImportStart(int totalRecords) {
        reset();
        startTiming("total_import");
        incrementCounter("total_records", totalRecords);
    }

    /**
     * 记录导入结束
     */
    public void recordImportEnd() {
        endTiming("total_import");
    }

    /**
     * 记录Excel读取阶段
     * @param duration 耗时
     * @param recordCount 读取的记录数
     */
    public void recordExcelRead(long duration, int recordCount) {
        timingMap.put("excel_read_duration", duration);
        incrementCounter("excel_read_records", recordCount);
    }

    /**
     * 记录数据处理阶段
     * @param duration 耗时
     * @param insertCount 新增数量
     * @param updateCount 更新数量
     */
    public void recordDataProcess(long duration, int insertCount, int updateCount) {
        timingMap.put("data_process_duration", duration);
        incrementCounter("insert_records", insertCount);
        incrementCounter("update_records", updateCount);
    }

    /**
     * 记录数据保存阶段
     * @param duration 耗时
     */
    public void recordDataSave(long duration) {
        timingMap.put("data_save_duration", duration);
    }

    /**
     * 记录变更追踪阶段
     * @param duration 耗时
     * @param historyCount 历史记录数量
     */
    public void recordChangeTracking(long duration, int historyCount) {
        timingMap.put("change_tracking_duration", duration);
        incrementCounter("history_records", historyCount);
    }
}
