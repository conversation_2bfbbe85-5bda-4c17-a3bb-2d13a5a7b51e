package com.rutong.platform.client.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @ClassName ToolsUtil * @Description TODO
 * <AUTHOR>
 * @Date 17:30 2025/8/19
 * @Version 1.0
 **/
public class ToolsUtil {
    // 判断是否是日期字段
    public static boolean isDateField(String property) {
        return property.toLowerCase().contains("time")
                || property.toLowerCase().contains("date");
    }

    // 转换日期参数
    public static Date convertToDate(Object value) {
        if (value == null) return null;
        if (value instanceof Date) return (Date) value;

        try {
            // 支持多种日期格式
            String strValue = value.toString();
            if (strValue.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(strValue);
            } else if (strValue.matches("\\d{4}-\\d{2}-\\d{2}")) {
                return new SimpleDateFormat("yyyy-MM-dd").parse(strValue);
            }
            throw new IllegalArgumentException("Unsupported date format: " + strValue);
        } catch (ParseException e) {
            throw new IllegalArgumentException("Invalid date value: " + value);
        }
    }
}
