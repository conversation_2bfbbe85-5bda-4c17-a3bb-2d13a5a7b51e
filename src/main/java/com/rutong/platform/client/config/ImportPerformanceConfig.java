package com.rutong.platform.client.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 导入性能配置类
 * 用于配置批量导入的性能参数
 */
@Configuration
@ConfigurationProperties(prefix = "import.performance")
public class ImportPerformanceConfig {

    /**
     * 批量保存的批次大小
     */
    private int batchSize = 100;

    /**
     * 历史记录批量保存的批次大小
     */
    private int historyBatchSize = 100;

    /**
     * 是否启用变更追踪
     */
    private boolean changeTrackingEnabled = true;

    /**
     * 是否异步处理变更追踪
     */
    private boolean asyncChangeTracking = true;

    /**
     * 数据库查询的IN语句最大长度
     */
    private int maxInClauseSize = 1000;

    /**
     * 是否启用性能监控日志
     */
    private boolean performanceLoggingEnabled = true;

    /**
     * 是否跳过无变更的更新操作
     */
    private boolean skipNoChangeUpdates = true;

    /**
     * 线程池核心线程数
     */
    private int corePoolSize = 2;

    /**
     * 线程池最大线程数
     */
    private int maxPoolSize = 4;

    /**
     * 线程池队列容量
     */
    private int queueCapacity = 100;

    // Getters and Setters
    public int getBatchSize() {
        return batchSize;
    }

    public void setBatchSize(int batchSize) {
        this.batchSize = batchSize;
    }

    public int getHistoryBatchSize() {
        return historyBatchSize;
    }

    public void setHistoryBatchSize(int historyBatchSize) {
        this.historyBatchSize = historyBatchSize;
    }

    public boolean isChangeTrackingEnabled() {
        return changeTrackingEnabled;
    }

    public void setChangeTrackingEnabled(boolean changeTrackingEnabled) {
        this.changeTrackingEnabled = changeTrackingEnabled;
    }

    public boolean isAsyncChangeTracking() {
        return asyncChangeTracking;
    }

    public void setAsyncChangeTracking(boolean asyncChangeTracking) {
        this.asyncChangeTracking = asyncChangeTracking;
    }

    public int getMaxInClauseSize() {
        return maxInClauseSize;
    }

    public void setMaxInClauseSize(int maxInClauseSize) {
        this.maxInClauseSize = maxInClauseSize;
    }

    public boolean isPerformanceLoggingEnabled() {
        return performanceLoggingEnabled;
    }

    public void setPerformanceLoggingEnabled(boolean performanceLoggingEnabled) {
        this.performanceLoggingEnabled = performanceLoggingEnabled;
    }

    public boolean isSkipNoChangeUpdates() {
        return skipNoChangeUpdates;
    }

    public void setSkipNoChangeUpdates(boolean skipNoChangeUpdates) {
        this.skipNoChangeUpdates = skipNoChangeUpdates;
    }

    public int getCorePoolSize() {
        return corePoolSize;
    }

    public void setCorePoolSize(int corePoolSize) {
        this.corePoolSize = corePoolSize;
    }

    public int getMaxPoolSize() {
        return maxPoolSize;
    }

    public void setMaxPoolSize(int maxPoolSize) {
        this.maxPoolSize = maxPoolSize;
    }

    public int getQueueCapacity() {
        return queueCapacity;
    }

    public void setQueueCapacity(int queueCapacity) {
        this.queueCapacity = queueCapacity;
    }
}
