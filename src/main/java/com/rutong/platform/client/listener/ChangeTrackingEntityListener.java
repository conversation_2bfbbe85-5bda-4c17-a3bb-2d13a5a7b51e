package com.rutong.platform.client.listener;

import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.annotation.ChangeTracking;
import com.rutong.platform.client.annotation.FieldTracking;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.entity.EmployeeProductConfigurationHistory;
import com.rutong.platform.client.service.EmployeeProductConfigurationHistoryService;
import org.hibernate.event.spi.PreUpdateEvent;
import org.hibernate.event.spi.PreUpdateEventListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

@Component
public class ChangeTrackingEntityListener implements PreUpdateEventListener {

    private static EmployeeProductConfigurationHistoryService historyService;

    @Autowired
    public void ChangeTrackingEventListener(EmployeeProductConfigurationHistoryService historyService) {
        this.historyService = historyService;
    }

    @Override
    public boolean onPreUpdate(PreUpdateEvent event) {
        Object entity = event.getEntity();
        if (!isTrackingEnabled(entity)) {
            return false;
        }

        if (entity instanceof EmployeeProductConfiguration) {
            EmployeeProductConfiguration current = (EmployeeProductConfiguration) entity;
            if (current.getId() == null) {
                return false; // 新实体不需要记录历史
            }

            try {
                // 直接从事件中获取原始值
                Object[] oldState = event.getOldState();
                String[] propertyNames = event.getPersister().getPropertyNames();

                // 检测变更并创建历史记录
                List<EmployeeProductConfigurationHistory> historyList = detectChanges(
                        current,
                        oldState,
                        propertyNames,
                        event.getPersister().getEntityName()
                );

                if (!historyList.isEmpty()) {
                    System.out.println("检测到 " + historyList.size() + " 个字段变更，开始异步保存历史记录");
                    historyService.asyncBatchSaveHistory(historyList);
                }
            } catch (Exception e) {
                System.err.println("处理变更检测失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
        return false; // 返回false表示继续执行操作
    }

    private boolean isTrackingEnabled(Object entity) {
        return entity.getClass().isAnnotationPresent(ChangeTracking.class);
    }

    private List<EmployeeProductConfigurationHistory> detectChanges(
            EmployeeProductConfiguration newEntity,
            Object[] oldState,
            String[] propertyNames,
            String entityName
    ) {
        List<EmployeeProductConfigurationHistory> historyList = new ArrayList<>();
        ChangeTracking tracking = newEntity.getClass().getAnnotation(ChangeTracking.class);
        if (tracking == null) {
            return historyList;
        }

        Set<String> ignoreFields = new HashSet<>(Arrays.asList(tracking.ignoreFields()));
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Date currentTime = new Date();

        for (int i = 0; i < propertyNames.length; i++) {
            String fieldName = propertyNames[i];

            if (ignoreFields.contains(fieldName)) {
                continue;
            }

            try {
                Field field = getField(newEntity.getClass(), fieldName);
                if (field == null) continue;

                FieldTracking fieldTracking = field.getAnnotation(FieldTracking.class);
                if (fieldTracking != null && fieldTracking.ignore()) {
                    continue;
                }

                Object newValue = getFieldValue(newEntity, field);
                Object oldValue = oldState[i];

                if (!Objects.equals(newValue, oldValue)) {
                    String displayName = getFieldDisplayName(field, fieldTracking);

                    System.out.println("检测到字段变更: " + displayName + " 从 [" + oldValue + "] 变更为 [" + newValue + "]");

                    EmployeeProductConfigurationHistory history = createHistoryRecord(newEntity);
                    history.setChangeType(displayName + "变更");
                    history.setOldValue(valueToString(oldValue));
                    history.setNewValue(valueToString(newValue));

                    if (loginUser != null) {
                        history.setOperator(loginUser.getUsername());
                        history.setCreateBy(loginUser.getUsername());
                        history.setOwnerDeptId(loginUser.getDeptId());
                        history.setOwnerUserId(loginUser.getUserId());
                    }
                    history.setCreateTime(currentTime);

                    historyList.add(history);
                }
            } catch (Exception e) {
                // 忽略无法访问的字段
            }
        }

        return historyList;
    }

    private Field getField(Class<?> clazz, String fieldName) {
        try {
            return clazz.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            if (clazz.getSuperclass() != null) {
                return getField(clazz.getSuperclass(), fieldName);
            }
            return null;
        }
    }

    private Object getFieldValue(Object obj, Field field) throws IllegalAccessException {
        field.setAccessible(true);
        return field.get(obj);
    }
    /**
     * 获取字段显示名称
     */
    private String getFieldDisplayName(Field field, FieldTracking fieldTracking) {
        if (fieldTracking != null && !fieldTracking.displayName().isEmpty()) {
            return fieldTracking.displayName();
        }

        // 使用默认的字段名映射
        return getDefaultDisplayName(field.getName());
    }

    /**
     * 获取默认的字段显示名称
     */
    private String getDefaultDisplayName(String fieldName) {
        Map<String, String> displayNames = new HashMap<>();
        displayNames.put("contractNumber", "合同编号");
        displayNames.put("enterpriseName", "客户名称");
        displayNames.put("employeeName", "员工姓名");
        displayNames.put("employeePhone", "员工电话");
        displayNames.put("department", "部门");
        displayNames.put("subDepartment", "分部门");
        displayNames.put("workWearConfigurationCategory", "工作服品类");
        displayNames.put("configurationCategory", "配置品类");
        displayNames.put("productModelNumber", "产品款号");
        displayNames.put("productColor", "产品颜色");
        displayNames.put("logoPosition", "LOGO位置");
        displayNames.put("productSpecification", "产品规格");
        displayNames.put("protocolConfigurationQuantity", "协议各品类配置数量");
        displayNames.put("employeeId", "员工企业工号");
        displayNames.put("rfidTagNumberWork", "工作服RFID标签编码");
        displayNames.put("rfidTagNumber", "RFID标签号");
        displayNames.put("productSerialNumber", "员工产品序列号");
        displayNames.put("status", "状态");
        displayNames.put("serviceType", "服务类型");

        return displayNames.getOrDefault(fieldName, fieldName);
    }

    /**
     * 创建历史记录基础对象
     */
    private EmployeeProductConfigurationHistory createHistoryRecord(EmployeeProductConfiguration entity) {
        EmployeeProductConfigurationHistory history = new EmployeeProductConfigurationHistory();

        // 复制所有字段到历史记录
        BeanUtils.copyProperties(entity, history);

        // 设置历史记录特有字段
        history.setParentId(entity.getId());
        history.setId(null);

        return history;
    }

    /**
     * 将值转换为字符串
     */
    private String valueToString(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Date) {
            return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((Date) value);
        }
        if (value instanceof BigDecimal) {
            return ((BigDecimal) value).toPlainString();
        }
        return value.toString();
    }
}
