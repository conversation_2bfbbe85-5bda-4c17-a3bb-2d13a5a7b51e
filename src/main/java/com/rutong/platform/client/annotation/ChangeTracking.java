package com.rutong.platform.client.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 变更追踪注解
 * 用于标记需要记录变更历史的实体类
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ChangeTracking {

    /**
     * 历史记录实体类
     */
    Class<?> historyClass();

    /**
     * 是否异步保存历史记录
     */
    boolean async() default true;

    /**
     * 忽略的字段列表
     */
    String[] ignoreFields() default {"id", "createTime", "updateTime", "createBy", "updateBy", "tableData", "ownerUserId", "ownerDeptId"};
}
