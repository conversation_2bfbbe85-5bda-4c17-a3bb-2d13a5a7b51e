package com.rutong.platform.client.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 字段追踪注解
 * 用于标记需要追踪变更的字段，并指定显示名称
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface FieldTracking {
    
    /**
     * 字段显示名称
     */
    String displayName();
    
    /**
     * 是否忽略此字段的变更追踪
     */
    boolean ignore() default false;
}
