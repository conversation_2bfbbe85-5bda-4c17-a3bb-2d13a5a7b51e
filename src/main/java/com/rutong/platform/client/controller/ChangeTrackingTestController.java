package com.rutong.platform.client.controller;

import com.rutong.framework.bean.ResultBean;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.service.ChangeTrackingService;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 变更追踪测试控制器
 */
@RestController
@RequestMapping("/api/test/changeTracking")
public class ChangeTrackingTestController {

    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;

    @Autowired
    private ChangeTrackingService changeTrackingService;

    /**
     * 测试变更追踪功能
     * @param id 实体ID
     * @param newEmployeeName 新的员工姓名
     * @param newDepartment 新的部门
     * @return
     */
    @PostMapping("/testUpdate")
    public ResultBean testUpdate(@RequestParam String id,
                                @RequestParam(required = false) String newEmployeeName,
                                @RequestParam(required = false) String newDepartment) {
        try {
            // 获取原始实体
            EmployeeProductConfiguration entity = employeeProductConfigurationService.findById(id);
            if (entity == null) {
                return ResultBean.error("记录不存在", 404);
            }

            System.out.println("=== 开始测试变更追踪 ===");
            System.out.println("原始数据 - 员工姓名: " + entity.getEmployeeName() + ", 部门: " + entity.getDepartment());

            // 修改字段进行测试
            if (newEmployeeName != null) {
                entity.setEmployeeName(newEmployeeName);
                System.out.println("修改员工姓名为: " + newEmployeeName);
            }
            if (newDepartment != null) {
                entity.setDepartment(newDepartment);
                System.out.println("修改部门为: " + newDepartment);
            }

            // 更新实体 - JPA监听器会自动处理变更追踪
            EmployeeProductConfiguration updated = employeeProductConfigurationService.update(entity);

            System.out.println("=== 测试完成 ===");
            return ResultBean.success("测试更新完成，变更历史已记录。请查看控制台日志。");

        } catch (Exception e) {
            e.printStackTrace();
            return ResultBean.error("测试失败: " + e.getMessage(), 500);
        }
    }

    /**
     * 获取实体信息用于测试
     */
    @GetMapping("/getEntity/{id}")
    public ResultBean getEntity(@PathVariable String id) {
        try {
            EmployeeProductConfiguration entity = employeeProductConfigurationService.findById(id);
            if (entity == null) {
                return ResultBean.error("记录不存在", 404);
            }
            return ResultBean.success(entity);
        } catch (Exception e) {
            return ResultBean.error("查询失败: " + e.getMessage(), 500);
        }
    }

}
