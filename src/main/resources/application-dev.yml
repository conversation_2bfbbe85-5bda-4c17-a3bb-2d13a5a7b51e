### mysql的设置
spring:
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
        jdbc:
          batch_size: 500
          batch_versioned_data: true
          order_inserts: true
          order_updates: true
    open-in-view: true
    hibernate:
      ddl-auto: update
    show-sql: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************
    username: admin
    password: db_admin@
#    url: *************************************************************************************************************************************************
#    username: root
#    password: 12345678
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      read-only: false
      connection-timeout: 60000
      idle-timeout: 10000
      validation-timeout: 3000
      max-lifetime: 60000
      login-timeout: 5
      maximum-pool-size: 60
      minimum-idle: 10